package dto

// MenuResponse 菜单详情响应
type MenuResponse struct {
	ID        uint64         `json:"id"`
	ParentID  uint64         `json:"parent_id"`
	Order     uint64         `json:"order"`
	Title     string         `json:"title"`
	Icon      string         `json:"icon"`
	URI       string         `json:"uri"`
	Children  []MenuResponse `json:"children,omitempty"`
}

// MenuDetailResponse 菜单详细信息响应
type MenuDetailResponse struct {
	ID       uint64   `json:"id"`
	ParentID uint64   `json:"parent_id"`
	Order    uint64   `json:"order"`
	Title    string   `json:"title"`
	Icon     string   `json:"icon"`
	URI      string   `json:"uri"`
	RoleIDs  []uint64 `json:"role_ids,omitempty"`
}

// MenuListItem 菜单列表项
type MenuListItem struct {
	ID        uint64         `json:"id"`
	ParentID  uint64         `json:"parent_id"`
	Order     uint64         `json:"order"`
	Title     string         `json:"title"`
	Icon      string         `json:"icon"`
	URI       string         `json:"uri"`
	Children  []MenuListItem `json:"children,omitempty"`
}

// MenuCreateDTO 创建菜单请求
type MenuCreateDTO struct {
	ParentID      uint64   `json:"parent_id"`
	Order     	  uint64   `json:"order"`
	Title         string   `json:"title" validate:"required"`
	Icon          string   `json:"icon"`
	URI           string   `json:"uri"`
	RoleIDs       []uint64 `json:"role_ids,omitempty"`
	PermissionIDs []uint64 `json:"permission_ids,omitempty"`
}

// MenuUpdateDTO 更新菜单请求
type MenuUpdateDTO struct {
	ParentID      uint64   `json:"parent_id"`
	Order         uint64   `json:"order"`
	Title         string   `json:"title" validate:"required"`
	Icon          string   `json:"icon"`
	URI           string   `json:"uri"`
	RoleIDs       []uint64 `json:"role_ids,omitempty"`
}

// MenuQuery 菜单查询参数
type MenuQuery struct {
	Title    string `query:"title"`
	Page     int    `query:"page"`
	PageSize int    `query:"page_size"`
} 