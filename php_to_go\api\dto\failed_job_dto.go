package dto

import "time"

// FailedJobResponse 失败任务响应
type FailedJobResponse struct {
	ID         uint64    `json:"id"`
	Connection string    `json:"connection"`
	Queue      string    `json:"queue"`
	Payload    string    `json:"payload"`
	Exception  string    `json:"exception"`
	FailedAt   time.Time `json:"failed_at"`
}

// FailedJobListItem 失败任务列表项
type FailedJobListItem struct {
	ID         uint64    `json:"id"`
	Connection string    `json:"connection"`
	Queue      string    `json:"queue"`
	Exception  string    `json:"exception,omitempty"`
	FailedAt   time.Time `json:"failed_at"`
}

// FailedJobCreateRequest 创建失败任务请求
type FailedJobCreateRequest struct {
	Connection string `json:"connection" validate:"required"`
	Queue      string `json:"queue" validate:"required"`
	Payload    string `json:"payload" validate:"required"`
	Exception  string `json:"exception" validate:"required"`
}

// FailedJobUpdateRequest 更新失败任务请求
type FailedJobUpdateRequest struct {
	Connection *string `json:"connection,omitempty"`
	Queue      *string `json:"queue,omitempty"`
	Payload    *string `json:"payload,omitempty"`
	Exception  *string `json:"exception,omitempty"`
}

// FailedJobQuery 失败任务查询参数
type FailedJobQuery struct {
	Queue       string     `query:"queue"`
	Connection  string     `query:"connection"`
	StartTime   *string    `query:"start_time,omitempty"`
	EndTime     *string    `query:"end_time,omitempty"`
	Page        int        `query:"page"`
	PageSize    int        `query:"page_size"`
} 