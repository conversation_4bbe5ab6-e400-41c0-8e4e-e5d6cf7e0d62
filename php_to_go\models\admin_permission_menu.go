package models

import (
	"time"
)

// AdminPermissionMenu 权限-菜单关联表
type AdminPermissionMenu struct {
	PermissionID uint64    `gorm:"primaryKey;type:bigint;not null"`
	MenuID       uint64    `gorm:"primaryKey;type:bigint;not null"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

func (AdminPermissionMenu) TableName() string {
	return "admin_permission_menu"
} 