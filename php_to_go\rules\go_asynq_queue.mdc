---
description: 队列，Asynq
globs: 
alwaysApply: false
---
# Asynq任务队列集成指南

本指南提供了在项目中集成和使用Asynq任务队列库的方法和最佳实践。

## 安装与配置

首先，需要安装Asynq库：

```bash
go get -u github.com/hibiken/asynq
```

Asynq使用Redis作为后端存储，需要确保项目中已配置Redis连接。

## 项目结构

建议在项目中创建如下目录结构管理任务队列相关代码：

```
utils/
  queue/
    tasks/          # 存放任务定义
    processors/     # 存放任务处理器
    client.go       # 队列客户端封装
    server.go       # 队列服务器封装
    monitor.go      # 可选：监控相关
```

## 任务类型定义

任务类型应当集中定义，建议在`utils/queue/tasks/tasks.go`中定义：

```go
package tasks

// 任务类型常量
const (
    // 研究相关任务
    TypeResearchJobProcess = "research:job:process"
    TypeResearchTaskProcess = "research:task:process"
    
    // 可以添加其他任务类型...
)

// 研究任务负载
type ResearchJobPayload struct {
    JobID uint64 `json:"job_id"`
    // 其他必要字段
}

// 研究动作负载
type ResearchTaskPayload struct {
    TaskID uint64 `json:"task_id"`
    // 其他必要字段
}

// 创建研究任务
func NewResearchJobTask(jobID uint64) (*asynq.Task, error) {
    payload, err := json.Marshal(ResearchJobPayload{JobID: jobID})
    if err != nil {
        return nil, err
    }
    return asynq.NewTask(TypeResearchJobProcess, payload), nil
}

// 创建研究动作任务
func NewResearchTaskTask(taskID uint64) (*asynq.Task, error) {
    payload, err := json.Marshal(ResearchTaskPayload{TaskID: taskID})
    if err != nil {
        return nil, err
    }
    return asynq.NewTask(TypeResearchTaskProcess, payload), nil
}
```

## 任务处理器实现

任务处理器应当实现在`utils/queue/processors`目录下，例如：

```go
package processors

import (
    "context"
    "encoding/json"
    "fmt"
    "go-fiber-api/models"
    "go-fiber-api/database"
    "go-fiber-api/utils/queue/tasks"
    
    "github.com/hibiken/asynq"
)

// ResearchJobProcessor 处理研究任务
func ResearchJobProcessor(ctx context.Context, t *asynq.Task) error {
    var payload tasks.ResearchJobPayload
    if err := json.Unmarshal(t.Payload(), &payload); err != nil {
        return fmt.Errorf("错误的任务负载: %v", err)
    }
    
    // 从数据库加载任务
    var job models.ResearchJob
    if err := database.DB.First(&job, payload.JobID).Error; err != nil {
        return fmt.Errorf("任务不存在: %v", err)
    }
    
    // 更新任务状态为进行中
    if err := database.DB.Model(&job).Update("state", 20).Error; err != nil {
        return err
    }
    
    // 执行任务逻辑
    // ...处理任务...
    
    // 完成后更新状态
    if err := database.DB.Model(&job).Update("state", 30).Error; err != nil {
        return err
    }
    
    return nil
}

// ResearchTaskProcessor 处理研究动作
func ResearchTaskProcessor(ctx context.Context, t *asynq.Task) error {
    var payload tasks.ResearchTaskPayload
    if err := json.Unmarshal(t.Payload(), &payload); err != nil {
        return fmt.Errorf("错误的任务负载: %v", err)
    }
    
    // 实现类似的处理逻辑...
    
    return nil
}
```

## 队列客户端封装

为了方便在API处理器中使用，可以创建客户端封装：

```go
// utils/queue/client.go
package queue

import (
    "github.com/hibiken/asynq"
)

// Client 队列客户端
type Client struct {
    client *asynq.Client
}

// NewClient 创建新的队列客户端
func NewClient(redisAddr string) *Client {
    return &Client{
        client: asynq.NewClient(asynq.RedisClientOpt{Addr: redisAddr}),
    }
}

// Enqueue 将任务入队
func (c *Client) Enqueue(task *asynq.Task, opts ...asynq.Option) (*asynq.TaskInfo, error) {
    return c.client.Enqueue(task, opts...)
}

// Close 关闭客户端
func (c *Client) Close() error {
    return c.client.Close()
}
```

## 队列服务器封装

为工作进程创建服务器封装：

```go
// utils/queue/server.go
package queue

import (
    "go-fiber-api/utils/queue/processors"
    "go-fiber-api/utils/queue/tasks"
    
    "github.com/hibiken/asynq"
)

// NewServer 创建新的队列服务器
func NewServer(redisAddr string, concurrency int) *asynq.Server {
    return asynq.NewServer(
        asynq.RedisClientOpt{Addr: redisAddr},
        asynq.Config{
            Concurrency: concurrency,
            Queues: map[string]int{
                "critical": 10, // 高优先级队列
                "default":  5,  // 默认队列
                "low":      1,  // 低优先级队列
            },
        },
    )
}

// RegisterHandlers 注册任务处理器
func RegisterHandlers(mux *asynq.ServeMux) {
    // 注册研究任务处理器
    mux.HandleFunc(tasks.TypeResearchJobProcess, processors.ResearchJobProcessor)
    mux.HandleFunc(tasks.TypeResearchTaskProcess, processors.ResearchTaskProcessor)
    // 可以添加更多处理器...
}
```

## 如何在API处理器中使用

示例：如何在API处理器中创建和入队任务：

```go
import (
    "go-fiber-api/utils/queue"
    "go-fiber-api/utils/queue/tasks"
    "time"
    
    "github.com/gofiber/fiber/v2"
    "github.com/hibiken/asynq"
)

func CreateResearchJob(c *fiber.Ctx) error {
    // 创建数据库记录...
    
    // 使用队列客户端创建异步任务
    client := queue.NewClient("localhost:6379")
    defer client.Close()
    
    task, err := tasks.NewResearchJobTask(job.ID)
    if err != nil {
        return err
    }
    
    // 立即执行的任务
    _, err = client.Enqueue(task)
    if err != nil {
        return err
    }
    
    // 或者延迟执行的任务
    _, err = client.Enqueue(task, asynq.ProcessIn(5*time.Minute))
    if err != nil {
        return err
    }
    
    return c.JSON(fiber.Map{"status": "success"})
}
```

## 如何创建工作进程

创建一个独立的工作进程来处理队列中的任务：

```go
// cmd/worker/main.go
package main

import (
    "log"
    "go-fiber-api/utils/queue"
    
    "github.com/hibiken/asynq"
)

func main() {
    // 创建服务器
    server := queue.NewServer("localhost:6379", 10)
    
    // 创建多路复用器并注册处理器
    mux := asynq.NewServeMux()
    queue.RegisterHandlers(mux)
    
    // 启动工作进程
    if err := server.Run(mux); err != nil {
        log.Fatalf("无法启动队列工作进程: %v", err)
    }
}
```

## 异常处理与重试

Asynq提供了强大的重试和错误处理机制：

```go
// 设置最大重试次数
task, _ := tasks.NewResearchJobTask(jobID)
_, err = client.Enqueue(task, asynq.MaxRetry(3))

// 在处理器中处理重试
func ResearchJobProcessor(ctx context.Context, t *asynq.Task) error {
    // ...
    if someError != nil {
        // 返回错误将自动触发重试
        return err
    }
    // 如果不想重试，可以包装错误
    if fatalError != nil {
        return fmt.Errorf("%v: %w", fatalError, asynq.SkipRetry)
    }
    // ...
}
```

## 监控任务队列

Asynq提供了Web UI和CLI工具来监控队列状态：

### Web UI (Asynqmon)

```bash
# 安装
go get -u github.com/hibiken/asynqmon

# 使用
asynqmon --redis-addr=localhost:6379
```

访问 http://localhost:8080 查看Web界面。

### CLI 工具

```bash
# 安装
go get -u github.com/hibiken/asynq/tools/asynq

# 使用示例
asynq stats --redis-addr=localhost:6379
asynq ls --redis-addr=localhost:6379 --queue=default
```

## 与现有数据模型的集成

项目中的以下模型可以与Asynq无缝集成：

1. `models.Job` - 可用于保存任务基本信息
2. `models.FailedJob` - 可用于记录任务失败信息
3. `models.ResearchJob` - 研究任务模型
4. `models.ResearchTask` - 研究动作模型

当任务失败时，可以选择：
1. 让Asynq自动重试
2. 将失败信息写入`models.FailedJob`表
3. 更新对应业务表的状态

## 总结

通过使用Asynq，我们可以实现高性能、可靠的任务队列系统，满足项目中各种异步处理需求。Asynq的简单易用和强大功能使其成为Go语言项目中处理异步任务的理想选择。

