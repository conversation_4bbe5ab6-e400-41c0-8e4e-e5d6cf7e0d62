<template>
  <div class="friendly-edit">
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? '编辑友情链接' : '创建友情链接' }}</h1>
      <div class="page-actions">
        <el-button @click="$router.push('/friendly')">返回列表</el-button>
      </div>
    </div>

    <el-card shadow="hover" class="edit-card">
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" v-loading="loading">
        <el-form-item label="链接名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入链接名称" maxlength="50" show-word-limit></el-input>
        </el-form-item>
        
        <el-form-item label="链接地址" prop="href">
          <el-input v-model="form.href" placeholder="请输入链接地址"></el-input>
        </el-form-item>
        
        <el-form-item label="链接类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择链接类型">
            <el-option label="友情链接" value="1"></el-option>
            <el-option label="合作伙伴" value="2"></el-option>
            <el-option label="推荐网站" value="3"></el-option>
            <el-option label="其他" value="4"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="缩略图" prop="thumb">
          <el-input v-model="form.thumb" placeholder="请输入缩略图URL地址"></el-input>
          <div v-if="form.thumb" class="logo-preview">
            <el-image 
              :src="form.thumb" 
              style="width: 60px; height: 60px; margin-top: 10px;" 
              fit="contain">
            </el-image>
          </div>
        </el-form-item>
        
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="9999" placeholder="数字越小排序越靠前"></el-input-number>
        </el-form-item>
        
        <el-form-item label="状态" prop="state">
          <el-radio-group v-model="form.state">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">保存</el-button>
          <el-button @click="$router.push('/friendly')">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { createFriendly, getFriendlyDetail, updateFriendly } from '@/api/friendly'

export default {
  name: 'FriendlyEdit',
  data() {
    return {
      isEdit: false,
      loading: false,
      submitLoading: false,
      form: {
        name: '',
        href: '',
        type: '1',
        thumb: '',
        sort: 0,
        state: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入链接名称', trigger: 'blur' },
          { max: 50, message: '链接名称不能超过50个字符', trigger: 'blur' }
        ],
        href: [
          { required: true, message: '请输入链接地址', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择链接类型', trigger: 'change' }
        ],
        thumb: [
          { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ],
        state: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    // 判断是否为编辑模式
    const id = this.$route.params.id
    if (id) {
      this.isEdit = true
      this.fetchFriendlyDetail(id)
    }
  },
  methods: {
    // 获取友情链接详情
    fetchFriendlyDetail(id) {
      this.loading = true
      getFriendlyDetail(id)
        .then(data => {
          this.form = {
            name: data.name,
            href: data.href,
            type: data.type || '1',
            thumb: data.thumb || '',
            sort: data.sort || 0,
            state: data.state
          }
        })
        .catch(error => {
          this.$message.error(`获取友情链接详情失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (!valid) return
        
        this.submitLoading = true
        const request = this.isEdit
          ? updateFriendly(this.$route.params.id, this.form)
          : createFriendly(this.form)
        
        request
          .then(() => {
            this.$message.success(this.isEdit ? '更新成功' : '添加成功')
            this.$router.push('/friendly')
          })
          .catch(error => {
            this.$message.error(`${this.isEdit ? '更新' : '添加'}失败: ${error.message}`)
          })
          .finally(() => {
            this.submitLoading = false
          })
      })
    }
  }
}
</script>

<style scoped>
.friendly-edit {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.edit-card {
  margin-bottom: 20px;
}

.logo-preview {
  margin-top: 10px;
  display: flex;
  align-items: center;
}
</style> 