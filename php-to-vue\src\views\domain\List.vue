<template>
  <div class="domain-list">
    <div class="page-header">
      <h1 class="page-title">域名管理</h1>
      <el-button type="primary" @click="$router.push('/domain/create')">
        <i class="el-icon-plus"></i> 添加域名
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="域名">
          <el-input v-model="filterForm.domain" placeholder="请输入域名" clearable></el-input>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="filterForm.type" placeholder="全部" clearable>
            <el-option label="过滤域名" :value="10"></el-option>
            <el-option label="切换域名" :value="20"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filterForm.state" placeholder="全部" clearable>
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchDomains">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 域名列表 -->
    <el-card shadow="hover" class="domain-table-container">
      <div slot="header">
        <span>域名列表</span>
      </div>
      <el-table
        :data="domains"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="loading">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="domain" label="域名" ></el-table-column>
        <el-table-column prop="type" label="类型">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type === 10 ? 'success' : 'warning'">
              {{ scope.row.type === 10 ? '过滤域名' : '切换域名' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="state" label="状态">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.state"
              @change="(val) => toggleStatus(scope.row, val)"
              :active-value="1"
              :inactive-value="0">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="warning" @click="editDomain(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="deleteDomain(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getDomainList, updateDomain, deleteDomain } from '@/api/domain'

export default {
  name: 'DomainList',
  data() {
    return {
      loading: false,
      // 筛选表单
      filterForm: {
        domain: '',
        type: '',
        state: ''
      },
      // 域名列表数据
      domains: [],
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 选中的行
      multipleSelection: []
    }
  },
  methods: {
    // 搜索域名
    searchDomains() {
      this.pagination.currentPage = 1
      this.fetchDomains()
    },
    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        domain: '',
        type: '',
        state: ''
      }
      this.searchDomains()
    },
    // 切换状态
    toggleStatus(row, val) {
      const newState = val ? 1 : 0
      updateDomain(row.id, { state: newState })
        .then(() => {
          row.state = newState
          this.$message.success(`域名 ${row.domain} 已${newState === 1 ? '启用' : '禁用'}`)
        })
        .catch(error => {
          this.$message.error(`状态更新失败: ${error.message}`)
          // 回滚UI状态
          row.state = row.state === 1 ? 0 : 1
        })
    },
    // 选择行变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.fetchDomains()
    },
    // 页码变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.fetchDomains()
    },
    // 编辑域名
    editDomain(row) {
      this.$router.push(`/domain/create?id=${row.id}`)
    },
    // 删除域名
    deleteDomain(row) {
      this.$confirm(`确认删除域名 "${row.domain}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 执行删除操作
        this.loading = true
        deleteDomain(row.id)
          .then(() => {
            this.$message.success('删除成功')
            this.fetchDomains()
          })
          .catch(error => {
            this.$message.error(`删除失败: ${error.message}`)
          })
          .finally(() => {
            this.loading = false
          })
      }).catch(() => {
        // 取消删除
      })
    },
    // 获取域名列表
    fetchDomains() {
      this.loading = true
      
      const params = {
        page: this.pagination.currentPage,
        page_size: this.pagination.pageSize,
        ...this.filterForm
      }
      
      // 移除空值
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })
      
      getDomainList(params)
        .then(response => {
          this.domains = response.items || []
          this.pagination.total = response.total || 0
        })
        .catch(error => {
          this.$message.error(`获取域名列表失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    }
  },
  created() {
    this.fetchDomains()
  }
}
</script>

<style scoped>
.domain-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.domain-table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-dropdown-link {
  color: #409EFF;
}
</style> 