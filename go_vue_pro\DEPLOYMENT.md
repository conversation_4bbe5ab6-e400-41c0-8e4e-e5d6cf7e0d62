# Go Vue Pro 生产环境部署指南

## 概述

本指南将帮助您将 Go Vue Pro 项目部署到远程服务器上，使用 Docker 容器化部署。

## 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 2GB RAM
- **存储**: 20GB 可用空间
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+

### 推荐配置
- **CPU**: 4核心
- **内存**: 4GB RAM
- **存储**: 50GB SSD
- **网络**: 稳定的网络连接

## 部署步骤

### 1. 服务器准备

#### 1.1 连接到服务器
```bash
ssh username@your-server-ip
```

#### 1.2 更新系统
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

#### 1.3 安装必要工具
```bash
# Ubuntu/Debian
sudo apt install -y curl wget git nano

# CentOS/RHEL
sudo yum install -y curl wget git nano
```

### 2. 安装 Docker

#### 2.1 安装 Docker Engine
```bash
# 使用官方安装脚本
curl -fsSL https://get.docker.com | sh

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到 docker 组
sudo usermod -aG docker $USER

# 重新登录或运行
newgrp docker
```

#### 2.2 安装 Docker Compose
```bash
# 下载最新版本
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker-compose --version
```

### 3. 上传项目文件

#### 3.1 使用 Git 克隆（推荐）
```bash
git clone https://your-git-repository.git go-vue-pro
cd go-vue-pro
```

#### 3.2 或使用 SCP 上传
```bash
# 在本地机器上运行
scp -r /path/to/go-vue-pro username@your-server-ip:/home/<USER>/
```

### 4. 环境配置

#### 4.1 运行环境检查
```bash
chmod +x server-check.sh
./server-check.sh
```

#### 4.2 配置生产环境变量
```bash
cd docker
cp .env.prod.example .env.prod
nano .env.prod
```

**重要配置项：**
```env
# 数据库密码（必须修改）
DB_PASSWORD=your_secure_password_here

# JWT密钥（必须修改）
JWT_SECRET=your_very_secure_jwt_secret_key_here

# Redis密码（建议设置）
REDIS_PASSWORD=your_redis_password_here

# 域名设置
WEB_BASE_URL=https://your-domain.com

# 邮箱配置
SMTP_HOST=smtp.your-domain.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
```

### 5. 部署应用

#### 5.1 运行部署脚本
```bash
chmod +x deploy-prod.sh
./deploy-prod.sh
```

#### 5.2 手动部署（可选）
```bash
cd docker
docker-compose -f docker-compose.prod.yml up -d --build
```

### 6. 验证部署

#### 6.1 检查服务状态
```bash
cd docker
docker-compose -f docker-compose.prod.yml ps
```

#### 6.2 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs api
docker-compose -f docker-compose.prod.yml logs nginx
```

#### 6.3 健康检查
```bash
# 检查应用健康状态
curl http://your-server-ip/health

# 检查API接口
curl http://your-server-ip/api/

# 访问前端页面
curl http://your-server-ip/
```

## 域名和SSL配置

### 1. 域名解析
将您的域名A记录指向服务器IP地址。

### 2. SSL证书配置

#### 2.1 使用 Let's Encrypt（免费）
```bash
# 安装 Certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem docker/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem docker/ssl/key.pem
sudo chown $USER:$USER docker/ssl/*
```

#### 2.2 启用HTTPS
编辑 `docker/nginx.prod.conf`，取消注释HTTPS配置部分。

## 维护和监控

### 1. 常用管理命令
```bash
cd docker

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 停止服务
docker-compose -f docker-compose.prod.yml down

# 更新服务
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

### 2. 数据备份
```bash
# 备份MySQL数据
docker-compose -f docker-compose.prod.yml exec mysql mysqldump -u root -p go_fiber > backup_$(date +%Y%m%d).sql

# 备份Redis数据
docker-compose -f docker-compose.prod.yml exec redis redis-cli BGSAVE
```

### 3. 日志管理
```bash
# 清理日志
docker system prune -f

# 限制日志大小（在docker-compose.yml中添加）
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## 故障排除

### 1. 常见问题

#### 服务无法启动
```bash
# 查看详细日志
docker-compose -f docker-compose.prod.yml logs service-name

# 检查端口占用
netstat -tuln | grep :80
```

#### 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose -f docker-compose.prod.yml exec mysql mysql -u root -p

# 重置数据库密码
docker-compose -f docker-compose.prod.yml exec mysql mysql -u root -p -e "ALTER USER 'root'@'%' IDENTIFIED BY 'new_password';"
```

#### 内存不足
```bash
# 检查内存使用
free -h
docker stats

# 添加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 2. 性能优化

#### 2.1 数据库优化
- 调整 `docker/mysql.conf` 中的缓冲池大小
- 定期清理慢查询日志
- 优化数据库索引

#### 2.2 Redis优化
- 设置合适的内存限制
- 配置持久化策略
- 监控内存使用情况

#### 2.3 Nginx优化
- 启用Gzip压缩
- 配置静态文件缓存
- 调整worker进程数

## 安全建议

1. **防火墙配置**
   ```bash
   # 只开放必要端口
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw allow 443   # HTTPS
   sudo ufw enable
   ```

2. **定期更新**
   - 定期更新系统包
   - 更新Docker镜像
   - 更新SSL证书

3. **监控和日志**
   - 设置日志轮转
   - 监控系统资源
   - 设置告警机制

4. **数据安全**
   - 定期备份数据
   - 使用强密码
   - 限制数据库访问

## 支持

如果在部署过程中遇到问题，请：

1. 检查日志文件
2. 参考故障排除部分
3. 查看项目文档
4. 提交Issue到项目仓库

---

**部署成功后，您的应用将在 `http://your-server-ip` 或 `https://your-domain.com` 上运行！**
