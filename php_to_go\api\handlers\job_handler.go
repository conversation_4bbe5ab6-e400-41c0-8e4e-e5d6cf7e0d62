package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetJobs 获取任务列表
// @Summary 获取任务列表
// @Description 获取任务列表，支持分页和筛选
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param queue query string false "队列名称(模糊搜索)"
// @Param attempts query int false "尝试次数"
// @Param is_reserved query bool false "是否已保留"
// @Param page query int false "页码(默认: 1)"
// @Param page_size query int false "每页数量(默认: 10)"
// @Success 200 {object} dto.StandardResponse{data=[]dto.JobListItem}
// @Router /admin/job/index [get]
func GetJobs(c *fiber.Ctx) error {
	// 解析查询参数
	query := new(dto.JobQuery)
	if err := c.QueryParser(query); err != nil {
		return utils.BadRequest(c, "无效的查询参数", err)
	}

	// 设置默认分页参数
	page := 1
	if query.Page != nil && *query.Page > 0 {
		page = *query.Page
	}

	pageSize := 10
	if query.PageSize != nil && *query.PageSize > 0 {
		pageSize = *query.PageSize
	}

	// 构建查询
	db := database.DB.Model(&models.Job{})

	// 应用筛选条件
	if query.Queue != nil && *query.Queue != "" {
		db = db.Where("queue LIKE ?", "%"+*query.Queue+"%")
	}

	if query.Attempts != nil && *query.Attempts > 0 {
		db = db.Where("attempts = ?", *query.Attempts)
	}

	if query.IsReserved != nil && *query.IsReserved {
		db = db.Where("reserved_at IS NOT NULL")
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return utils.ServerError(c, "获取任务总数失败", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var jobs []models.Job
	if err := db.Order("id DESC").Offset(offset).Limit(pageSize).Find(&jobs).Error; err != nil {
		return utils.ServerError(c, "获取任务列表失败", err)
	}

	// 转换为DTO
	var jobDTOs []dto.JobListItem
	for _, job := range jobs {
		jobDTOs = append(jobDTOs, dto.JobListItem{
			ID:          job.ID,
			Queue:       job.Queue,
			Attempts:    job.Attempts,
			ReservedAt:  job.ReservedAt,
			AvailableAt: job.AvailableAt,
			CreatedAt:   job.CreatedAt,
		})
	}

	// 计算总页数
	totalPages := int(total) / pageSize
	if int(total)%pageSize > 0 {
		totalPages++
	}

	// 返回分页响应
	return utils.SuccessPaginated(c, "获取任务列表成功", jobDTOs, total, page, pageSize)
}

// GetJob 获取单个任务详情
// @Summary 获取任务详情
// @Description 根据ID获取任务详情
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "任务ID"
// @Success 200 {object} dto.StandardResponse{data=dto.JobResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "任务不存在"
// @Router /admin/job/index/{id} [get]
func GetJob(c *fiber.Ctx) error {
	// 获取任务ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的任务ID", err)
	}

	// 查询任务
	var job models.Job
	if err := database.DB.First(&job, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "任务不存在")
		}
		return utils.ServerError(c, "获取任务失败", err)
	}

	// 转换为DTO
	response := dto.JobResponse{
		ID:          job.ID,
		Queue:       job.Queue,
		Payload:     job.Payload,
		Attempts:    job.Attempts,
		ReservedAt:  job.ReservedAt,
		AvailableAt: job.AvailableAt,
		CreatedAt:   job.CreatedAt,
	}

	return utils.Success(c, "获取任务成功", response)
}

// CreateJob 创建任务
// @Summary 创建任务
// @Description 创建新的任务
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param job body dto.JobCreateRequest true "任务信息"
// @Success 200 {object} dto.StandardResponse{data=dto.JobResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/job/index [post]
func CreateJob(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.JobCreateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 创建新任务
	job := models.Job{
		Queue:       req.Queue,
		Payload:     req.Payload,
		Attempts:    req.Attempts,
		ReservedAt:  nil,
		AvailableAt: req.AvailableAt,
		CreatedAt:   uint(time.Now().Unix()),
	}

	if err := database.DB.Create(&job).Error; err != nil {
		return utils.ServerError(c, "创建任务失败", err)
	}

	// 转换为DTO
	response := dto.JobResponse{
		ID:          job.ID,
		Queue:       job.Queue,
		Payload:     job.Payload,
		Attempts:    job.Attempts,
		ReservedAt:  job.ReservedAt,
		AvailableAt: job.AvailableAt,
		CreatedAt:   job.CreatedAt,
	}

	return utils.Success(c, "创建任务成功", response)
}

// UpdateJob 更新任务
// @Summary 更新任务
// @Description 更新现有任务
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "任务ID"
// @Param job body dto.JobUpdateRequest true "任务更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.JobResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "任务不存在"
// @Router /admin/job/index/{id} [put]
func UpdateJob(c *fiber.Ctx) error {
	// 获取任务ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的任务ID", err)
	}

	// 查询任务
	var job models.Job
	if err := database.DB.First(&job, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "任务不存在")
		}
		return utils.ServerError(c, "获取任务失败", err)
	}

	// 解析请求体
	var req dto.JobUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 准备更新数据
	updates := make(map[string]interface{})

	if req.Queue != nil {
		updates["queue"] = *req.Queue
	}

	if req.Payload != nil {
		updates["payload"] = *req.Payload
	}

	if req.Attempts != nil {
		updates["attempts"] = *req.Attempts
	}

	if req.ReservedAt != nil {
		updates["reserved_at"] = *req.ReservedAt
	}

	if req.AvailableAt != nil {
		updates["available_at"] = *req.AvailableAt
	}

	// 更新任务
	if err := database.DB.Model(&job).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新任务失败", err)
	}

	// 重新获取更新后的任务
	if err := database.DB.First(&job, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的任务失败", err)
	}

	// 转换为DTO
	response := dto.JobResponse{
		ID:          job.ID,
		Queue:       job.Queue,
		Payload:     job.Payload,
		Attempts:    job.Attempts,
		ReservedAt:  job.ReservedAt,
		AvailableAt: job.AvailableAt,
		CreatedAt:   job.CreatedAt,
	}

	return utils.Success(c, "更新任务成功", response)
}

// DeleteJob 删除任务
// @Summary 删除任务
// @Description 删除现有任务
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "任务ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "任务不存在"
// @Router /admin/job/index/{id} [delete]
func DeleteJob(c *fiber.Ctx) error {
	// 获取任务ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的任务ID", err)
	}

	// 查询任务
	var job models.Job
	if err := database.DB.First(&job, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "任务不存在")
		}
		return utils.ServerError(c, "获取任务失败", err)
	}

	// 删除任务
	if err := database.DB.Delete(&job).Error; err != nil {
		return utils.ServerError(c, "删除任务失败", err)
	}

	return utils.Success(c, "删除任务成功", nil)
}

// ClearAllJobs 清空所有任务
// @Summary 清空所有任务
// @Description 清空所有任务
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse "清空成功"
// @Router /admin/job/clear-all [delete]
func ClearAllJobs(c *fiber.Ctx) error {
	// 执行清空操作
	if err := database.DB.Exec("TRUNCATE TABLE jobs").Error; err != nil {
		return utils.ServerError(c, "清空任务失败", err)
	}

	return utils.Success(c, "清空任务成功", nil)
}