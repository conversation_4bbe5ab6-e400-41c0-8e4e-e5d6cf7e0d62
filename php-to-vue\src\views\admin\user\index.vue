<template>
  <div class="admin-user-container">
    <div class="filter-container">
      <el-card>
        <el-form :inline="true" :model="listQuery" class="filter-form">
          <el-form-item label="用户名">
            <el-input
              v-model="listQuery.username"
              placeholder="请输入用户名"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input
              v-model="listQuery.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input
              v-model="listQuery.email"
              placeholder="请输入邮箱"
              clearable
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="listQuery.state"
              placeholder="请选择状态"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" @click="resetFilter"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <div class="action-container">
      <el-button type="primary" icon="el-icon-plus" @click="handleCreate"
        >新建用户</el-button
      >
      <el-button type="danger" icon="el-icon-delete" @click="handleBatchDelete" :disabled="multipleSelection.length === 0">批量删除</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="ID" align="center" width="80">
        <template slot-scope="scope">{{ scope.row.id }}</template>
      </el-table-column>
      <el-table-column label="用户名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="邮箱" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.email || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.state === 1 ? 'success' : 'danger'">
            {{ scope.row.state === 1 ? "启用" : "禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后登录时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.lasted_at || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后登录IP" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.lasted_ipaddress || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.page_size"
      @pagination="getList"
    />

    <!-- 用户表单对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="80px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="temp.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item
          :label="dialogStatus === 'create' ? '密码' : '新密码'"
          prop="password"
        >
          <el-input
            v-model="temp.password"
            placeholder="请输入密码"
            type="password"
            :required="dialogStatus === 'create'"
          />
          <span v-if="dialogStatus === 'update'" class="tips"
            >如不修改密码请留空</span
          >
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="temp.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="temp.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <el-input v-model="temp.avatar" placeholder="请输入头像URL" />
          <el-button type="text" @click="showAvatarUpload">上传头像</el-button>
          <div v-if="avatarUrl" class="avatar-preview">
            <img :src="avatarUrl" alt="头像预览" style="max-width: 100px; max-height: 100px;">
          </div>
        </el-form-item>
        <el-form-item label="推荐人" prop="referee">
          <el-input v-model="temp.referee" placeholder="请输入推荐人" />
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-select v-model="temp.state" placeholder="请选择状态">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="通知" prop="notify">
          <el-select v-model="temp.notify" placeholder="请选择通知设置">
            <el-option label="开启" :value="1" />
            <el-option label="关闭" :value="0" />
          </el-select>
        </el-form-item>
        
        <!-- 权限设置 -->
        <!-- <el-form-item label="权限" prop="permissions">
          <el-button type="text" @click="showPermissionDialog">设置权限</el-button>
          <div v-if="selectedPermissions.length" class="selected-permissions">
            已选择 {{ selectedPermissions.length }} 个权限
          </div>
        </el-form-item> -->

        <!-- 角色设置 -->
        <el-form-item label="角色" prop="roles">
          <el-button type="text" @click="showRoleDialog">设置角色</el-button>
          <div v-if="selectedRoles.length" class="selected-roles">
            已选择 {{ selectedRoles.length }} 个角色
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="dialogStatus === 'create' ? createData() : updateData()"
          >确认</el-button
        >
      </div>
    </el-dialog>

    <!-- 权限选择对话框 -->
    <el-dialog
      title="选择权限"
      :visible.sync="permissionDialogVisible"
      width="60%"
      append-to-body
    >
      <div v-loading="permissionLoading">
        <el-input
          v-model="permissionSearch"
          placeholder="搜索权限"
          clearable
          style="margin-bottom: 15px"
        />
        <el-tree
          ref="permissionTree"
          :data="permissionTreeData"
          :props="{
            children: 'children',
            label: 'name'
          }"
          node-key="id"
          show-checkbox
          default-expand-all
          :filter-node-method="filterPermissionNode"
        >
          <span slot-scope="{ data }" class="custom-tree-node">
            <span>{{ data.name }}</span>
            <span class="permission-slug">{{ data.slug }}</span>
            <span v-if="data.http_path" class="permission-http">
              {{ data.http_method || '*' }} {{ data.http_path }}
            </span>
          </span>
        </el-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPermissions">确认</el-button>
      </div>
    </el-dialog>

    <!-- 角色选择对话框 -->
    <el-dialog
      title="选择角色"
      :visible.sync="roleDialogVisible"
      width="60%"
      append-to-body
    >
      <div v-loading="roleLoading">
        <el-input
          v-model="roleSearch"
          placeholder="搜索角色"
          clearable
          style="margin-bottom: 15px"
        />
        <el-table
          ref="roleTable"
          :data="filteredRoleData"
          border
          @selection-change="handleRoleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="name" label="角色名称"></el-table-column>
          <el-table-column prop="guard_name" label="Guard名称"></el-table-column>
          <el-table-column prop="remark" label="备注" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="roleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmRoles">确认</el-button>
      </div>
    </el-dialog>

    <!-- 头像上传对话框 -->
    <el-dialog
      title="上传头像"
      :visible.sync="avatarUploadVisible"
      width="30%"
    >
      <div v-loading="uploadLoading">
        <el-upload
          ref="avatarUpload"
          action=""
          :auto-upload="false"
          :on-change="handleAvatarChange"
          :show-file-list="false"
        >
          <img v-if="avatarUrl" :src="avatarUrl" class="avatar-preview">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          <div style="margin-top: 10px;">
            <el-button size="small" type="primary">选择头像</el-button>
          </div>
        </el-upload>
        <div style="margin-top: 20px; text-align: center;">
          <el-button size="small" type="success" @click="handleAvatarUpload" :disabled="!avatarFile">上传头像</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="30%"
    >
      <span>确定要删除此用户吗？此操作不可恢复。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="deleteData">确认删除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAdminUserList, createAdminUser, updateAdminUser, deleteAdminUser, getAdminUser, uploadAvatar } from '@/api/admin'
import { getPermissionTree } from '@/api/permission'
import { getAllRoles } from '@/api/role'
import Pagination from '@/components/Pagination'

export default {
  name: "AdminUserList",
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 10,
        username: "",
        name: "",
        email: "",
        state: "",
      },
      temp: {
        id: undefined,
        username: "",
        password: "",
        name: "",
        email: "",
        avatar: "",
        referee: "",
        state: 1,
        notify: 1,
        permission_ids: [],
        role_ids: [],
      },
      dialogFormVisible: false,
      dialogStatus: "",
      textMap: {
        update: "编辑管理员",
        create: "创建管理员",
      },
      rules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
        ],
        password: [
          { required: false, message: "密码不能为空", trigger: "blur" },
        ],
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
      },
      deleteDialogVisible: false,
      deleteId: null,
      multipleSelection: [],
      // 权限相关数据
      permissionDialogVisible: false,
      permissionLoading: false,
      permissionTreeData: [],
      selectedPermissions: [],
      permissionSearch: "",
      // 角色相关数据
      roleDialogVisible: false,
      roleLoading: false,
      roleData: [],
      selectedRoles: [],
      roleSearch: "",
      // 头像上传相关
      avatarUploadVisible: false,
      avatarFile: null,
      avatarUrl: "",
      uploadLoading: false,
    };
  },
  computed: {
    // 根据搜索过滤角色数据
    filteredRoleData() {
      if (!this.roleSearch) return this.roleData;
      return this.roleData.filter(role => 
        role.name.toLowerCase().includes(this.roleSearch.toLowerCase()) ||
        (role.guard_name && role.guard_name.toLowerCase().includes(this.roleSearch.toLowerCase()))
      );
    },
  },
  watch: {
    permissionSearch(val) {
      this.$refs.permissionTree && this.$refs.permissionTree.filter(val);
    },
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      getAdminUserList(this.listQuery)
        .then((response) => {
          this.list = response.items;
          this.total = response.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    resetFilter() {
      this.listQuery = {
        page: 1,
        page_size: 10,
        username: "",
        name: "",
        email: "",
        state: "",
      };
      this.getList();
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        username: "",
        password: "",
        name: "",
        email: "",
        avatar: "",
        referee: "",
        state: 1,
        notify: 1,
        permission_ids: [],
        role_ids: [],
      };
      this.selectedPermissions = [];
      this.selectedRoles = [];
      this.avatarUrl = "";
    },
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    createData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          const data = { ...this.temp };
          // 添加权限ID数组
          if (this.selectedPermissions.length > 0) {
            data.permission_ids = this.selectedPermissions.map(
              (item) => item.id
            );
          }
          
          // 添加角色ID数组
          if (this.selectedRoles.length > 0) {
            data.role_ids = this.selectedRoles.map((item) => item.id);
          }
          
          createAdminUser(data).then((response) => {
            this.dialogFormVisible = false;
            this.$notify({
              title: "成功",
              message: "创建管理员成功",
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        }
      });
    },
    handleUpdate(row) {
      this.resetTemp();
      this.dialogStatus = "update";
      getAdminUser(row.id).then((response) => {
        // 注意response结构可能是 { user: {...}, roles: [...], permissions: [...] }
        const userData = response.user || response;
        const roles = response.roles || [];
        const permissions = response.permissions || [];
        
        console.log("获取到的用户数据:", response);
        console.log("用户:", userData);
        console.log("角色:", roles);
        console.log("权限:", permissions);

        // 设置基本用户信息
        this.temp = {
          ...userData,
          password: "", // 清空密码字段
          role_ids: roles.map(role => role.id),
          permission_ids: permissions.map(perm => perm.id)
        };
        
        // 设置已选择的角色和权限
        this.selectedRoles = roles;
        this.selectedPermissions = permissions;
        
        // 确保头像URL是完整的路径
        if (this.temp.avatar) {
          let avatarUrl = this.temp.avatar;
          if (!avatarUrl.startsWith('http') && !avatarUrl.startsWith('/')) {
            avatarUrl = '/' + avatarUrl;
          }
          this.avatarUrl = avatarUrl;
          this.temp.avatar = avatarUrl;
        }
        
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs["dataForm"].clearValidate();
        });
      });
    },
    updateData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp);
          // 如果密码为空，则不更新密码
          if (!tempData.password) {
            delete tempData.password;
          }
          
          // 添加权限ID数组
          if (this.selectedPermissions.length > 0) {
            tempData.permission_ids = this.selectedPermissions.map(
              (item) => item.id
            );
          }
          
          // 添加角色ID数组
          if (this.selectedRoles.length > 0) {
            tempData.role_ids = this.selectedRoles.map((item) => item.id);
          }
          
          updateAdminUser(tempData.id, tempData).then(() => {
            this.dialogFormVisible = false;
            this.$notify({
              title: "成功",
              message: "更新管理员成功",
              type: "success",
              duration: 2000,
            });
            this.getList();
          });
        }
      });
    },
    handleDelete(row) {
      this.deleteId = row.id;
      this.deleteDialogVisible = true;
    },
    deleteData() {
      deleteAdminUser(this.deleteId).then(() => {
        this.$notify({
          title: "成功",
          message: "删除管理员成功",
          type: "success",
          duration: 2000,
        });
        this.getList();
        this.deleteDialogVisible = false;
      });
    },
    // 显示权限选择对话框
    showPermissionDialog() {
      if (this.permissionTreeData.length === 0) {
        this.loadPermissions();
      } else {
        this.permissionDialogVisible = true;
        this.$nextTick(() => {
          // 设置已选择的权限
          if (this.$refs.permissionTree && this.temp.permission_ids && this.temp.permission_ids.length > 0) {
            console.log("设置权限树选中状态，权限IDs:", this.temp.permission_ids);
            this.$refs.permissionTree.setCheckedKeys(this.temp.permission_ids);
          }
        });
      }
    },
    
    // 加载权限数据
    loadPermissions() {
      this.permissionLoading = true;
      getPermissionTree()
        .then((response) => {
          console.log("获取到的权限树数据:", response);
          this.permissionTreeData = response || [];
          this.permissionDialogVisible = true;
          this.permissionLoading = false;
          
          this.$nextTick(() => {
            // 设置已选择的权限
            if (this.$refs.permissionTree && this.temp.permission_ids && this.temp.permission_ids.length > 0) {
              console.log("加载权限后设置选中状态，权限IDs:", this.temp.permission_ids);
              this.$refs.permissionTree.setCheckedKeys(this.temp.permission_ids);
            }
          });
        })
        .catch((error) => {
          console.error("获取权限数据失败:", error);
          this.permissionLoading = false;
          this.$message.error("获取权限数据失败");
        });
    },
    
    // 确认选择的权限
    confirmPermissions() {
      if (this.$refs.permissionTree) {
        const checkedNodes = this.$refs.permissionTree.getCheckedNodes();
        const halfCheckedNodes = this.$refs.permissionTree.getHalfCheckedNodes();
        
        // 组合所有选中和半选中的节点
        const allSelectedNodes = [...checkedNodes, ...halfCheckedNodes];
        const permissionIds = allSelectedNodes.map((node) => node.id);
        
        this.temp.permission_ids = permissionIds;
        this.selectedPermissions = allSelectedNodes;
      }
      
      this.permissionDialogVisible = false;
    },
    
    // 过滤权限节点
    filterPermissionNode(value, data) {
      if (!value) return true;
      
      // 搜索权限名称或标识
      return data.name.indexOf(value) !== -1 || 
             (data.slug && data.slug.indexOf(value) !== -1);
    },
    
    // 显示角色选择对话框
    showRoleDialog() {
      if (this.roleData.length === 0) {
        this.loadRoles();
      } else {
        this.roleDialogVisible = true;
        this.$nextTick(() => {
          // 清除所有选择
          this.$refs.roleTable && this.$refs.roleTable.clearSelection();
          
          // 设置已选择的角色
          if (this.$refs.roleTable && this.temp.role_ids && this.temp.role_ids.length > 0) {
            console.log("需要选中的角色IDs:", this.temp.role_ids);
            this.roleData.forEach(role => {
              if (this.temp.role_ids.includes(role.id)) {
                console.log("选中角色:", role.name, role.id);
                this.$refs.roleTable.toggleRowSelection(role, true);
              }
            });
          }
        });
      }
    },
    
    // 加载角色数据
    loadRoles() {
      this.roleLoading = true;
      getAllRoles()
        .then((response) => {
          this.roleData = response || [];
          this.roleDialogVisible = true;
          this.roleLoading = false;
          
          this.$nextTick(() => {
            // 清除所有选择
            this.$refs.roleTable && this.$refs.roleTable.clearSelection();
            
            // 设置已选择的角色
            if (this.$refs.roleTable && this.temp.role_ids && this.temp.role_ids.length > 0) {
              console.log("加载完成后选中角色IDs:", this.temp.role_ids);
              this.roleData.forEach(role => {
                if (this.temp.role_ids.includes(role.id)) {
                  console.log("加载后选中角色:", role.name, role.id);
                  this.$refs.roleTable.toggleRowSelection(role, true);
                }
              });
            }
          });
        })
        .catch(() => {
          this.roleLoading = false;
          this.$message.error("获取角色数据失败");
        });
    },
    
    // 选择角色
    handleRoleSelectionChange(selection) {
      this.selectedRoles = selection;
      this.temp.role_ids = selection.map((item) => item.id);
    },
    
    // 确认选择的角色
    confirmRoles() {
      this.roleDialogVisible = false;
    },
    
    // 显示头像上传对话框
    showAvatarUpload() {
      this.avatarUploadVisible = true;
    },
    
    // 处理头像选择
    handleAvatarChange(file) {
      this.avatarFile = file.raw
      // 预览头像
      const reader = new FileReader()
      reader.onload = (e) => {
        this.avatarUrl = e.target.result
      }
      reader.readAsDataURL(file.raw)
    },
    
    // 处理头像上传
    handleAvatarUpload() {
      if (!this.avatarFile) {
        this.$message.warning('请选择头像文件')
        return
      }
      
      // 验证文件类型和大小
      const isJPG = this.avatarFile.type === 'image/jpeg' || this.avatarFile.type === 'image/png'
      const isLt2M = this.avatarFile.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 或 PNG 格式!')
        return
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
        return
      }
      
      this.uploadLoading = true
      const formData = new FormData()
      formData.append('file', this.avatarFile)
      
      uploadAvatar(formData)
        .then(response => {
          // 确保URL是完整的路径
          let avatarUrl = response.url
          if (avatarUrl && !avatarUrl.startsWith('http') && !avatarUrl.startsWith('/')) {
            avatarUrl = '/' + avatarUrl
          }
          this.temp.avatar = avatarUrl
          this.avatarUrl = avatarUrl
          this.$message.success('头像上传成功')
          this.avatarUploadVisible = false
        })
        .catch(error => {
          this.$message.error(`头像上传失败: ${error.message}`)
        })
        .finally(() => {
          this.uploadLoading = false
        })
    },
    
    // 表格选择变化
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
    
    // 批量删除
    handleBatchDelete() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请选择要删除的用户");
        return;
      }
      
      this.$confirm(`确认批量删除选中的 ${this.multipleSelection.length} 个用户吗？此操作不可恢复。`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 这里应该调用批量删除API
        const deletePromises = this.multipleSelection.map(item => deleteAdminUser(item.id));
        
        Promise.all(deletePromises).then(() => {
          this.$message.success("批量删除成功");
          this.getList();
        }).catch(error => {
          this.$message.error(`批量删除失败: ${error.message}`);
        });
      }).catch(() => {
        // 取消删除
      });
    }
  }
};
</script>

<style scoped>
.admin-user-container {
  padding: 20px;
}
.filter-container {
  margin-bottom: 20px;
}
.action-container {
  margin-bottom: 20px;
}
.filter-form {
  display: flex;
  flex-wrap: wrap;
}
.tips {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}
.selected-permissions,
.selected-roles {
  margin-top: 5px;
  font-size: 12px;
  color: #409EFF;
}
.custom-tree-node {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.permission-slug,
.role-slug {
  color: #909399;
  font-size: 12px;
  margin-left: 10px;
}
.permission-http {
  color: #409EFF;
  font-size: 12px;
  margin-left: 10px;
}
.avatar-preview {
  margin-top: 10px;
  text-align: center;
  width: 100%;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
}
</style>
