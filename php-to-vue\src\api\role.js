import request from './index'

/**
 * 获取角色列表
 * @param {Object} params - 查询参数
 * @param {string} [params.name] - 角色名称
 * @param {string} [params.guard_name] - Guard名称
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getRoles(params) {
  return request({
    url: '/admin/role/index',
    method: 'get',
    params
  })
}

/**
 * 获取角色详情
 * @param {number} id - 角色ID
 * @returns {Promise}
 */
export function getRole(id) {
  return request({
    url: `/admin/role/index/${id}`,
    method: 'get'
  })
}

/**
 * 创建角色
 * @param {Object} data - 角色数据
 * @param {string} data.name - 角色名称
 * @param {string} data.guard_name - Guard名称
 * @param {string} [data.remark] - 备注
 * @param {Array} [data.permission_ids] - 关联权限ID列表
 * @param {Array} [data.menu_ids] - 关联菜单ID列表
 * @returns {Promise}
 */
export function createRole(data) {
  return request({
    url: '/admin/role/index',
    method: 'post',
    data
  })
}

/**
 * 更新角色
 * @param {number} id - 角色ID
 * @param {Object} data - 更新数据
 * @param {string} [data.name] - 角色名称
 * @param {string} [data.guard_name] - Guard名称
 * @param {string} [data.remark] - 备注
 * @param {Array} [data.permission_ids] - 关联权限ID列表
 * @param {Array} [data.menu_ids] - 关联菜单ID列表
 * @returns {Promise}
 */
export function updateRole(id, data) {
  return request({
    url: `/admin/role/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param {number} id - 角色ID
 * @returns {Promise}
 */
export function deleteRole(id) {
  return request({
    url: `/admin/role/index/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除角色
 * @param {Array} ids - 角色ID列表
 * @returns {Promise}
 */
export function batchDeleteRoles(ids) {
  return request({
    url: '/admin/role/batch-delete',
    method: 'post',
    data: { ids }
  })
}

/**
 * 获取所有角色（不分页）
 * @returns {Promise}
 */
export function getAllRoles() {
  return request({
    url: '/admin/role/all',
    method: 'get'
  })
}

/**
 * 分配权限给角色
 * @param {number} roleId - 角色ID
 * @param {Array} permissionIds - 权限ID列表
 * @returns {Promise}
 */
export function assignPermissionsToRole(roleId, permissionIds) {
  return request({
    url: `/admin/role/${roleId}/permissions`,
    method: 'post',
    data: { ids: permissionIds }
  })
}

/**
 * 分配菜单给角色
 * @param {number} roleId - 角色ID
 * @param {Array} menuIds - 菜单ID列表
 * @returns {Promise}
 */
export function assignMenusToRole(roleId, menuIds) {
  return request({
    url: `/admin/role/${roleId}/menus`,
    method: 'post',
    data: { ids: menuIds }
  })
}

/**
 * 获取角色用户列表
 * @param {number} roleId - 角色ID
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getRoleUsers(roleId, params) {
  return request({
    url: `/admin/role/${roleId}/users`,
    method: 'get',
    params
  })
}

/**
 * 分配角色给用户
 * @param {number} roleId - 角色ID
 * @param {Array} userIds - 用户ID列表
 * @returns {Promise}
 */
export function assignRoleToUsers(roleId, userIds) {
  return request({
    url: `/admin/role/${roleId}/users`,
    method: 'post',
    data: { ids: userIds }
  })
}

/**
 * 从用户移除角色
 * @param {number} roleId - 角色ID
 * @param {Array} userIds - 用户ID列表
 * @returns {Promise}
 */
export function removeRoleFromUsers(roleId, userIds) {
  return request({
    url: `/admin/role/${roleId}/users/remove`,
    method: 'post',
    data: { ids: userIds }
  })
} 