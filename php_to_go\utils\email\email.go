package email

import (
	"bytes"
	"fmt"
	"html/template"
	"log"
	"strings"
	"time"

	"go-fiber-api/config"
	"go-fiber-api/utils"
	"gopkg.in/gomail.v2"
)

// EmailData 包含发送电子邮件所需的所有数据
type EmailData struct {
	To       []string          // 收件人列表
	Subject  string            // 邮件主题
	Body     string            // 邮件内容（HTML或纯文本）
	Template string            // 模板名称
	Data     map[string]string // 模板数据
	IsHTML   bool              // 是否为HTML邮件
}


// SendEmail 发送电子邮件
func SendEmail(data EmailData) error {
	cfg := config.LoadConfig()

	smtpHost := cfg.SMTPHost
	smtpPort := cfg.SMTPPort
	smtpUser := cfg.SMTPUser
	smtpPassword := cfg.SMTPPassword
	smtpFrom := cfg.SMTPFrom

	if smtpHost == "" || smtpPort == 0 || smtpUser == "" || smtpPassword == "" || smtpFrom == "" {
		return fmt.Errorf("SMTP配置不完整")
	}

	m := gomail.NewMessage()
	m.SetHeader("From", smtpFrom)
	m.SetHeader("To", data.To...)
	m.SetHeader("Subject", data.Subject)
	// 支持HTML和纯文本
	if data.IsHTML {
		m.SetBody("text/html", GetEmailBody(data))
	} else {
		m.SetBody("text/plain", GetEmailBody(data))
	}

	d := gomail.NewDialer(smtpHost, smtpPort, smtpUser, smtpPassword)
	d.SSL = true // 465端口必须为true

	if err := d.DialAndSend(m); err != nil {
		log.Printf("邮件发送失败: %v", err)
		return err
	}
	log.Printf("邮件发送成功: %v", strings.Join(data.To, ","))
	return nil
}

// GetEmailBody 根据提供的数据获取邮件正文
func GetEmailBody(data EmailData) string {
	// 如果提供了模板，使用模板渲染
	if data.Template != "" {
		tmpl, err := template.New("email").Parse(data.Template)
		if err != nil {
			log.Printf("解析邮件模板失败: %v", err)
			return data.Body // 如果模板解析失败，返回原始正文
		}

		var buf bytes.Buffer
		if err := tmpl.Execute(&buf, data.Data); err != nil {
			log.Printf("渲染邮件模板失败: %v", err)
			return data.Body // 如果模板渲染失败，返回原始正文
		}

		return buf.String()
	}

	// 如果没有提供模板，直接返回邮件正文
	return data.Body
}

// GeneratePasswordResetToken 生成密码重置令牌
func GeneratePasswordResetToken(userID uint64) (string, error) {
	// 生成带有短期过期时间的JWT令牌
	claims := utils.AuthClaims{
		UserID:    userID,
		TokenType: "password_reset",
		ExpiresAt: time.Now().Add(time.Duration(utils.GetPasswordResetExpireMinutes()) * time.Minute).Unix(),
	}

	return utils.GenerateCustomToken(claims)
}

// VerifyPasswordResetToken 验证密码重置令牌
func VerifyPasswordResetToken(token string) (uint64, error) {
	// 解析并验证令牌
	claims, err := utils.ParseCustomToken(token)
	if err != nil {
		return 0, err
	}

	// 验证令牌类型
	if claims.TokenType != "password_reset" {
		return 0, fmt.Errorf("无效的令牌类型")
	}

	// 返回用户ID
	return claims.UserID, nil
}

// GenerateEmailVerificationToken 生成邮箱验证令牌
func GenerateEmailVerificationToken(userID uint64) (string, error) {
	// 生成带有短期过期时间的JWT令牌
	claims := utils.AuthClaims{
		UserID:    userID,
		TokenType: "email_verification",
		ExpiresAt: time.Now().Add(time.Duration(utils.GetEmailVerificationExpireMinutes()) * time.Minute).Unix(),
	}

	return utils.GenerateCustomToken(claims)
}

// VerifyEmailVerificationToken 验证邮箱验证令牌
func VerifyEmailVerificationToken(token string) (uint64, error) {
	// 解析并验证令牌
	claims, err := utils.ParseCustomToken(token)
	if err != nil {
		return 0, err
	}

	// 验证令牌类型
	if claims.TokenType != "email_verification" {
		return 0, fmt.Errorf("无效的令牌类型")
	}

	// 返回用户ID
	return claims.UserID, nil
}

// GetPasswordResetEmailTemplate 获取密码重置邮件模板
func GetPasswordResetEmailTemplate() string {
	// 使用模板工具加载邮件模板
	content, err := utils.GetEmailTemplate("password_reset")
	if err != nil {
		log.Printf("无法加载密码重置邮件模板: %v", err)
		// 返回一个简单的备用模板，以防文件无法读取
		return `
<!DOCTYPE html>
<html>
<body>
    <p>尊敬的 {{.Username}}：</p>
    <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
    <p><a href="{{.ResetLink}}">重置密码</a></p>
    <p>或者复制以下链接到浏览器地址栏：</p>
    <p>{{.ResetLink}}</p>
    <p>此链接将在 1 小时后失效。如果您没有请求重置密码，请忽略此邮件。</p>
</body>
</html>
`
	}
	return content
} 

