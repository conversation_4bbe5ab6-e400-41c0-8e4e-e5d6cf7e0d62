.project-detail {
    padding: 20px;
}

.page-header {
    display        : flex;
    justify-content: space-between;
    align-items    : center;
    margin-bottom  : 20px;
}

.page-title {
    margin   : 0;
    font-size: 24px;
    color    : #303133;
}

.page-actions {
    display: flex;
    gap    : 10px;
}

.project-card {
    margin-bottom: 20px;
}

.empty-state {
    text-align: center;
    padding   : 40px 0;
}

.empty-state i {
    font-size    : 48px;
    color        : #909399;
    margin-bottom: 20px;
}

.empty-state p {
    color        : #606266;
    margin-bottom: 20px;
}

.project-code-section {
    margin-top: 20px;
}

.code-container {
    margin-top      : 10px;
    padding         : 15px;
    background-color: #f5f7fa;
    border-radius   : 4px;
    overflow-x      : auto;
}

.code-container pre {
    margin     : 0;
    font-family: 'Courier New', Courier, monospace;
    white-space: pre-wrap;
}

.danger-zone {
    margin-top: 20px;
    border    : 1px solid #f56c6c;
}

.danger-header {
    color      : #f56c6c;
    font-weight: bold;
}

.content-header {
    margin-bottom: 20px;
}

.pagination-container {
    margin-top: 20px;
    text-align: right;
}

.batch-actions {
    margin-top: 20px;
}

/* 项目代码标签页样式 */
.code-selection-header {
    margin-bottom : 20px;
    padding-bottom: 15px;
    border-bottom : 1px solid #ebeef5;
}

.selection-row {
    display      : flex;
    gap          : 20px;
    align-items  : center;
    margin-bottom: 10px;
}

.selection-item {
    display    : flex;
    align-items: center;
    gap        : 10px;
}

.selection-label {
    font-weight: bold;
    min-width  : 100px;
}

.xss-code-section {
    margin-top: 20px;
}

.code-block {
    margin-bottom   : 20px;
    padding         : 15px;
    background-color: #f8f8f8;
    border-radius   : 4px;
    border          : 1px solid #ebeef5;
}

.code-block h4 {
    margin-top   : 0;
    margin-bottom: 10px;
    color        : #606266;
}

.code-textarea {
    margin-bottom: 10px;
    font-family  : 'Courier New', Courier, monospace;
}