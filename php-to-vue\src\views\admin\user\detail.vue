<template>
  <div class="admin-user-detail-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>管理员用户详情</span>
        <el-button 
          style="float: right; margin-left: 10px;" 
          type="primary" 
          icon="el-icon-edit"
          @click="handleEdit"
        >编辑</el-button>
        <el-button 
          style="float: right;" 
          type="danger" 
          icon="el-icon-delete"
          @click="handleDelete"
        >删除</el-button>
      </div>
      
      <div v-loading="loading" class="user-info">
        <div class="user-avatar">
          <el-avatar 
            :size="100" 
            :src="userInfo.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" 
          />
        </div>
        
        <el-descriptions class="margin-top" title="用户信息" :column="2" border>
          <el-descriptions-item label="用户ID">{{ userInfo.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ userInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ userInfo.email || '-' }}</el-descriptions-item>
          <el-descriptions-item label="推荐人">{{ userInfo.referee || '-' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="userInfo.state === 1 ? 'success' : 'danger'">
              {{ userInfo.state === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="通知设置">
            <el-tag :type="userInfo.notify === 1 ? 'success' : 'info'">
              {{ userInfo.notify === 1 ? '开启' : '关闭' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ userInfo.created_at }}</el-descriptions-item>
          <el-descriptions-item label="最后更新时间">{{ userInfo.updated_at || '-' }}</el-descriptions-item>
          <el-descriptions-item label="最后登录时间">{{ userInfo.lasted_at || '-' }}</el-descriptions-item>
          <el-descriptions-item label="最后登录IP">{{ userInfo.lasted_ipaddress || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
    
    <!-- 权限管理卡片 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>权限管理</span>
        <el-button 
          style="float: right;" 
          type="primary" 
          icon="el-icon-edit"
          @click="handleEditPermissions"
        >编辑权限</el-button>
      </div>
      
      <div v-loading="permissionLoading">
        <el-empty v-if="!userPermissions.length" description="暂无已分配权限"></el-empty>
        <el-tree
          v-else
          :data="permissionTreeData"
          :props="{
            children: 'children',
            label: 'name'
          }"
          node-key="id"
          default-expand-all
          :highlight-current="false"
          :expand-on-click-node="false"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <span>{{ data.name }}</span>
            <span class="permission-slug">{{ data.slug }}</span>
          </span>
        </el-tree>
      </div>
    </el-card>
    
    <!-- 编辑用户对话框 -->
    <el-dialog title="编辑管理员用户" :visible.sync="dialogVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="userForm"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input 
            v-model="userForm.password" 
            placeholder="请输入新密码，留空则不修改" 
            type="password" 
          />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="头像URL" prop="avatar">
          <el-input v-model="userForm.avatar" placeholder="请输入头像URL" />
        </el-form-item>
        <el-form-item label="推荐人" prop="referee">
          <el-input v-model="userForm.referee" placeholder="请输入推荐人" />
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-select v-model="userForm.state" placeholder="请选择状态">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="通知设置" prop="notify">
          <el-select v-model="userForm.notify" placeholder="请选择通知设置">
            <el-option label="开启" :value="1" />
            <el-option label="关闭" :value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updateUser">确认</el-button>
      </div>
    </el-dialog>
    
    <!-- 权限编辑对话框 -->
    <el-dialog title="编辑用户权限" :visible.sync="permissionDialogVisible" width="60%">
      <div v-loading="permissionTreeLoading">
        <el-tree
          ref="permissionTree"
          :data="allPermissions"
          :props="{
            children: 'children',
            label: 'name'
          }"
          node-key="id"
          show-checkbox
          default-expand-all
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <span>{{ data.name }}</span>
            <span class="permission-slug">{{ data.slug }}</span>
            <span v-if="data.http_path" class="permission-http">
              {{ data.http_method || '*' }} {{ data.http_path }}
            </span>
          </span>
        </el-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updateUserPermissions">确认</el-button>
      </div>
    </el-dialog>
    
    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="30%"
    >
      <span>确定要删除此管理员用户吗？此操作不可恢复。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="deleteUser">确认删除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAdminUser, updateAdminUser, deleteAdminUser } from '@/api/admin'
import { getPermissionList, getPermissionTree } from '@/api/permission'

export default {
  name: 'AdminUserDetail',
  data() {
    return {
      userId: null,
      loading: true,
      userInfo: {},
      userForm: {
        id: undefined,
        username: '',
        password: '',
        name: '',
        email: '',
        avatar: '',
        referee: '',
        state: 1,
        notify: 1
      },
      dialogVisible: false,
      deleteDialogVisible: false,
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
      },
      // 权限相关数据
      permissionLoading: false,
      permissionDialogVisible: false,
      permissionTreeLoading: false,
      allPermissions: [], // 所有权限
      userPermissions: [], // 用户已有权限
      permissionTreeData: [] // 用户权限的树形结构
    }
  },
  created() {
    this.userId = this.$route.params.id
    this.getUserInfo()
    this.getUserPermissions()
  },
  methods: {
    getUserInfo() {
      this.loading = true
      getAdminUser(this.userId).then(response => {
        this.userInfo = response
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 获取用户权限列表
    getUserPermissions() {
      this.permissionLoading = true
      // 假设这个接口返回用户权限数据
      // 实际使用时需要根据具体后端接口进行调整
      getAdminUser(this.userId).then(response => {
        if (response && response.permissions) {
          this.userPermissions = response.permissions || []
          this.buildPermissionTree()
        }
        this.permissionLoading = false
      }).catch(() => {
        this.permissionLoading = false
      })
    },
    // 构建权限树形结构
    buildPermissionTree() {
      if (!this.userPermissions || this.userPermissions.length === 0) {
        this.permissionTreeData = []
        return
      }
      
      // 如果后端已经返回树形结构，则直接使用
      if (this.userPermissions[0].children) {
        this.permissionTreeData = this.userPermissions
        return
      }
      
      // 如果是平铺的权限列表，则手动构建树形结构
      const permissionMap = {}
      const rootPermissions = []
      
      // 第一步：创建以id为键的map
      this.userPermissions.forEach(permission => {
        permission.children = []
        permissionMap[permission.id] = permission
      })
      
      // 第二步：将权限放到其父节点的children数组中
      this.userPermissions.forEach(permission => {
        if (permission.parent_id && permission.parent_id !== 0 && permissionMap[permission.parent_id]) {
          permissionMap[permission.parent_id].children.push(permission)
        } else {
          rootPermissions.push(permission)
        }
      })
      
      this.permissionTreeData = rootPermissions
    },
    // 加载所有权限
    loadAllPermissions() {
      this.permissionTreeLoading = true
      getPermissionTree().then(response => {
        this.allPermissions = response || []
        this.permissionTreeLoading = false
        this.$nextTick(() => {
          // 设置用户已有权限为选中状态
          if (this.$refs.permissionTree && this.userPermissions.length > 0) {
            const permissionIds = this.userPermissions.map(item => item.id)
            this.$refs.permissionTree.setCheckedKeys(permissionIds)
          }
        })
      }).catch(() => {
        this.permissionTreeLoading = false
      })
    },
    handleEdit() {
      this.userForm = Object.assign({}, this.userInfo)
      this.userForm.password = '' // 清空密码字段
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateUser() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const data = Object.assign({}, this.userForm)
          // 如果密码为空，则不更新密码
          if (!data.password) {
            delete data.password
          }
          
          updateAdminUser(this.userId, data).then(() => {
            this.dialogVisible = false
            this.$notify({
              title: '成功',
              message: '更新管理员用户成功',
              type: 'success',
              duration: 2000
            })
            this.getUserInfo() // 刷新用户信息
          })
        }
      })
    },
    // 编辑用户权限
    handleEditPermissions() {
      this.permissionDialogVisible = true
      if (this.allPermissions.length === 0) {
        this.loadAllPermissions()
      }
    },
    // 更新用户权限
    updateUserPermissions() {
      if (!this.$refs.permissionTree) {
        return
      }
      
      const selectedPermissions = this.$refs.permissionTree.getCheckedKeys()
      // 假设后端接口接受权限ID数组
      const data = {
        permission_ids: selectedPermissions
      }
      
      // 发送请求更新用户权限
      updateAdminUser(this.userId, data).then(() => {
        this.permissionDialogVisible = false
        this.$notify({
          title: '成功',
          message: '更新用户权限成功',
          type: 'success',
          duration: 2000
        })
        this.getUserPermissions() // 刷新权限信息
      })
    },
    handleDelete() {
      this.deleteDialogVisible = true
    },
    deleteUser() {
      deleteAdminUser(this.userId).then(() => {
        this.$notify({
          title: '成功',
          message: '删除管理员用户成功',
          type: 'success',
          duration: 2000
        })
        this.deleteDialogVisible = false
        this.$router.push('/admin/users')
      })
    }
  }
}
</script>

<style scoped>
.admin-user-detail-container {
  padding: 20px;
}
.box-card {
  width: 100%;
  margin-bottom: 20px;
}
.user-info {
  padding: 20px 0;
}
.user-avatar {
  text-align: center;
  margin-bottom: 20px;
}
.margin-top {
  margin-top: 20px;
}
.custom-tree-node {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.permission-slug {
  color: #909399;
  font-size: 12px;
  margin-left: 10px;
}
.permission-http {
  color: #409EFF;
  font-size: 12px;
  margin-left: 10px;
}
</style> 