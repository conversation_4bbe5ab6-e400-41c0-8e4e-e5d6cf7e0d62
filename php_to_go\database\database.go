package database

import (
	"fmt"
	"log"
	"os"

	"go-fiber-api/config"
	"go-fiber-api/models"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDatabase 连接到数据库并迁移，用于测试
func InitDatabase() error {
	err := ConnDatabase()
	if err != nil {
		return err
	}

	// 自动迁移数据库结构
	if err := autoMigrate(); err != nil {
		log.Println("数据库迁移失败:", err)
		return err
	}

	return nil
}

// ConnDatabase 连接到数据库
func ConnDatabase() error {
	var err error

	// 获取数据库配置
	dbConfig := config.LoadConfig().DBConfig

	// 构建DSN连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbConfig.User,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.DBName,
	)
	// 打印当前工作目录（os.Getwd()）。
	// 打印 config.LoadConfig().DBConfig 的内容。
	// 打印 database.ConnDatabase() 里的 dsn 字符串和 err。
	
	dir, err := os.Getwd()
	log.Println("当前工作目录:", dir, "错误信息:", err)

	log.Println("数据库配置:", config.LoadConfig().DBConfig)
	log.Println("数据库连接字符串:", dsn)
	log.Println("数据库连接错误:", err)

	// 连接数据库
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})

	if err != nil {
		log.Println("数据库连接失败:", err)
		return err
	}

	log.Println("数据库连接成功")
	return nil
}

// autoMigrate 自动迁移数据库表结构
func autoMigrate() error {
	// 在这里注册所有需要自动迁移的模型
	// 注意：自动迁移仅创建缺少的表/列/索引
	// 不会删除或修改现有表/列/索引
	return DB.AutoMigrate(
		// 管理员相关模型
		&models.AdminUser{},
		&models.Role{},
		&models.Permission{},
		&models.Menu{},
		&models.AdminRoleUser{},
		&models.AdminRolePermission{},
		&models.AdminRoleMenu{},
		&models.AdminPermissionMenu{},
		&models.AdminOperationLog{},

		// 内容相关模型
		&models.Article{},
		&models.Domain{},
		&models.Friendly{},
		&models.Invitation{},

		// 项目相关模型
		&models.Project{},
		&models.ProjectContent{},
		&models.Module{},
		&models.XssTemplate{},

		// 系统设置模型
		&models.Settings{},

		// 其他模型
		&models.FailedJob{},
		&models.Job{},
		&models.Migration{},
	)
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return DB
}
