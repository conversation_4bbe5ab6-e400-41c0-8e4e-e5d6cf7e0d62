#!/bin/bash

# 修复Docker构建问题的脚本

echo "=== 修复Docker构建配置 ==="

# 检查项目结构
echo "检查项目结构..."

if [ ! -f "php_to_go/go.mod" ]; then
    echo "错误: 找不到 php_to_go/go.mod 文件"
    echo "请确保您在项目根目录中运行此脚本"
    exit 1
fi

# 检查前端项目位置
FRONTEND_PACKAGE=""
if [ -f "frontend/package.json" ]; then
    FRONTEND_PACKAGE="frontend/package.json"
    echo "✓ 找到前端项目: frontend/"
elif [ -f "php-to-vue/package.json" ]; then
    FRONTEND_PACKAGE="php-to-vue/package.json"
    echo "✓ 找到前端项目: php-to-vue/"
else
    # 尝试查找package.json文件
    FRONTEND_PACKAGE=$(find . -name "package.json" -not -path "./node_modules/*" | head -1)
    if [ -n "$FRONTEND_PACKAGE" ]; then
        echo "✓ 找到前端项目: $FRONTEND_PACKAGE"
    else
        echo "错误: 找不到前端项目的package.json文件"
        echo "请确保前端项目存在"
        exit 1
    fi
fi

echo "✓ 项目结构检查通过"

# 清理之前的构建
echo "清理之前的Docker构建..."
cd docker
docker-compose -f docker-compose.prod.yml down || true
docker system prune -f

echo "✓ 清理完成"

# 重新构建
echo "开始重新构建..."
docker-compose -f docker-compose.prod.yml build --no-cache

if [ $? -eq 0 ]; then
    echo "✓ 构建成功！"
    echo "现在可以启动服务:"
    echo "docker-compose -f docker-compose.prod.yml up -d"
else
    echo "❌ 构建失败"
    echo "请检查错误信息并修复问题"
    exit 1
fi
