import request from './index'

/**
 * 获取邀请码列表
 * @param {Object} params - 查询参数
 * @param {string} [params.code] - 邀请码(模糊查询)
 * @param {number} [params.state] - 状态(1:启用, 0:禁用, 不传:所有)
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getInvitationList(params) {
  return request({
    url: '/admin/invitation/index',
    method: 'get',
    params
  })
}

/**
 * 创建邀请码
 * @param {Object} data - 邀请码数据
 * @param {string} data.code - 邀请码(必填)
 * @param {string} [data.description] - 描述
 * @param {number} [data.state=1] - 状态(1:启用, 0:禁用)
 * @param {number} [data.limit=0] - 使用次数限制(0表示不限制)
 * @param {string} [data.expired_at] - 过期时间
 * @param {number} data.count - 创建数量，单个创建时为1
 * @returns {Promise}
 */
export function createInvitation(data) {
  // 确保必填字段存在
  if (!data.code) {
    throw new Error('邀请码不能为空')
  }
  
  if (!data.count || data.count < 1) {
    data.count = 1
  }
  
  return request({
    url: '/admin/invitation/index',
    method: 'post',
    data
  })
}

/**
 * 获取单个邀请码详情
 * @param {number} id - 邀请码ID
 * @returns {Promise}
 */
export function getInvitation(id) {
  return request({
    url: `/admin/invitation/index/${id}`,
    method: 'get'
  })
}

/**
 * 更新邀请码
 * @param {number} id - 邀请码ID
 * @param {Object} data - 需要更新的邀请码数据
 * @param {string} [data.code] - 邀请码
 * @param {string} [data.description] - 描述
 * @param {number} [data.state] - 状态(1:启用, 0:禁用)
 * @param {number} [data.limit] - 使用次数限制
 * @param {string} [data.expired_at] - 过期时间
 * @returns {Promise}
 */
export function updateInvitation(id, data) {
  return request({
    url: `/admin/invitation/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除邀请码
 * @param {number} id - 邀请码ID
 * @returns {Promise}
 */
export function deleteInvitation(id) {
  return request({
    url: `/admin/invitation/index/${id}`,
    method: 'delete'
  })
}

/**
 * 批量操作邀请码
 * @param {Object} data - 批量操作数据
 * @param {Array} [data.ids] - 邀请码ID数组(删除、启用、禁用时需要)
 * @param {string} data.action - 操作类型：create(批量创建)/delete(批量删除)/enable(批量启用)/disable(批量禁用)
 * @param {number} [data.count] - 创建数量(仅当action为create时有效，最小值为1)
 * @param {number} [data.limit] - 使用次数限制(仅当action为create时有效)
 * @param {string} [data.expired_at] - 过期时间(仅当action为create时有效)
 * @returns {Promise}
 */
export function batchInvitation(data) {
  // 创建一个新的对象，以便不修改原始对象
  const payload = { ...data }
  

  
  // 如果是创建操作，需要走创建接口，不是批量操作
  if (payload.action === 'Create') {
    return request({
      url: '/admin/invitation/index',
      method: 'post',
      data: {
        code: payload.code || `AUTO_${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
        count: payload.count || 1,
        state: payload.state !== undefined ? payload.state : 1,
        limit: payload.limit,
        expired_at: payload.expired_at
      }
    })
  }
  


  
  // 打印发送的请求，便于调试
  console.log('批量操作请求参数:', payload)
  
  return request({
    url: '/admin/invitation/batch',
    method: 'post',
    data: payload
  })
} 