# Go Fiber API 项目运行教程

本教程将指导您如何设置和运行 Go Fiber API 项目。

## 环境准备

### 1. 安装 Go

首先需要安装 Go 环境（建议 Go 1.16 或更高版本）：

- Windows: 从 [Go 官网](https://golang.org/dl/) 下载安装包
- 验证安装: 打开命令提示符或 PowerShell 运行 `go version`

### 2. 安装 MySQL 数据库

- Windows: 从 [MySQL 官网](https://dev.mysql.com/downloads/installer/) 下载安装包
- 安装过程中记下您设置的 root 密码
- 安装完成后创建一个名为 `go_fiber` 的数据库

```sql
CREATE DATABASE go_fiber CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 项目设置

### 1. 安装项目依赖

在项目根目录下运行以下命令安装所需依赖：

```bash
go mod tidy
```

如果遇到网络问题，可以设置 GOPROXY：

```bash
go env -w GOPROXY=https://goproxy.cn,direct
```

### 2. 配置环境变量

1. 在项目根目录创建 `.env` 文件：

```
# 应用设置
APP_NAME=Go Fiber API
APP_ENV=development
PORT=3000

# 数据库设置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=你的数据库密码
DB_NAME=go_fiber

# JWT 设置
JWT_SECRET=your_secret_key_here
JWT_EXPIRATION=24h
```

2. 请确保将 `DB_PASSWORD` 修改为您的 MySQL root 密码

## 运行项目

### 方法一：直接运行

在项目根目录下执行：

```bash
go run cmd/server/main.go
```

### 方法二：构建后运行

```bash
# 构建项目
go build -o api-server.exe cmd/server/main.go

# 运行可执行文件
./api-server.exe
```

如果一切正常，您将看到类似以下输出：

```
服务器启动在端口 3000
数据库连接成功
```

现在可以通过浏览器或API测试工具访问 `http://localhost:3000/api/health` 检查服务是否正常运行。

## 测试 API

```go
go test -v ./test
```

您可以使用 [Postman](https://www.postman.com/downloads/) 或 [Insomnia](https://insomnia.rest/download) 等 API 测试工具测试以下端点：

### 健康检查

```
GET http://localhost:3000/api/health
```

预期响应：

```json
{
  "status": "success",
  "message": "系统正常运行"
}
```

### 用户 API（需先启用）

要启用用户 API，请编辑 `api/routes/routes.go` 文件，取消注释 `setupUserRoutes(api)` 行和整个 `setupUserRoutes` 函数，并确保导入了 handlers 包。

## 常见问题排查

### 1. 数据库连接失败

- 确认 MySQL 服务已启动
- 验证 .env 文件中的数据库配置是否正确
- 确认数据库 `go_fiber` 已创建

### 2. 端口占用问题

如果端口 3000 已被占用，可在 .env 文件中修改 PORT 值。

### 3. 依赖导入错误

如果遇到找不到模块的错误，可能需要调整导入路径。确保 `go.mod` 文件中的模块名称与代码中的导入路径一致。

### 4. Go 版本问题

如果收到 Go 版本不兼容的警告，请更新 Go 到最新稳定版本，或修改 `go.mod` 文件中的 Go 版本。

## 生成Swagger文档的步骤：

### 安装swag命令行工具：
go install github.com/swaggo/swag/cmd/swag@latest

### 如果GOPATH环境变量没有设置，可以手动设置：
go env -w GOPATH=C:\Users\<USER>\go

### 确保swag命令可用：
C:\Users\<USER>\go\bin\swag.exe -v

### 生成Swagger文档：
C:\Users\<USER>\go\bin\swag.exe init -g cmd/server/main.go -o docs

## 开发流程

1. 启动项目并确保 API 正常工作
2. 在 `models` 目录中添加新的数据模型
3. 在 `database/database.go` 中注册模型进行自动迁移
4. 在 `api/handlers` 中创建新的处理函数
5. 在 `api/routes/routes.go` 中定义新的路由

## 项目结构参考

```
go-fiber-api/
├── api/                                       # API相关代码
│   ├── dto/                                   # 数据传输对象 (请求/响应结构体)
│   │   ├── admin_user_dto.go                  # 管理员用户DTO
│   │   ├── article_dto.go                     # 文章DTO
│   │   ├── auth_dto.go                        # 认证DTO
│   │   ├── common_dto.go                      # 通用响应DTO
│   │   ├── domain_dto.go                      # 域名DTO
│   │   ├── email.go                           # 邮件DTO
│   │   ├── failed_job_dto.go                  # 失败任务DTO
│   │   ├── friendly_dto.go                    # 友情链接DTO
│   │   ├── invitation_dto.go                  # 邀请码DTO
│   │   ├── job_dto.go                         # 队列任务DTO
│   │   ├── menu_dto.go                        # 菜单DTO
│   │   ├── module_dto.go                      # 模块DTO
│   │   ├── permission_dto.go                  # 权限DTO
│   │   ├── project_dto.go                     # 项目DTO
│   │   ├── research_dto.go                    # 检索器DTO
│   │   ├── research_job_dto.go                # 检索任务DTO
│   │   ├── research_language_dto.go           # 检索语言包DTO
│   │   ├── research_task_dto.go               # 检索动作DTO
│   │   ├── role_dto.go                        # 角色DTO
│   │   ├── settings_dto.go                    # 系统设置DTO
│   ├── handlers/                              # HTTP请求处理函数
│   │   ├── admin_user_handler.go              # 管理员用户处理器
│   │   ├── article_handler.go                 # 文章处理器
│   │   ├── auth_handler.go                    # 认证处理器
│   │   ├── domain_handler.go                  # 域名处理器
│   │   ├── email_handler.go                   # 邮件处理器
│   │   ├── failed_job_handler.go              # 失败任务处理器
│   │   ├── friendly_handler.go                # 友情链接处理器
│   │   ├── gateway_handler.go                 # 网关/数据收集处理器
│   │   ├── invitation_handler.go              # 邀请码处理器
│   │   ├── job_handler.go                     # 队列任务处理器
│   │   ├── menu_handler.go                    # 菜单处理器
│   │   ├── module_handler.go                  # 模块处理器
│   │   ├── permission_handler.go              # 权限处理器
│   │   ├── project_content_handler.go         # 项目内容处理器
│   │   ├── project_handler.go                 # 项目处理器
│   │   ├── queue_handler.go                   # 队列管理处理器
│   │   ├── research_handler.go                # 检索器处理器
│   │   ├── research_job_handler.go            # 检索任务处理器
│   │   ├── research_language_handler.go       # 检索语言包处理器
│   │   ├── research_task_handler.go           # 检索动作处理器
│   │   ├── role_handler.go                    # 角色处理器
│   │   ├── settings_handler.go                # 系统设置处理器
│   ├── middleware/                            # 中间件
│   │   ├── auth_middleware.go                 # 认证中间件
│   │   ├── operation_log_middleware.go        # 操作日志中间件
│   └── routes/                                # 路由定义
│       ├── admin_routes.go                    # 管理员相关路由
│       ├── article_routes.go                  # 文章相关路由
│       ├── auth_routes.go                     # 认证相关路由
│       ├── domain_routes.go                   # 域名相关路由
│       ├── email_routes.go                    # 邮件相关路由
│       ├── failed_job_routes.go               # 失败任务相关路由
│       ├── friendly_routes.go                 # 友情链接相关路由
│       ├── gateway_routes.go                  # 网关相关路由
│       ├── invitation_routes.go               # 邀请码相关路由
│       ├── job_routes.go                      # 队列任务相关路由
│       ├── menu_routes.go                     # 菜单相关路由
│       ├── module_routes.go                   # 模块相关路由
│       ├── permission_routes.go               # 权限相关路由
│       ├── project_routes.go                  # 项目相关路由
│       ├── projectscontent_routes.go          # 项目内容相关路由
│       ├── queue_routes.go                    # 队列相关路由
│       ├── research_routes.go                 # 检索相关路由
│       ├── role_routes.go                     # 角色相关路由
│       ├── routes.go                          # 路由注册主文件
│       ├── settings_routes.go                 # 系统设置相关路由
├── cmd/                                       # 应用入口
│   ├── server/                                # API服务器入口
│   │   └── main.go                            # API主程序入口，负责启动HTTP服务
│   └── worker/                                # 队列worker入口
│       └── main.go                            # Worker主程序入口，负责启动队列消费进程
├── config/                                    # 应用配置
│   └── config.go                              # 配置结构体和加载逻辑，支持环境变量
├── database/                                  # 数据库连接和迁移
│   ├── database.go                            # 数据库连接与初始化
│   ├── go_fiber.sql                           # MySQL建表脚本
│   ├── 1xs_me.sql                             # 其他SQL脚本
│   └── redis.go                               # Redis连接与初始化
├── docker/                                    # Docker相关配置（可扩展）
├── docs/                                      # API文档与Swagger相关
├── models/                                    # 数据模型定义
│   ├── admin_user.go                          # 管理员用户模型
│   ├── admin_role_users.go                    # 角色-用户关联模型
│   ├── admin_operation_log.go                 # 操作日志模型
│   ├── admin_permission_menu.go               # 权限-菜单关联模型
│   ├── admin_role_menu.go                     # 角色-菜单关联模型
│   ├── admin_role_permission.go               # 角色-权限关联模型
│   ├── article.go                             # 文章模型
│   ├── domain.go                              # 域名模型
│   ├── failed_job.go                          # 失败任务模型
│   ├── friendly.go                            # 友情链接模型
│   ├── invitation.go                          # 邀请码模型
│   ├── job.go                                 # 队列任务模型
│   ├── menu.go                                # 菜单模型
│   ├── migration.go                           # 数据库迁移模型
│   ├── module.go                              # 模块模型
│   ├── permission.go                          # 权限模型
│   ├── project.go                             # 项目模型
│   ├── project_content.go                     # 项目内容模型
│   ├── research.go                            # 检索器模型
│   ├── research_job.go                        # 检索任务模型
│   ├── research_language.go                   # 检索语言包模型
│   ├── research_task.go                       # 检索动作模型
│   ├── role.go                                # 角色模型
│   ├── settings.go                            # 系统设置模型
├── test/                                      # 测试代码
│   ├── admin_user_handler_test.go             # 管理员用户接口测试
│   ├── article_handler_test.go                # 文章接口测试
│   ├── getenv_test.go                         # 环境变量测试
│   ├── project_content_handler_test.go        # 项目内容接口测试
│   ├── project_handler_test.go                # 项目接口测试
│   ├── README.md                              # 测试说明
│   ├── test_helpers.go                        # 测试辅助函数
│   └── test_main.go                           # 测试主入口
├── utils/                                     # 通用工具函数
│   ├── captcha.go                             # 验证码生成工具
│   ├── jwt.go                                 # JWT工具
│   ├── password.go                            # 密码加密工具
│   ├── random.go                              # 随机数工具
│   ├── response.go                            # 项目通用响应体
│   ├── template.go                            # 模板渲染工具
│   ├── time.go                                # 时间处理工具
│   ├── validate.go                            # 结构体校验工具
│   ├── email/                                 # 邮件相关工具
│   │   └── email.go                           # 邮件发送与模板渲染
│   ├── export/                                # 数据导出工具
│   │   └── export.go                          # 通用导出逻辑
│   └── queue/                                 # 队列相关工具
│       ├── client.go                          # 队列客户端封装
│       ├── email_util.go                      # 邮件任务入队工具
│       ├── monitor.go                         # 队列监控服务
│       ├── server.go                          # 队列服务端封装与全局失败钩子
│       ├── processors/                        # 队列任务处理器
│       │   ├── data_export_processor.go       # 数据导出任务处理器
│       │   ├── email_processor.go             # 邮件任务处理器
│       │   ├── file_processor.go              # 文件处理任务处理器
│       │   ├── notification_processor.go      # 通知任务处理器
│       │   └── research_processor.go          # 检索任务处理器
│       └── tasks/                             # 队列任务定义
│           └── tasks.go                       # 所有任务类型与payload定义
├── assets/                                    # 静态资源（图片、字体、邮件模板等，省略明细）
├── .env                                       # 环境变量（不纳入版本控制）
├── .env.test                                  # 测试环境变量
├── .gitignore                                 # Git忽略文件
├── go.mod                                     # Go模块定义
├── go.sum                                     # Go模块依赖校验
├── README.md                                  # 项目说明文档
├── run_project_tips.md                        # 项目运行提示
└── 接口文档.md                                 # 接口文档
```
