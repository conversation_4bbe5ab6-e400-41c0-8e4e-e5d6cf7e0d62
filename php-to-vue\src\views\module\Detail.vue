<template>
  <div class="module-detail">
    <div v-loading="loading" class="detail-container">
      <!-- 页头 -->
      <div class="page-header">
        <div class="title-section">
          <h1 class="page-title">{{ module.title || '模块详情' }}</h1>
          <el-tag v-if="module.level !== undefined" :type="getSecurityLevelType(module.level)" class="security-tag">
            {{ getLevelText(module.level) }}
          </el-tag>
          <el-tag v-if="module.is_share !== undefined" :type="getShareType(module.is_share)" class="share-tag">
            {{ getShareText(module.is_share) }}
          </el-tag>
          <el-tag v-if="module.state !== undefined" :type="module.state ? 'success' : 'info'" class="state-tag">
            {{ module.state ? '启用' : '禁用' }}
          </el-tag>
        </div>
        <div class="button-group">
          <el-button type="primary" icon="el-icon-edit" @click="handleEdit">编辑</el-button>
          <el-button type="danger" icon="el-icon-delete" @click="handleDelete">删除</el-button>
          <el-button icon="el-icon-back" @click="$router.push('/module')">返回列表</el-button>
        </div>
      </div>

      <!-- 基本信息卡片 -->
      <el-card shadow="hover" class="info-card">
        <div slot="header" class="card-header">
          <span>基本信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模块ID">{{ module.id }}</el-descriptions-item>
          <el-descriptions-item label="模块名称">{{ module.title }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ module.created_at || '-' }}</el-descriptions-item>
          <el-descriptions-item label="最后更新">{{ module.updated_at || '-' }}</el-descriptions-item>
          <el-descriptions-item label="安全级别">
            <el-tag :type="getSecurityLevelType(module.level)">{{ getLevelText(module.level) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="共享状态">
            <el-tag :type="getShareType(module.is_share)">{{ getShareText(module.is_share) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-switch
              v-model="module.state"
              :active-value="1"
              :inactive-value="0"
              disabled
            ></el-switch>
            {{ module.state ? '启用' : '禁用' }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ module.description || '无描述' }}</el-descriptions-item>
          <el-descriptions-item label="关键字" :span="2">
            <div v-if="parsedKeys && parsedKeys.length > 0" class="keys-container">
              <el-tag
                v-for="(item, index) in parsedKeys"
                :key="index"
                :type="item.state ? 'success' : 'info'"
                class="key-tag"
              >
                {{ item.key }}
              </el-tag>
            </div>
            <span v-else>无关键字</span>
          </el-descriptions-item>
          <el-descriptions-item label="配置参数" :span="2">
            <div v-if="module.setkeys" class="setkeys-container">
              <pre>{{ formatJSON(module.setkeys) }}</pre>
            </div>
            <span v-else>无配置参数</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ module.remark || '无备注' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 代码内容卡片 -->
      <el-card shadow="hover" class="code-card">
        <div slot="header" class="card-header">
          <span>模块代码</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text"
            @click="copyCode"
          >复制代码</el-button>
        </div>
        <pre class="code-block"><code>{{ module.code || '// 暂无代码' }}</code></pre>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getModule, deleteModule } from '@/api/module'

export default {
  name: 'ModuleDetail',
  data() {
    return {
      loading: false,
      moduleId: null,
      module: {
        id: '',
        title: '',
        description: '',
        level: 0,
        state: 1,
        is_share: 10,
        code: '',
        keys: '',
        setkeys: '',
        remark: '',
        created_at: '',
        updated_at: ''
      },
      parsedKeys: []
    }
  },
  created() {
    this.moduleId = this.$route.params.id
    if (this.moduleId) {
      this.fetchModuleDetail()
    }
  },
  watch: {
    // 监听module.keys变化，自动解析关键字
    'module.keys': {
      handler(newVal) {
        if (newVal) {
          this.parseKeys();
        }
      },
      immediate: false
    }
  },
  methods: {
    // 获取安全级别对应的类型
    getSecurityLevelType(level) {
      if (level === undefined || level === null) return 'info';
      
      // 将0-9的安全级别映射到不同类型
      const levelNumber = Number(level);
      if (levelNumber === 0) return 'info'; // 默认级别
      if (levelNumber >= 1 && levelNumber <= 3) return 'success'; // 低级别
      if (levelNumber >= 4 && levelNumber <= 6) return 'warning'; // 中级别
      if (levelNumber >= 7 && levelNumber <= 9) return 'danger'; // 高级别
      
      return 'info'; // 默认返回info类型
    },
    // 获取安全级别文本
    getLevelText(level) {
      if (level === undefined || level === null) return '默认';
      
      // 直接返回数字级别
      return `${level}级`;
    },
    // 格式化共享状态
    getShareType(share) {
      const map = {
        10: 'info',    // 非公开
        20: 'success', // 公开
        30: 'warning'  // 待审核
      }
      return map[share] || 'info'
    },
    // 获取共享状态文本
    getShareText(share) {
      const map = {
        10: '非公开',
        20: '公开',
        30: '待审核'
      }
      return map[share] || '未知'
    },
    // 获取模块详情
    fetchModuleDetail() {
      this.loading = true
      getModule(this.moduleId)
        .then(response => {
          this.module = response || this.module
          this.parseKeys()
        })
        .catch(error => {
          this.$message.error(`获取模块详情失败: ${error.message || '未知错误'}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 解析关键字
    parseKeys() {
      if (!this.module.keys) {
        this.parsedKeys = [];
        return;
      }
      
      try {
        let keysData = this.module.keys;
        
        // 处理字符串类型的keys
        if (typeof keysData === 'string') {
          // 针对具体的示例格式 "[{\"key\":\"112\",\"state\":1}]"
          // 最简单的方式：针对此特定格式进行处理
          if (keysData.includes('\\\"key\\\"') || keysData.includes('key')) {
            try {
              // 使用JSON.parse尝试解析
              this.parsedKeys = JSON.parse(keysData);
              console.log('使用JSON.parse解析成功');
            } catch (jsonError) {
              console.warn('JSON.parse解析失败，尝试硬编码解析:', jsonError);
              
              // 硬编码方式解析这种特定格式
              const keyMatch = keysData.match(/key\\?":\\?"([^"\\,]+)/);
              if (keyMatch && keyMatch[1]) {
                // 从样例中直接提取关键信息构建数组
                this.parsedKeys = [{
                  key: keyMatch[1], 
                  state: keysData.includes('state\\?":1') || keysData.includes('state":1') ? 1 : 0
                }];
                console.log('使用硬编码方式解析成功:', this.parsedKeys);
              } else {
                console.error('硬编码解析失败');
                this.parsedKeys = [];
              }
            }
          } else {
            // 常规JSON字符串
            try {
              this.parsedKeys = JSON.parse(keysData);
            } catch (e) {
              console.error('解析JSON字符串失败:', e);
              this.parsedKeys = [];
            }
          }
        }
        // 如果已经是数组，直接使用
        else if (Array.isArray(keysData)) {
          this.parsedKeys = keysData;
        }
        // 其他情况，设为空数组
        else {
          this.parsedKeys = [];
        }
        
        // 确保结果是数组
        if (!Array.isArray(this.parsedKeys)) {
          this.parsedKeys = [];
        }
      } catch (e) {
        console.error('解析关键字出错:', e);
        this.parsedKeys = [];
      }
    },
    // 格式化JSON
    formatJSON(jsonString) {
      try {
        if (!jsonString) return '';
        const parsed = JSON.parse(jsonString);
        return JSON.stringify(parsed, null, 2);
      } catch (e) {
        return jsonString;
      }
    },
    // 编辑模块
    handleEdit() {
      this.$router.push(`/module/create?id=${this.moduleId}`)
    },
    // 删除模块
    handleDelete() {
      this.$confirm(`确认删除模块 "${this.module.title}" 吗？删除后不可恢复！`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteModule(this.moduleId)
          .then(() => {
            this.$message.success('删除成功')
            this.$router.push('/module')
          })
          .catch(error => {
            this.$message.error(`删除失败: ${error.message || '未知错误'}`)
          })
          .finally(() => {
            this.loading = false
          })
      }).catch(() => {
        // 取消删除
      })
    },
    // 复制代码
    copyCode() {
      if (!this.module.code) {
        this.$message.warning('没有可复制的代码')
        return
      }
      
      // 创建临时文本区域并复制
      const textArea = document.createElement('textarea')
      textArea.value = this.module.code
      document.body.appendChild(textArea)
      textArea.select()
      
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success('代码已复制到剪贴板')
        } else {
          this.$message.error('复制失败')
        }
      } catch (err) {
        this.$message.error('复制失败: ' + err)
      }
      
      document.body.removeChild(textArea)
    }
  }
}
</script>

<style scoped>
.module-detail {
  padding: 20px;
}

.detail-container {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-section {
  display: flex;
  align-items: center;
}

.page-title {
  margin: 0;
  margin-right: 15px;
  font-size: 24px;
}

.security-tag, .state-tag {
  margin-right: 10px;
}

.info-card, .code-card {
  margin-bottom: 20px;
}

.code-block {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  line-height: 1.5;
  font-family: 'Courier New', Courier, monospace;
  white-space: pre-wrap;
  min-height: 200px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.keys-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.key-tag {
  margin-bottom: 5px;
}

.setkeys-container pre {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 0;
  font-family: 'Courier New', Courier, monospace;
}
</style> 