package dto

// FriendlyListItem 友情链接列表项
type FriendlyListItem struct {
	ID        uint64 `json:"id"`         // 友情链接ID
	Name      string `json:"name"`       // 名称
	Href      string `json:"href"`       // 链接地址
	Thumb     string `json:"thumb"`      // 缩略图
	Type      string `json:"type"`       // 类型
	State     int    `json:"state"`      // 状态
	CreatedAt string `json:"created_at"` // 创建时间
}

// FriendlyResponse 友情链接详情
type FriendlyResponse struct {
	ID        uint64 `json:"id"`         // 友情链接ID
	Name      string `json:"name"`       // 名称
	Href      string `json:"href"`       // 链接地址
	Thumb     string `json:"thumb"`      // 缩略图
	Type      string `json:"type"`       // 类型
	State     int    `json:"state"`      // 状态
	CreatedAt string `json:"created_at"` // 创建时间
	UpdatedAt string `json:"updated_at"` // 更新时间
}

// FriendlyRequest 友情链接创建请求
type FriendlyRequest struct {
	Name  string `json:"name" validate:"required"` // 名称
	Href  string `json:"href" validate:"required,url"` // 链接地址
	Thumb string `json:"thumb"` // 缩略图
	Type  string `json:"type" validate:"required"` // 类型
	State int    `json:"state" validate:"min=0,max=1"` // 状态
}

// FriendlyUpdateRequest 友情链接更新请求
type FriendlyUpdateRequest struct {
	Name  *string `json:"name"` // 名称
	Href  *string `json:"href" validate:"omitempty,url"` // 链接地址
	Thumb *string `json:"thumb"` // 缩略图
	Type  *string `json:"type"` // 类型
	State *int    `json:"state" validate:"omitempty,min=0,max=1"` // 状态
} 