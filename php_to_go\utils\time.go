package utils

import (
	"errors"
	"time"
)

const (
	// 常用时间格式
	DateTimeFormat     = "2006-01-02 15:04:05" // 标准日期时间格式 
	DateFormat         = "2006-01-02"          // 日期格式
	TimeFormat         = "15:04:05"            // 时间格式
	DateTimeFormatISO  = "2006-01-02T15:04:05Z07:00" // ISO8601格式
	DateTimeFormatYMDHM = "2006-01-02 15:04"   // 年月日时分格式
)

// ParseTime 解析时间字符串，支持多种格式
// 如果解析失败返回错误
func ParseTime(timeStr string) (time.Time, error) {
	if timeStr == "" {
		return time.Time{}, errors.New("时间字符串为空")
	}

	// 尝试不同的时间格式
	formats := []string{
		DateTimeFormat,
		DateFormat,
		TimeFormat,
		DateTimeFormatISO,
		DateTimeFormatYMDHM,
		time.RFC3339,
	}

	var err error
	var t time.Time

	for _, format := range formats {
		t, err = time.Parse(format, timeStr)
		if err == nil {
			return t, nil
		}
	}

	// 所有格式都解析失败
	return time.Time{}, errors.New("无法解析时间字符串")
}

// FormatTime 将时间格式化为指定格式的字符串
func FormatTime(t time.Time, format string) string {
	if t.IsZero() {
		return ""
	}
	
	if format == "" {
		format = DateTimeFormat
	}
	
	return t.Format(format)
}

// ParseTimeWithDefault 解析时间字符串，如果解析失败返回默认值
func ParseTimeWithDefault(timeStr string, defaultTime time.Time) time.Time {
	t, err := ParseTime(timeStr)
	if err != nil {
		return defaultTime
	}
	return t
}

// IsValidTimeStr 检查时间字符串是否有效
func IsValidTimeStr(timeStr string) bool {
	_, err := ParseTime(timeStr)
	return err == nil
}

// GetStartOfDay 获取指定时间的当天开始时间 (00:00:00)
func GetStartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// GetEndOfDay 获取指定时间的当天结束时间 (23:59:59)
func GetEndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, t.Location())
}

// GetNow 获取当前时间
func GetNow() time.Time {
	return time.Now()
}

// GetNowStr 获取当前时间字符串
func GetNowStr(format string) string {
	if format == "" {
		format = DateTimeFormat
	}
	return time.Now().Format(format)
} 