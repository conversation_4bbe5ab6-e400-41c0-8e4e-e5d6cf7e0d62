package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"strconv"
	"context"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetDomains 获取域名列表
// @Summary 获取域名列表
// @Description 获取域名列表，支持分页和筛选
// @Tags 域名管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为10"
// @Param domain query string false "域名(模糊搜索)"
// @Param type query int false "类型：10 过滤域名/20 切换使用的域名"
// @Param state query int false "状态"
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.DomainListItem}}
// @Router /admin/domain/index [get]
func GetDomains(c *fiber.Ctx) error {
	// 获取分页参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "10"))
	
	// 获取筛选参数
	domain := c.Query("domain", "")
	domainType := c.Query("type", "")
	state := c.Query("state", "")
	
	// 构建查询
	query := database.DB.Model(&models.Domain{})
	
	// 应用筛选条件
	if domain != "" {
		query = query.Where("domain LIKE ?", "%"+domain+"%")
	}
	
	if domainType != "" {
		typeInt, err := strconv.Atoi(domainType)
		if err == nil {
			query = query.Where("type = ?", typeInt)
		}
	}
	
	if state != "" {
		stateInt, err := strconv.Atoi(state)
		if err == nil {
			query = query.Where("state = ?", stateInt)
		}
	}
	
	// 计算总数
	var total int64
	query.Count(&total)
	
	// 获取分页数据
	var domains []models.Domain
	query.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&domains)
	
	// 转换为DTO
	var domainDTOs []dto.DomainListItem
	for _, domain := range domains {
		domainDTOs = append(domainDTOs, dto.DomainListItem{
			ID:        domain.ID,
			Domain:    domain.Domain,
			Type:      domain.Type,
			State:     domain.State,
			CreatedAt: domain.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	
	// 返回分页响应
	return utils.SuccessPaginated(c, "获取域名列表成功", domainDTOs, total, page, pageSize)
}

// GetDomain 获取单个域名详情
// @Summary 获取域名详情
// @Description 根据ID获取域名详情
// @Tags 域名管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "域名ID"
// @Success 200 {object} dto.StandardResponse{data=dto.DomainResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "域名不存在"
// @Router /admin/domain/index/{id} [get]
func GetDomain(c *fiber.Ctx) error {
	// 获取域名ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的域名ID", err)
	}
	
	// 查询域名
	var domain models.Domain
	if err := database.DB.First(&domain, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "域名不存在")
		}
		return utils.ServerError(c, "获取域名失败", err)
	}
	
	// 转换为DTO
	domainDTO := dto.DomainResponse{
		ID:        domain.ID,
		Domain:    domain.Domain,
		Type:      domain.Type,
		State:     domain.State,
		CreatedAt: domain.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: domain.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "获取域名成功", domainDTO)
}

// CreateDomain 创建域名
// @Summary 创建域名
// @Description 创建新的域名
// @Tags 域名管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param domain body dto.DomainRequest true "域名信息"
// @Success 200 {object} dto.StandardResponse{data=dto.DomainResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/domain/index [post]
func CreateDomain(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.DomainRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 检查域名是否已存在
	var count int64
	if err := database.DB.Model(&models.Domain{}).Where("domain = ?", req.Domain).Count(&count).Error; err != nil {
		return utils.ServerError(c, "检查域名失败", err)
	}
	
	if count > 0 {
		return utils.BadRequest(c, "域名已存在", nil)
	}
	
	// 创建新域名
	domain := models.Domain{
		Domain: req.Domain,
		Type:   req.Type,
		State:  req.State,
	}
	
	if err := database.DB.Create(&domain).Error; err != nil {
		return utils.ServerError(c, "创建域名失败", err)
	}
	
	// 域名过滤：type==10时加入Redis集合
	if domain.Type == 10 {
		database.Rdb.SAdd(context.Background(), "filter_domain", domain.Domain)
	}
	
	// 转换为DTO
	domainDTO := dto.DomainResponse{
		ID:        domain.ID,
		Domain:    domain.Domain,
		Type:      domain.Type,
		State:     domain.State,
		CreatedAt: domain.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: domain.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "创建域名成功", domainDTO)
}

// UpdateDomain 更新域名
// @Summary 更新域名
// @Description 更新现有域名
// @Tags 域名管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "域名ID"
// @Param domain body dto.DomainUpdateRequest true "域名更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.DomainResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "域名不存在"
// @Router /admin/domain/index/{id} [put]
func UpdateDomain(c *fiber.Ctx) error {
	// 获取域名ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的域名ID", err)
	}
	
	// 查询域名
	var domain models.Domain
	if err := database.DB.First(&domain, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "域名不存在")
		}
		return utils.ServerError(c, "获取域名失败", err)
	}
	
	// 解析请求体
	var req dto.DomainUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 准备更新数据
	updates := make(map[string]interface{})
	
	if req.Domain != nil {
		// 检查域名是否已存在（排除自己）
		var count int64
		if err := database.DB.Model(&models.Domain{}).Where("domain = ? AND id != ?", *req.Domain, id).Count(&count).Error; err != nil {
			return utils.ServerError(c, "检查域名失败", err)
		}
		
		if count > 0 {
			return utils.BadRequest(c, "域名已存在", nil)
		}
		
		updates["domain"] = *req.Domain
	}
	
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	
	if req.State != nil {
		updates["state"] = *req.State
	}
	
	// 执行更新
	if err := database.DB.Model(&domain).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新域名失败", err)
	}
	
	// 重新获取更新后的域名
	if err := database.DB.First(&domain, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的域名失败", err)
	}
	
	// 域名过滤：type==10时加入Redis集合
	if domain.Type == 10 {
		database.Rdb.SAdd(context.Background(), "filter_domain", domain.Domain)
	}
	
	// 转换为DTO
	domainDTO := dto.DomainResponse{
		ID:        domain.ID,
		Domain:    domain.Domain,
		Type:      domain.Type,
		State:     domain.State,
		CreatedAt: domain.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: domain.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "更新域名成功", domainDTO)
}

// DeleteDomain 删除域名
// @Summary 删除域名
// @Description 删除现有域名
// @Tags 域名管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "域名ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "域名不存在"
// @Router /admin/domain/index/{id} [delete]
func DeleteDomain(c *fiber.Ctx) error {
	// 获取域名ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的域名ID", err)
	}
	
	// 查询域名
	var domain models.Domain
	if err := database.DB.First(&domain, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "域名不存在")
		}
		return utils.ServerError(c, "获取域名失败", err)
	}
	
	// 软删除域名
	if err := database.DB.Delete(&domain).Error; err != nil {
		return utils.ServerError(c, "删除域名失败", err)
	}
	
	return utils.Success(c, "删除域名成功", nil)
} 