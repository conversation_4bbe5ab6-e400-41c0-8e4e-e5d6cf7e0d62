import request from './index'

/**
 * 获取项目内容详情
 * @param {number} userid - 用户ID
 * @param {number} projectid - 项目ID
 * @returns {Promise}
 */
export function getProjectContent(userid, projectid) {
  return request({
    url: `/admin/project/my/${userid}/${projectid}`,
    method: 'get'
  })
}

/**
 * 删除项目内容
 * @param {number} userid - 用户ID
 * @param {number} projectid - 项目ID
 * @returns {Promise}
 */
export function deleteProjectContent(userid, projectid) {
  return request({
    url: `/admin/project/my/${userid}/${projectid}`,
    method: 'delete'
  })
}

/**
 * 获取项目内容列表
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @param {number} [params.project_id] - 项目ID过滤
 * @returns {Promise}
 */
export function getProjectContentList(params) {
  return request({
    url: '/admin/projectscontent/index',
    method: 'get',
    params
  })
}

/**
 * 创建项目内容
 * @param {Object} data - 项目内容数据
 * @param {string} data.content - 内容
 * @param {string} data.domain - 域名
 * @param {string} data.hash - 哈希值
 * @param {number} data.project_id - 项目ID
 * @param {string} data.server - 服务器
 * @param {number} data.state - 状态(0:禁用, 1:启用)
 * @returns {Promise}
 */
export function createProjectContent(data) {
  return request({
    url: '/admin/projectscontent/index',
    method: 'post',
    data
  })
}

/**
 * 获取项目内容详情
 * @param {number} id - 内容ID
 * @returns {Promise}
 */
export function getProjectContentDetail(id) {
  return request({
    url: `/admin/projectscontent/index/${id}`,
    method: 'get'
  })
}

/**
 * 更新项目内容
 * @param {number} id - 内容ID
 * @param {Object} data - 更新数据
 * @param {string} [data.content] - 内容
 * @param {string} [data.domain] - 域名
 * @param {string} [data.hash] - 哈希值
 * @param {string} [data.server] - 服务器
 * @param {number} [data.state] - 状态(0:禁用, 1:启用)
 * @returns {Promise}
 */
export function updateProjectContent(id, data) {
  return request({
    url: `/admin/projectscontent/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除项目内容
 * @param {number} id - 内容ID
 * @returns {Promise}
 */
export function deleteProjectContentById(id) {
  return request({
    url: `/admin/projectscontent/index/${id}`,
    method: 'delete'
  })
}

/**
 * 预览项目
 * @param {number} projectId - 项目ID
 * @returns {Promise}
 */
export function previewProject(projectId) {
  return request({
    url: '/admin/project/my/preview',
    method: 'get',
    params: {
      project_id: projectId
    }
  })
} 

/**
 * 恢复被删除的项目内容
 * @param {number} id - 内容ID
 * @returns {Promise}
 */
export function restoreProjectContent(id) {
  return request({
    url: `/admin/projectscontent/restore/${id}`,
    method: 'put'
  })
} 