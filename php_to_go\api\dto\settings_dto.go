package dto

// SettingsResponse 网站设置响应
type SettingsResponse struct {
	SiteName       string `json:"site_name"`        // 网站名称
	SiteKeywords   string `json:"site_keywords"`    // 网站关键词
	SiteDescription string `json:"site_description"` // 网站描述
	SiteLogo       string `json:"site_logo"`        // 网站Logo
	SiteFavicon    string `json:"site_favicon"`     // 网站Favicon
	SiteFooter     string `json:"site_footer"`      // 网站页脚
	SiteNotice     string `json:"site_notice"`      // 网站公告
	SiteClosed     bool   `json:"site_closed"`      // 网站是否关闭
	ClosedMessage  string `json:"closed_message"`   // 关闭提示信息
	RegisterClosed bool   `json:"register_closed"`  // 是否关闭注册
	MailHost       string `json:"mail_host"`        // 邮件服务器地址
	MailPort       int    `json:"mail_port"`        // 邮件服务器端口
	MailUsername   string `json:"mail_username"`    // 邮件用户名
	MailPassword   string `json:"mail_password"`    // 邮件密码
	MailFromName   string `json:"mail_from_name"`   // 发件人名称
	MailFromEmail  string `json:"mail_from_email"`  // 发件人邮箱
}

// SettingsUpdateRequest 网站设置更新请求
type SettingsUpdateRequest struct {
	SiteName        *string `json:"site_name"`         // 网站名称
	SiteKeywords    *string `json:"site_keywords"`     // 网站关键词
	SiteDescription *string `json:"site_description"`  // 网站描述
	SiteLogo        *string `json:"site_logo"`         // 网站Logo
	SiteFavicon     *string `json:"site_favicon"`      // 网站Favicon
	SiteFooter      *string `json:"site_footer"`       // 网站页脚
	SiteNotice      *string `json:"site_notice"`       // 网站公告
	SiteClosed      *bool   `json:"site_closed"`       // 网站是否关闭
	ClosedMessage   *string `json:"closed_message"`    // 关闭提示信息
	RegisterClosed  *bool   `json:"register_closed"`   // 是否关闭注册
	MailHost        *string `json:"mail_host"`         // 邮件服务器地址
	MailPort        *int    `json:"mail_port"`         // 邮件服务器端口
	MailUsername    *string `json:"mail_username"`     // 邮件用户名
	MailPassword    *string `json:"mail_password"`     // 邮件密码
	MailFromName    *string `json:"mail_from_name"`    // 发件人名称
	MailFromEmail   *string `json:"mail_from_email"`   // 发件人邮箱
} 