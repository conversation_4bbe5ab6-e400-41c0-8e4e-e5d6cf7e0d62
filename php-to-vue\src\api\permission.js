import request from './index'

/**
 * 获取权限列表
 * @param {Object} params - 查询参数
 * @param {string} [params.name] - 权限名称(模糊查询)
 * @param {number} [params.parent_id] - 父权限ID
 * @returns {Promise}
 */
export function getPermissionList(params) {
  return request({
    url: '/admin/permissions',
    method: 'get',
    params
  })
}

/**
 * 创建权限
 * @param {Object} data - 权限数据
 * @param {string} data.name - 权限名称
 * @param {string} data.slug - 权限标识
 * @param {string} [data.http_method] - HTTP方法
 * @param {string} [data.http_path] - HTTP路径
 * @param {number} [data.order] - 排序
 * @param {number} [data.parent_id] - 父权限ID
 * @param {Array} [data.menu_ids] - 关联菜单ID列表
 * @param {Array} [data.role_ids] - 关联角色ID列表
 * @returns {Promise}
 */
export function createPermission(data) {
  return request({
    url: '/admin/permissions',
    method: 'post',
    data
  })
}

/**
 * 获取权限详情
 * @param {number} id - 权限ID
 * @returns {Promise}
 */
export function getPermissionDetail(id) {
  return request({
    url: `/admin/permissions/${id}`,
    method: 'get'
  })
}

/**
 * 更新权限
 * @param {number} id - 权限ID
 * @param {Object} data - 权限数据
 * @param {string} [data.name] - 权限名称
 * @param {string} [data.slug] - 权限标识
 * @param {string} [data.http_method] - HTTP方法
 * @param {string} [data.http_path] - HTTP路径
 * @param {number} [data.order] - 排序
 * @param {number} [data.parent_id] - 父权限ID
 * @param {Array} [data.menu_ids] - 关联菜单ID列表
 * @param {Array} [data.role_ids] - 关联角色ID列表
 * @returns {Promise}
 */
export function updatePermission(id, data) {
  return request({
    url: `/admin/permissions/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除权限
 * @param {number} id - 权限ID
 * @returns {Promise}
 */
export function deletePermission(id) {
  return request({
    url: `/admin/permissions/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除权限
 * @param {Array} ids - 权限ID列表
 * @returns {Promise}
 */
export function batchDeletePermissions(ids) {
  return request({
    url: '/admin/permissions/batch-delete',
    method: 'post',
    data: { ids }
  })
}

/**
 * 获取权限树形结构
 * @param {Object} params - 查询参数
 * @param {string} [params.exclude_ids] - 排除的权限ID，多个ID用逗号分隔
 * @returns {Promise}
 */
export function getPermissionTree(params) {
  return request({
    url: '/admin/permissions/tree',
    method: 'get',
    params
  })
}

/**
 * 检查用户是否拥有特定权限
 * @param {string} slug - 权限标识
 * @returns {Promise<boolean>}
 */
export function checkPermission(slug) {
  return request({
    url: `/admin/permissions/check/${slug}`,
    method: 'get'
  })
} 