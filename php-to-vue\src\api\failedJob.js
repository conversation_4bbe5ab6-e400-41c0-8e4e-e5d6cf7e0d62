import request from './index'

/**
 * 获取失败任务列表
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getFailedJobs(params) {
  return request({
    url: '/admin/failed-job/index',
    method: 'get',
    params
  })
}

/**
 * 获取失败任务详情
 * @param {number} id - 失败任务ID
 * @returns {Promise}
 */
export function getFailedJob(id) {
  return request({
    url: `/admin/failed-job/index/${id}`,
    method: 'get'
  })
}

/**
 * 创建失败任务
 * @param {Object} data - 失败任务数据
 * @returns {Promise}
 */
export function createFailedJob(data) {
  return request({
    url: '/admin/failed-job/index',
    method: 'post',
    data
  })
}

/**
 * 更新失败任务
 * @param {number} id - 失败任务ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateFailedJob(id, data) {
  return request({
    url: `/admin/failed-job/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除失败任务
 * @param {number} id - 失败任务ID
 * @returns {Promise}
 */
export function deleteFailedJob(id) {
  return request({
    url: `/admin/failed-job/index/${id}`,
    method: 'delete'
  })
}

/**
 * 重试失败任务
 * @param {number} id - 失败任务ID
 * @returns {Promise}
 */
export function retryFailedJob(id) {
  return request({
    url: `/admin/failed-job/retry/${id}`,
    method: 'post'
  })
}

/**
 * 清空所有失败任务
 * @returns {Promise}
 */
export function clearAllFailedJobs() {
  return request({
    url: '/admin/failed-job/clear-all',
    method: 'delete'
  })
} 