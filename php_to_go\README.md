# Go Fiber API

一个基于Go Fiber框架的RESTful API项目脚手架。


## 开始使用

### 环境要求

- Go 1.16+
- MySQL 5.7+ 或其他GORM支持的数据库

### 安装步骤

1. 克隆项目:

```bash
git clone https://github.com/yourusername/go-fiber-api.git
cd go-fiber-api
```

2. 安装依赖:

```bash
go mod download
```

3. 创建环境变量文件:

```bash
cp .env.example .env
```

4. 修改`.env`文件中的数据库配置

5. 运行项目:

```bash
go run cmd/server/main.go
```

6. 生成接口doc

```bash
swag init -g cmd/server/main.go
```
运行：http://localhost:3000/swagger/index.html#/

服务器将启动在 `http://localhost:3000`

## 部署

项目可以构建为独立二进制文件部署:

```bash
go build -o api-server cmd/server/main.go
./api-server
```

## 队列系统

本项目使用Asynq作为异步任务队列系统，用于处理邮件发送、通知推送、文件处理和数据导出等耗时操作。

### 启动队列工作进程

```bash
# 启动工作进程，默认10个并发
go run cmd/worker/main.go

# 指定并发数
go run cmd/worker/main.go -concurrency=20

# 同时启用监控服务
go run cmd/worker/main.go -monitor -monitor-addr=":8080"
```

### 启动队列监控服务和webui

docker run --rm -p 8080:8080 \
-e REDIS_ADDR=host.docker.internal:6379 \
hibiken/asynqmon --redis-addr=host.docker.internal:6379

访问地址：http://localhost:8080/

### 添加新任务类型

1. 在`utils/queue/tasks/tasks.go`中定义新的任务类型和负载结构体
2. 在`utils/queue/processors`目录下创建对应的处理器
3. 在`utils/queue/server.go`的`RegisterHandlers`函数中注册处理器
4. 创建API处理器用于创建和入队任务 