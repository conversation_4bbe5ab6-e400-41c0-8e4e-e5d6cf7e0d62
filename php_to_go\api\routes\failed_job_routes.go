package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupFailedJobRoutes 设置失败任务相关路由
func SetupFailedJobRoutes(router fiber.Router) {
	// 失败任务路由组
	failedJob := router.Group("/failed-job")
	
	// 应用认证中间件
	failedJob.Use(utils.RequireAuthentication)
	
	// 获取失败任务列表
	failedJob.Get("/index", middleware.RequirePermission("failedjob.view"), handlers.GetFailedJobs)
	
	// 获取单个失败任务详情
	failedJob.Get("/index/:id", middleware.RequirePermission("failedjob.view"), handlers.GetFailedJob)
	
	// 创建失败任务
	failedJob.Post("/index", middleware.RequirePermission("failedjob.create"), handlers.CreateFailedJob)
	
	// 更新失败任务
	failedJob.Put("/index/:id", middleware.RequirePermission("failedjob.edit"), handlers.UpdateFailedJob)
	
	// 删除失败任务
	failedJob.Delete("/index/:id", middleware.RequirePermission("failedjob.delete"), handlers.DeleteFailedJob)
	
	// 重试失败任务
	failedJob.Post("/retry/:id", middleware.RequirePermission("failedjob.retry"), handlers.RetryFailedJob)
	
	// 清空所有失败任务
	failedJob.Delete("/clear-all", middleware.RequirePermission("failedjob.delete"), handlers.ClearAllFailedJobs)
} 