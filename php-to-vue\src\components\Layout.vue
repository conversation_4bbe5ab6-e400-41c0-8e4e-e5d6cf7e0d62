<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <el-header height="60px" class="header">
      <div class="logo">
        <span class="logo-text">XSS测试平台</span>
      </div>
      <div class="header-right">
        <!-- 通知中心 -->
        <notification-center class="notification-component" />

        <el-dropdown trigger="click" @command="handleCommand">
          <span class="el-dropdown-link">
            <i class="el-icon-user"></i>
            {{ username }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="profile">个人资料</el-dropdown-item>
            <el-dropdown-item command="settings">系统设置</el-dropdown-item>
            <el-dropdown-item divided command="logout"
              >退出登录</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="main-container">
      <!-- 侧边栏导航 -->
      <el-aside width="220px" class="sidebar">
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          background-color="#001529"
          text-color="#fff"
          active-text-color="#409EFF"
          :collapse="isCollapse"
          :collapse-transition="false"
          router
        >
          <!-- 使用动态菜单组件 -->
          <dynamic-menu
            :menu-items="menuTree"
            v-if="menuTree.length > 0"
            @menu-click="handleMenuClick"
          ></dynamic-menu>

          <!-- 加载中状态 -->
          <div v-else class="menu-loading">
            <el-skeleton :rows="8" animated />
          </div>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <slot></slot>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { mapActions, mapState } from "vuex";
import NotificationCenter from "@/components/NotificationCenter.vue";
import DynamicMenu from "@/components/DynamicMenu.vue";
import { getUserMenuTree } from "@/api/menu";

export default {
  name: "Layout",
  components: {
    NotificationCenter,
    DynamicMenu,
  },
  data() {
    return {
      isCollapse: false,
      menuTree: [], // 菜单树数据
    };
  },
  computed: {
    ...mapState(["user"]),
    activeMenu() {
      return this.$route.path;
    },
    username() {
      return this.user ? this.user.username : "用户";
    },
    isAdmin() {
      // 检查用户是否为管理员
      return this.user && this.user.role === 1;
    },
  },
  methods: {
    ...mapActions(["logout"]),
    // 处理下拉菜单命令
    handleCommand(command) {
      if (command === "logout") {
        this.handleLogout();
      } else if (command === "profile") {
        this.$router.push("/user/profile");
      } else if (command === "settings") {
        this.$router.push("/settings");
      }
    },
    // 处理退出登录
    handleLogout() {
      this.logout().then(() => {
        // 清除可能的错误消息
        localStorage.removeItem('lastError');
        
        // 显示成功消息
        this.$message.success("已退出登录");
        
        // 使用replace而不是push，避免用户可以返回到需要认证的页面
        this.$router.replace("/login");
      }).catch(error => {
        console.error("退出登录时发生错误:", error);
        this.$message.error("退出登录时发生错误，请重试");
      });
    },
    // 获取菜单数据
    fetchMenuData() {
      // 检查用户信息
      const userInfo = localStorage.getItem("user");
      let isAdmin = false;
      let userRole = 0;

      if (userInfo) {
        try {
          const user = JSON.parse(userInfo);
          userRole = user ? user.role || 0 : 0;
          isAdmin = user && (user.role === 1 || user.is_admin === true);
          console.log("当前用户信息:", JSON.stringify(user));
          console.log("是否管理员:", isAdmin, "角色ID:", userRole);
        } catch (error) {
          console.error("解析用户信息失败:", error);
        }
      }

      console.log("开始请求菜单数据...");
      getUserMenuTree()
        .then((response) => {
          console.log("获取到的原始菜单数据:", JSON.stringify(response));

          if (response && response.length > 0) {
            console.log("菜单数据有效，共", response.length, "个顶级菜单");
            // 打印每个顶级菜单的名称
            response.forEach((menu, index) => {
              console.log(
                `顶级菜单${index + 1}:`,
                menu.title,
                "子菜单数量:",
                menu.children ? menu.children.length : 0
              );
            });
            this.menuTree = response;
          }
        })
        .catch((error) => {
          console.error("获取菜单数据出错:", error.message, error.stack);
          this.$message.error("获取菜单数据失败");

          // 显示基本菜单
          this.menuTree = [
            {
              id: 1,
              title: "控制面板",
              icon: "el-icon-s-home",
              uri: "/",
              children: [],
            },
            {
              id: 10,
              title: "关于系统",
              icon: "el-icon-info",
              uri: "/about",
              children: [],
            },
          ];
        });
    },
    // 处理菜单点击
    handleMenuClick(item) {
      if (item.uri) {
        console.log("菜单点击：", item.title, "URI:", item.uri);
        
        try {
          // 检查是否为绝对路径
          if (item.uri.startsWith('http://') || item.uri.startsWith('https://')) {
            // 外部链接，直接在新窗口打开
            window.open(item.uri, '_blank');
          } else {
            // 内部路由，使用router.push
            this.$router.push(item.uri).catch(err => {
              console.error("路由跳转错误:", err);
              // 如果是重复导航错误，可以忽略
              if (err.name !== 'NavigationDuplicated') {
                this.$message.error("页面跳转失败，请检查路由配置");
              }
            });
          }
        } catch (error) {
          console.error("菜单点击处理错误:", error);
          this.$message.error("页面跳转出错");
        }
      }
    },
  },
  created() {
    // 组件创建时获取菜单数据
    this.fetchMenuData();
  },
};
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #001529;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
}

.header-right {
  display: flex;
  align-items: center;
}

.notification-component {
  margin-right: 20px;
}

.el-dropdown-link {
  color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.main-container {
  display: flex;
  flex: 1;
  background-color: #f0f2f5;
}

.sidebar {
  background-color: #001529;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.el-menu-vertical {
  height: 100%;
  border-right: none;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 220px;
}

.main-content {
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 60px);
}
</style>
