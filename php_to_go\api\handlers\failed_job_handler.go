package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetFailedJobs 获取失败任务列表
// @Summary 获取失败任务列表
// @Description 获取失败任务列表，支持分页和筛选
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param queue query string false "队列名称(模糊搜索)"
// @Param connection query string false "连接名称(模糊搜索)"
// @Param start_time query string false "开始时间(格式: 2006-01-02 15:04:05)"
// @Param end_time query string false "结束时间(格式: 2006-01-02 15:04:05)"
// @Param page query int false "页码(默认: 1)"
// @Param page_size query int false "每页数量(默认: 10)"
// @Success 200 {object} dto.StandardResponse{data=[]dto.FailedJobListItem}
// @Router /admin/failed-job/index [get]
func GetFailedJobs(c *fiber.Ctx) error {
	// 解析查询参数
	query := new(dto.FailedJobQuery)
	if err := c.QueryParser(query); err != nil {
		return utils.BadRequest(c, "无效的查询参数", err)
	}

	// 设置默认分页参数
	page := query.Page
	if page <= 0 {
		page = 1
	}

	pageSize := query.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 构建查询
	db := database.DB.Model(&models.FailedJob{})

	// 应用筛选条件
	if query.Queue != "" {
		db = db.Where("queue LIKE ?", "%"+query.Queue+"%")
	}

	if query.Connection != "" {
		db = db.Where("connection LIKE ?", "%"+query.Connection+"%")
	}

	// 时间筛选
	if query.StartTime != nil && *query.StartTime != "" {
		// 使用时间工具类解析开始时间
		startTime, err := utils.ParseTime(*query.StartTime)
		if err == nil {
			db = db.Where("failed_at >= ?", startTime)
		}
	}

	if query.EndTime != nil && *query.EndTime != "" {
		// 使用时间工具类解析结束时间
		endTime, err := utils.ParseTime(*query.EndTime)
		if err == nil {
			// 如果是日期格式，自动设置为当天结束时间
			if len(*query.EndTime) <= 10 {
				endTime = utils.GetEndOfDay(endTime)
			}
			db = db.Where("failed_at <= ?", endTime)
		}
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return utils.ServerError(c, "获取失败任务总数失败", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	var failedJobs []models.FailedJob
	if err := db.Order("id DESC").Offset(offset).Limit(pageSize).Find(&failedJobs).Error; err != nil {
		return utils.ServerError(c, "获取失败任务列表失败", err)
	}

	// 转换为DTO
	var failedJobDTOs []dto.FailedJobListItem
	for _, job := range failedJobs {
		failedJobDTOs = append(failedJobDTOs, dto.FailedJobListItem{
			ID:         job.ID,
			Connection: job.Connection,
			Queue:      job.Queue,
			Exception:  job.Exception,
			FailedAt:   job.FailedAt,
		})
	}

	// 计算总页数
	totalPages := int(total) / pageSize
	if int(total)%pageSize > 0 {
		totalPages++
	}

	// 返回分页响应
	return utils.SuccessPaginated(c, "获取失败任务列表成功", failedJobDTOs, total, page, pageSize)
}

// GetFailedJob 获取单个失败任务详情
// @Summary 获取失败任务详情
// @Description 根据ID获取失败任务详情
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "失败任务ID"
// @Success 200 {object} dto.StandardResponse{data=dto.FailedJobResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "失败任务不存在"
// @Router /admin/failed-job/index/{id} [get]
func GetFailedJob(c *fiber.Ctx) error {
	// 获取任务ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的失败任务ID", err)
	}

	// 查询任务
	var failedJob models.FailedJob
	if err := database.DB.First(&failedJob, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "失败任务不存在")
		}
		return utils.ServerError(c, "获取失败任务失败", err)
	}

	// 转换为DTO
	response := dto.FailedJobResponse{
		ID:         failedJob.ID,
		Connection: failedJob.Connection,
		Queue:      failedJob.Queue,
		Payload:    failedJob.Payload,
		Exception:  failedJob.Exception,
		FailedAt:   failedJob.FailedAt,
	}

	return utils.Success(c, "获取失败任务成功", response)
}

// CreateFailedJob 创建失败任务
// @Summary 创建失败任务
// @Description 创建新的失败任务
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param failedJob body dto.FailedJobCreateRequest true "失败任务信息"
// @Success 200 {object} dto.StandardResponse{data=dto.FailedJobResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/failed-job/index [post]
func CreateFailedJob(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.FailedJobCreateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 创建新失败任务
	failedJob := models.FailedJob{
		Connection: req.Connection,
		Queue:      req.Queue,
		Payload:    req.Payload,
		Exception:  req.Exception,
		FailedAt:   time.Now(),
	}

	if err := database.DB.Create(&failedJob).Error; err != nil {
		return utils.ServerError(c, "创建失败任务失败", err)
	}

	// 转换为DTO
	response := dto.FailedJobResponse{
		ID:         failedJob.ID,
		Connection: failedJob.Connection,
		Queue:      failedJob.Queue,
		Payload:    failedJob.Payload,
		Exception:  failedJob.Exception,
		FailedAt:   failedJob.FailedAt,
	}

	return utils.Success(c, "创建失败任务成功", response)
}

// UpdateFailedJob 更新失败任务
// @Summary 更新失败任务
// @Description 更新现有失败任务
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "失败任务ID"
// @Param failedJob body dto.FailedJobUpdateRequest true "失败任务更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.FailedJobResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "失败任务不存在"
// @Router /admin/failed-job/index/{id} [put]
func UpdateFailedJob(c *fiber.Ctx) error {
	// 获取任务ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的失败任务ID", err)
	}

	// 查询任务
	var failedJob models.FailedJob
	if err := database.DB.First(&failedJob, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "失败任务不存在")
		}
		return utils.ServerError(c, "获取失败任务失败", err)
	}

	// 解析请求体
	var req dto.FailedJobUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 准备更新数据
	updates := make(map[string]interface{})

	if req.Connection != nil {
		updates["connection"] = *req.Connection
	}

	if req.Queue != nil {
		updates["queue"] = *req.Queue
	}

	if req.Payload != nil {
		updates["payload"] = *req.Payload
	}

	if req.Exception != nil {
		updates["exception"] = *req.Exception
	}

	// 更新任务
	if err := database.DB.Model(&failedJob).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新失败任务失败", err)
	}

	// 重新获取更新后的任务
	if err := database.DB.First(&failedJob, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的失败任务失败", err)
	}

	// 转换为DTO
	response := dto.FailedJobResponse{
		ID:         failedJob.ID,
		Connection: failedJob.Connection,
		Queue:      failedJob.Queue,
		Payload:    failedJob.Payload,
		Exception:  failedJob.Exception,
		FailedAt:   failedJob.FailedAt,
	}

	return utils.Success(c, "更新失败任务成功", response)
}

// DeleteFailedJob 删除失败任务
// @Summary 删除失败任务
// @Description 删除现有失败任务
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "失败任务ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "失败任务不存在"
// @Router /admin/failed-job/index/{id} [delete]
func DeleteFailedJob(c *fiber.Ctx) error {
	// 获取任务ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的失败任务ID", err)
	}

	// 查询任务
	var failedJob models.FailedJob
	if err := database.DB.First(&failedJob, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "失败任务不存在")
		}
		return utils.ServerError(c, "获取失败任务失败", err)
	}

	// 删除任务
	if err := database.DB.Delete(&failedJob).Error; err != nil {
		return utils.ServerError(c, "删除失败任务失败", err)
	}

	return utils.Success(c, "删除失败任务成功", nil)
}

// RetryFailedJob 重试失败任务
// @Summary 重试失败任务
// @Description 重试失败的任务
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "失败任务ID"
// @Success 200 {object} dto.StandardResponse "重试成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "失败任务不存在"
// @Router /admin/failed-job/retry/{id} [post]
func RetryFailedJob(c *fiber.Ctx) error {
	// 获取任务ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的失败任务ID", err)
	}

	// 查询任务
	var failedJob models.FailedJob
	if err := database.DB.First(&failedJob, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "失败任务不存在")
		}
		return utils.ServerError(c, "获取失败任务失败", err)
	}

	// 创建新的Job
	job := models.Job{
		Queue:       failedJob.Queue,
		Payload:     failedJob.Payload,
		Attempts:    0,
		ReservedAt:  nil,
		AvailableAt: uint(time.Now().Unix()),
		CreatedAt:   uint(time.Now().Unix()),
	}

	// 事务处理
	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 创建新的Job
		if err := tx.Create(&job).Error; err != nil {
			return err
		}

		// 删除失败任务
		if err := tx.Delete(&failedJob).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "重试失败任务失败", err)
	}

	return utils.Success(c, "重试失败任务成功", nil)
}

// ClearAllFailedJobs 清空所有失败任务
// @Summary 清空所有失败任务
// @Description 清空所有失败任务
// @Tags 任务管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse "清空成功"
// @Router /admin/failed-job/clear-all [delete]
func ClearAllFailedJobs(c *fiber.Ctx) error {
	// 执行清空操作
	if err := database.DB.Exec("TRUNCATE TABLE failed_jobs").Error; err != nil {
		return utils.ServerError(c, "清空失败任务失败", err)
	}

	return utils.Success(c, "清空失败任务成功", nil)
} 