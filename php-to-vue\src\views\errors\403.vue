<template>
  <div class="error-container">
    <div class="error-card">
      <div class="error-icon">
        <i class="el-icon-warning-outline"></i>
      </div>
      <h1 class="error-title">403</h1>
      <h2 class="error-subtitle">权限不足</h2>
      <p class="error-description">
        您没有权限访问此页面，请联系管理员获取权限
      </p>
      <div class="error-actions">
        <el-button type="primary" @click="goBack">返回上一页</el-button>
        <el-button @click="goHome">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Forbidden',
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goHome() {
      this.$router.push('/')
    }
  }
}
</script>

<style scoped>
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 120px);
  padding: 20px;
}

.error-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 500px;
  text-align: center;
}

.error-icon {
  font-size: 60px;
  color: #F56C6C;
  margin-bottom: 20px;
}

.error-title {
  font-size: 80px;
  font-weight: bold;
  color: #F56C6C;
  margin: 0;
  line-height: 1;
}

.error-subtitle {
  font-size: 24px;
  color: #303133;
  margin: 10px 0 20px;
}

.error-description {
  color: #606266;
  margin-bottom: 30px;
  font-size: 16px;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style> 