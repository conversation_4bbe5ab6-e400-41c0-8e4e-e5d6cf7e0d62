<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <span>编辑菜单</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" v-loading="loading">
        <el-form-item label="上级菜单" prop="parent_id">
          <el-select v-model="form.parent_id" placeholder="请选择上级菜单" style="width: 100%">
            <el-option label="顶级菜单" :value="0" />
            <el-option 
              v-for="item in menuOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
              :disabled="item.id === Number($route.query.id)" 
            />
          </el-select>
        </el-form-item>

        <el-form-item label="菜单标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入菜单标题" />
        </el-form-item>

        <el-form-item label="菜单图标" prop="icon">
          <el-input v-model="form.icon" placeholder="请选择菜单图标">
            <template slot="prepend">
              <i :class="form.icon || 'el-icon-menu'" style="width: 20px;" />
            </template>
            <el-button slot="append" @click="showIconSelector = true">选择图标</el-button>
          </el-input>
        </el-form-item>

        <el-form-item label="菜单排序" prop="order">
          <el-input-number v-model="form.order" :min="0" :max="9999" controls-position="right" />
        </el-form-item>

        <el-form-item label="菜单路径" prop="uri">
          <el-input v-model="form.uri" placeholder="请输入菜单路径" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="submitLoading" @click="submitForm">保 存</el-button>
          <el-button @click="cancel">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图标选择器对话框 -->
    <el-dialog
      title="选择图标"
      :visible.sync="showIconSelector"
      width="800px"
      append-to-body
    >
      <div class="icon-container">
        <div
          v-for="(icon, index) in iconList"
          :key="index"
          class="icon-item"
          @click="selectIcon(icon)"
        >
          <i :class="icon" />
          <span>{{ icon }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMenuDetail, updateMenu, getMenuList } from '@/api/menu'

export default {
  name: 'MenuEdit',
  data() {
    return {
      // 表单参数
      form: {
        parent_id: 0,
        title: '',
        icon: '',
        order: 0,
        uri: '',
        role_ids: [],
        permission_ids: []
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: '菜单标题不能为空', trigger: 'blur' }
        ]
      },
      // 菜单选项
      menuOptions: [],
      // 菜单ID
      menuId: null,
      // 加载状态
      loading: false,
      // 提交状态
      submitLoading: false,
      // 是否显示图标选择器
      showIconSelector: false,
      // 图标列表
      iconList: [
        'el-icon-menu',
        'el-icon-s-home',
        'el-icon-s-custom',
        'el-icon-s-grid',
        'el-icon-s-tools',
        'el-icon-s-marketing',
        'el-icon-s-platform',
        'el-icon-s-operation',
        'el-icon-s-data',
        'el-icon-s-cooperation',
        'el-icon-s-order',
        'el-icon-s-release',
        'el-icon-s-ticket',
        'el-icon-s-management',
        'el-icon-s-open',
        'el-icon-s-shop',
        'el-icon-s-help',
        'el-icon-picture',
        'el-icon-camera',
        'el-icon-video-camera',
        'el-icon-document',
        'el-icon-setting',
        'el-icon-user',
        'el-icon-phone',
        'el-icon-more'
      ]
    }
  },
  created() {
    this.menuId = Number(this.$route.query.id)
    if (!this.menuId) {
      this.$message.error('参数错误')
      this.goBack()
      return
    }
    
    this.loading = true
    Promise.all([
      this.getMenuOptions(),
      this.getMenuInfo()
    ]).then(() => {
      this.loading = false
    }).catch(() => {
      this.loading = false
      this.$message.error('获取数据失败')
      this.goBack()
    })
  },
  methods: {
    /** 获取菜单选项 */
    getMenuOptions() {
      return getMenuList().then(response => {
        this.menuOptions = response || []
      })
    },
    /** 获取菜单信息 */
    getMenuInfo() {
      return getMenuDetail(this.menuId).then(response => {
        if (!response) {
          this.$message.error('获取菜单信息失败')
          this.goBack()
          return
        }
        
        this.form = {
          parent_id: response.parent_id || 0,
          title: response.title || '',
          icon: response.icon || '',
          order: response.order || 0,
          uri: response.uri || '',
          role_ids: response.role_ids || [],
          permission_ids: response.permission_ids || []
        }
      })
    },
    /** 表单提交 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.submitLoading = true
          updateMenu(this.menuId, this.form).then(() => {
            this.$message.success('更新成功')
            this.goBack()
          }).catch(error => {
            console.error('更新菜单失败:', error)
          }).finally(() => {
            this.submitLoading = false
          })
        }
      })
    },
    /** 取消按钮 */
    cancel() {
      this.goBack()
    },
    /** 返回按钮操作 */
    goBack() {
      this.$router.push({ path: '/menu' })
    },
    /** 选择图标 */
    selectIcon(icon) {
      this.form.icon = icon
      this.showIconSelector = false
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.icon-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.icon-item {
  width: 16%;
  margin-bottom: 15px;
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #eee;
  border-radius: 4px;
  transition: all 0.3s;
}
.icon-item:hover {
  background-color: #f5f7fa;
  color: #409EFF;
}
.icon-item i {
  font-size: 24px;
  margin-bottom: 10px;
}
.icon-item span {
  font-size: 12px;
  word-break: break-all;
  text-align: center;
  padding: 0 5px;
}
</style> 