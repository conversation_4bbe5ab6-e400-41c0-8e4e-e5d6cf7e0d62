import request from './index'

/**
 * 获取模块列表
 * @param {Object} params - 查询参数
 * @param {string} [params.title] - 模块名称(模糊查询)
 * @param {number} [params.level] - 安全等级
 * @param {number} [params.state] - 状态(1:启用, 0:禁用, 不传:所有)
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getModuleList(params) {
  return request({
    url: '/admin/module/index',
    method: 'get',
    params
  })
}

/**
 * 创建模块
 * @param {Object} data - 模块数据
 * @param {string} data.title - 模块名称
 * @param {string} data.description - 模块描述
 * @param {string} data.code - 模块代码
 * @param {number} [data.level=0] - 安全等级
 * @param {number} [data.state=1] - 状态(1:启用, 0:禁用)
 * @param {number} [data.is_share=10] - 共享状态(10:非公开, 20:公开, 30:待审核)
 * @param {string} [data.keys] - 关键字
 * @param {string} [data.setkeys] - 配置参数
 * @param {string} [data.remark] - 备注
 * @returns {Promise}
 */
export function createModule(data) {
  return request({
    url: '/admin/module/index',
    method: 'post',
    data
  })
}

/**
 * 获取单个模块详情
 * @param {number} id - 模块ID
 * @returns {Promise}
 */
export function getModule(id) {
  return request({
    url: `/admin/module/index/${id}`,
    method: 'get'
  })
}

/**
 * 更新模块
 * @param {number} id - 模块ID
 * @param {Object} data - 需要更新的模块数据
 * @param {string} [data.title] - 模块名称
 * @param {string} [data.description] - 模块描述
 * @param {string} [data.code] - 模块代码
 * @param {number} [data.level] - 安全等级
 * @param {number} [data.state] - 状态(1:启用, 0:禁用)
 * @param {number} [data.is_share] - 共享状态(10:非公开, 20:公开, 30:待审核)
 * @param {string} [data.keys] - 关键字
 * @param {string} [data.setkeys] - 配置参数
 * @param {string} [data.remark] - 备注
 * @returns {Promise}
 */
export function updateModule(id, data) {
  return request({
    url: `/admin/module/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除模块
 * @param {number} id - 模块ID
 * @returns {Promise}
 */
export function deleteModule(id) {
  return request({
    url: `/admin/module/index/${id}`,
    method: 'delete'
  })
}

/**
 * 获取模块统计数据
 * @returns {Promise}
 */
export function getModuleStats() {
  return request({
    url: '/admin/module/stats',
    method: 'get'
  })
}

/**
 * 恢复已删除的模块
 * @param {number} id - 模块ID
 * @returns {Promise}
 */
export function restoreModule(id) {
  return request({
    url: `/admin/module/restore/${id}`,
    method: 'put'
  })
} 