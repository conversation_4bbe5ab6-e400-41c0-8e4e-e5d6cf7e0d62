package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetFriendlies 获取友情链接列表
// @Summary 获取友情链接列表
// @Description 获取友情链接列表，支持分页和筛选
// @Tags 友情链接管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为10"
// @Param name query string false "名称(模糊搜索)"
// @Param type query int false "类型"
// @Param state query int false "状态"
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.FriendlyListItem}}
// @Router /admin/friendly/index [get]
func GetFriendlies(c *fiber.Ctx) error {
	// 获取分页参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "10"))
	
	// 获取筛选参数
	name := c.Query("name", "")
	friendlyType := c.Query("type", "")
	state := c.Query("state", "")
	
	// 构建查询
	query := database.DB.Model(&models.Friendly{})
	
	// 应用筛选条件
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	
	if friendlyType != "" {
		typeInt, err := strconv.Atoi(friendlyType)
		if err == nil {
			query = query.Where("type = ?", typeInt)
		}
	}
	
	if state != "" {
		stateInt, err := strconv.Atoi(state)
		if err == nil {
			query = query.Where("state = ?", stateInt)
		}
	}
	
	// 计算总数
	var total int64
	query.Count(&total)
	
	// 获取分页数据
	var friendlies []models.Friendly
	query.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&friendlies)
	
	// 转换为DTO
	var friendlyDTOs []dto.FriendlyListItem
	for _, friendly := range friendlies {
		friendlyDTOs = append(friendlyDTOs, dto.FriendlyListItem{
			ID:        friendly.ID,
			Name:      friendly.Name,
			Href:      friendly.Href,
			Thumb:     friendly.Thumb,
			Type:      friendly.Type,
			State:     friendly.State,
			CreatedAt: friendly.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	
	// 返回分页响应
	return utils.SuccessPaginated(c, "获取友情链接列表成功", friendlyDTOs, total, page, pageSize)
}

// GetFriendly 获取单个友情链接详情
// @Summary 获取友情链接详情
// @Description 根据ID获取友情链接详情
// @Tags 友情链接管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "友情链接ID"
// @Success 200 {object} dto.StandardResponse{data=dto.FriendlyResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "友情链接不存在"
// @Router /admin/friendly/index/{id} [get]
func GetFriendly(c *fiber.Ctx) error {
	// 获取友情链接ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的友情链接ID", err)
	}
	
	// 查询友情链接
	var friendly models.Friendly
	if err := database.DB.First(&friendly, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "友情链接不存在")
		}
		return utils.ServerError(c, "获取友情链接失败", err)
	}
	
	// 转换为DTO
	friendlyDTO := dto.FriendlyResponse{
		ID:        friendly.ID,
		Name:      friendly.Name,
		Href:      friendly.Href,
		Thumb:     friendly.Thumb,
		Type:      friendly.Type,
		State:     friendly.State,
		CreatedAt: friendly.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: friendly.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "获取友情链接成功", friendlyDTO)
}

// CreateFriendly 创建友情链接
// @Summary 创建友情链接
// @Description 创建新的友情链接
// @Tags 友情链接管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param friendly body dto.FriendlyRequest true "友情链接信息"
// @Success 200 {object} dto.StandardResponse{data=dto.FriendlyResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/friendly/index [post]
func CreateFriendly(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.FriendlyRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 创建新友情链接
	friendly := models.Friendly{
		Name:  req.Name,
		Href:  req.Href,
		Thumb: req.Thumb,
		Type:  req.Type,
		State: req.State,
	}
	
	if err := database.DB.Create(&friendly).Error; err != nil {
		return utils.ServerError(c, "创建友情链接失败", err)
	}
	
	// 转换为DTO
	friendlyDTO := dto.FriendlyResponse{
		ID:        friendly.ID,
		Name:      friendly.Name,
		Href:      friendly.Href,
		Thumb:     friendly.Thumb,
		Type:      friendly.Type,
		State:     friendly.State,
		CreatedAt: friendly.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: friendly.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "创建友情链接成功", friendlyDTO)
}

// UpdateFriendly 更新友情链接
// @Summary 更新友情链接
// @Description 更新现有友情链接
// @Tags 友情链接管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "友情链接ID"
// @Param friendly body dto.FriendlyUpdateRequest true "友情链接更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.FriendlyResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "友情链接不存在"
// @Router /admin/friendly/index/{id} [put]
func UpdateFriendly(c *fiber.Ctx) error {
	// 获取友情链接ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的友情链接ID", err)
	}
	
	// 查询友情链接
	var friendly models.Friendly
	if err := database.DB.First(&friendly, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "友情链接不存在")
		}
		return utils.ServerError(c, "获取友情链接失败", err)
	}
	
	// 解析请求体
	var req dto.FriendlyUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 准备更新数据
	updates := make(map[string]interface{})
	
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	
	if req.Href != nil {
		updates["href"] = *req.Href
	}
	
	if req.Thumb != nil {
		updates["thumb"] = *req.Thumb
	}
	
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	
	if req.State != nil {
		updates["state"] = *req.State
	}
	
	// 执行更新
	if err := database.DB.Model(&friendly).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新友情链接失败", err)
	}
	
	// 重新获取更新后的友情链接
	if err := database.DB.First(&friendly, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的友情链接失败", err)
	}
	
	// 转换为DTO
	friendlyDTO := dto.FriendlyResponse{
		ID:        friendly.ID,
		Name:      friendly.Name,
		Href:      friendly.Href,
		Thumb:     friendly.Thumb,
		Type:      friendly.Type,
		State:     friendly.State,
		CreatedAt: friendly.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: friendly.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "更新友情链接成功", friendlyDTO)
}

// DeleteFriendly 删除友情链接
// @Summary 删除友情链接
// @Description 删除现有友情链接
// @Tags 友情链接管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "友情链接ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "友情链接不存在"
// @Router /admin/friendly/index/{id} [delete]
func DeleteFriendly(c *fiber.Ctx) error {
	// 获取友情链接ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的友情链接ID", err)
	}
	
	// 查询友情链接
	var friendly models.Friendly
	if err := database.DB.First(&friendly, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "友情链接不存在")
		}
		return utils.ServerError(c, "获取友情链接失败", err)
	}
	
	// 软删除友情链接
	if err := database.DB.Delete(&friendly).Error; err != nil {
		return utils.ServerError(c, "删除友情链接失败", err)
	}
	
	return utils.Success(c, "删除友情链接成功", nil)
}

// GetPublicFriendlies 获取公开友情链接列表（无需认证）
// @Summary 获取公开友情链接列表
// @Description 获取公开可用的友情链接列表，无需认证
// @Tags 公开接口
// @Accept json
// @Produce json
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为20"
// @Success 200 {object} dto.StandardResponse{data=[]dto.FriendlyListItem}
// @Router /admin/friendly-list [get]
func GetPublicFriendlies(c *fiber.Ctx) error {
	// 获取分页参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "20"))
	
	// 构建查询 - 只返回启用状态的友情链接
	query := database.DB.Model(&models.Friendly{}).Where("state = ?", 1)
	
	// 计算总数
	var total int64
	query.Count(&total)
	
	// 获取分页数据
	var friendlies []models.Friendly
	query.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&friendlies)
	
	// 转换为DTO
	var friendlyDTOs []dto.FriendlyListItem
	for _, friendly := range friendlies {
		friendlyDTOs = append(friendlyDTOs, dto.FriendlyListItem{
			ID:        friendly.ID,
			Name:      friendly.Name,
			Href:      friendly.Href,
			Thumb:     friendly.Thumb,
			Type:      friendly.Type,
			State:     friendly.State,
			CreatedAt: friendly.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	
	// 返回响应 - 这里直接返回列表，不使用分页结构
	return utils.Success(c, "获取友情链接列表成功", friendlyDTOs)
} 