const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
  // 禁用ESLint校验
  lintOnSave: false,
  // 配置webpack
  configureWebpack: {
    // 禁用对应的webpack插件
    plugins: []
  },
  // 开发服务器配置
  devServer: {
    proxy: {
      // 代理API请求
      '/api': {
        // target: process.env.VUE_APP_BASE_API,
        target: 'http://127.0.0.1:3000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'  // 保持路径不变
        }
      },
      '/static': {
        target: 'http://127.0.0.1:3000',
        changeOrigin: true
      }
    }
  }
})
