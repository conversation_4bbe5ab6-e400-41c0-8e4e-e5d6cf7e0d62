#!/bin/bash

# Go Vue Pro 项目验证脚本
# Linux/WSL 版本

echo "=== Go Vue Pro 项目服务验证 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

# 进入 docker 目录
cd docker || {
    echo -e "${RED}错误: docker 目录不存在${NC}"
    exit 1
}

# 检查容器状态
echo -e "\n${CYAN}1. 检查容器运行状态${NC}"
if ! docker compose ps; then
    echo -e "${RED}错误: 无法获取容器状态${NC}"
    cd ..
    exit 1
fi

# 检查端口占用
echo -e "\n${CYAN}2. 检查端口占用情况${NC}"
ports=(
    "80:Nginx (前端)"
    "3000:API 服务"
    "3308:MySQL"
    "6379:Redis"
    "8080:队列监控"
)

for port_info in "${ports[@]}"; do
    port=$(echo $port_info | cut -d: -f1)
    service=$(echo $port_info | cut -d: -f2)
    
    if nc -z localhost $port 2>/dev/null; then
        echo -e "${GREEN}✓ 端口 $port ($service): 正常${NC}"
    else
        echo -e "${RED}✗ 端口 $port ($service): 未响应${NC}"
    fi
done

# 检查服务健康状态
echo -e "\n${CYAN}3. 检查服务健康状态${NC}"

# 检查前端
echo -e "${YELLOW}检查前端服务...${NC}"
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost 2>/dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ 前端服务: 正常访问${NC}"
    echo -e "${GRAY}  响应状态: $response${NC}"
else
    echo -e "${RED}✗ 前端服务: 无法访问${NC}"
    echo -e "${GRAY}  响应状态: $response${NC}"
fi

# 检查API健康检查端点
echo -e "${YELLOW}检查API健康检查...${NC}"
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/health 2>/dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ API 健康检查: 正常${NC}"
    content=$(curl -s http://localhost/health 2>/dev/null)
    echo -e "${GRAY}  响应内容: $content${NC}"
else
    echo -e "${RED}✗ API 健康检查: 失败${NC}"
    echo -e "${GRAY}  响应状态: $response${NC}"
fi

# 检查API接口
echo -e "${YELLOW}检查API接口...${NC}"
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/api/ 2>/dev/null)
if [ "$response" = "200" ] || [ "$response" = "404" ]; then
    echo -e "${GREEN}✓ API 接口: 可访问${NC}"
    echo -e "${GRAY}  响应状态: $response${NC}"
else
    echo -e "${RED}✗ API 接口: 无法访问${NC}"
    echo -e "${GRAY}  响应状态: $response${NC}"
fi

# 检查Swagger文档
echo -e "${YELLOW}检查Swagger文档...${NC}"
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/swagger/ 2>/dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ Swagger 文档: 可访问${NC}"
else
    echo -e "${RED}✗ Swagger 文档: 无法访问${NC}"
    echo -e "${GRAY}  响应状态: $response${NC}"
fi

# 检查队列监控
echo -e "${YELLOW}检查队列监控...${NC}"
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/asynq/ 2>/dev/null)
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ 队列监控: 可访问${NC}"
else
    echo -e "${RED}✗ 队列监控: 无法访问${NC}"
    echo -e "${GRAY}  响应状态: $response${NC}"
fi

# 显示日志摘要
echo -e "\n${CYAN}4. 最近的服务日志${NC}"
echo -e "${YELLOW}API 服务日志 (最后5行):${NC}"
if ! docker compose logs --tail=5 api; then
    echo -e "${RED}无法获取API日志${NC}"
fi

echo -e "\n${YELLOW}Nginx 服务日志 (最后5行):${NC}"
if ! docker compose logs --tail=5 nginx; then
    echo -e "${RED}无法获取Nginx日志${NC}"
fi

# 总结
echo -e "\n${CYAN}=== 验证总结 ===${NC}"
echo "如果所有检查都显示 ✓，说明服务运行正常"
echo "如果有 ✗ 标记，请检查对应的日志:"
echo -e "${GRAY}- docker compose logs [服务名]${NC}"
echo -e "\n${CYAN}主要访问地址:${NC}"
echo "- 前端应用: http://localhost"
echo "- API 文档: http://localhost/swagger/"
echo -e "\n${YELLOW}💡 在 WSL 环境中，请在 Windows 浏览器中访问上述地址${NC}"

# 返回原目录
cd ..
