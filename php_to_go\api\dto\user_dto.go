package dto

// ProfileResponse 用户个人资料响应DTO
type ProfileResponse struct {
	ID          uint64     `json:"id"`
	Username    string     `json:"username"`
	Name        string     `json:"name"`
	Email       string     `json:"email"`
	Avatar      string     `json:"avatar"`
	Roles       []RoleItem `json:"roles"`
	Permissions []string   `json:"permissions"` // 用户权限列表
	RoleSlugs   []string   `json:"role_slugs"`  // 用户角色标识列表
}

// UpdateProfileDTO 更新个人资料请求DTO
type UpdateProfileDTO struct {
	Name   string `json:"name" validate:"required,max=50"`
	Email  string `json:"email" validate:"omitempty,email,max=100"`
	Avatar string `json:"avatar"`
}

// RoleItem 角色简要信息
type RoleItem struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug"`
}

// AvatarResponse 头像上传响应DTO
type AvatarResponse struct {
	URL string `json:"url"` // 上传成功后的头像URL
} 