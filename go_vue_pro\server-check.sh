#!/bin/bash

# 服务器环境检查脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== 服务器环境检查 ===${NC}"

# 检查操作系统
echo -e "\n${CYAN}1. 操作系统信息${NC}"
echo "系统: $(uname -s)"
echo "内核: $(uname -r)"
echo "架构: $(uname -m)"
if [ -f /etc/os-release ]; then
    . /etc/os-release
    echo "发行版: $NAME $VERSION"
fi

# 检查系统资源
echo -e "\n${CYAN}2. 系统资源${NC}"
echo "CPU核心数: $(nproc)"
echo "内存信息:"
free -h
echo "磁盘空间:"
df -h

# 检查网络
echo -e "\n${CYAN}3. 网络配置${NC}"
echo "IP地址:"
hostname -I
echo "网络连接测试:"
if ping -c 1 google.com &> /dev/null; then
    echo -e "${GREEN}✓ 网络连接正常${NC}"
else
    echo -e "${RED}✗ 网络连接异常${NC}"
fi

# 检查端口占用
echo -e "\n${CYAN}4. 端口检查${NC}"
PORTS=(80 443 3000 3308 6379 8080)
for port in "${PORTS[@]}"; do
    if netstat -tuln | grep ":$port " &> /dev/null; then
        echo -e "${YELLOW}⚠ 端口 $port 已被占用${NC}"
    else
        echo -e "${GREEN}✓ 端口 $port 可用${NC}"
    fi
done

# 检查Docker
echo -e "\n${CYAN}5. Docker环境${NC}"
if command -v docker &> /dev/null; then
    echo -e "${GREEN}✓ Docker 已安装${NC}"
    docker --version
    
    # 检查Docker服务状态
    if systemctl is-active --quiet docker; then
        echo -e "${GREEN}✓ Docker 服务运行中${NC}"
    else
        echo -e "${RED}✗ Docker 服务未运行${NC}"
    fi
    
    # 检查Docker权限
    if docker ps &> /dev/null; then
        echo -e "${GREEN}✓ Docker 权限正常${NC}"
    else
        echo -e "${YELLOW}⚠ 当前用户可能需要Docker权限${NC}"
        echo "建议运行: sudo usermod -aG docker \$USER"
    fi
else
    echo -e "${RED}✗ Docker 未安装${NC}"
    echo "安装命令:"
    echo "curl -fsSL https://get.docker.com | sh"
fi

# 检查Docker Compose
if command -v docker-compose &> /dev/null; then
    echo -e "${GREEN}✓ Docker Compose 已安装${NC}"
    docker-compose --version
elif docker compose version &> /dev/null; then
    echo -e "${GREEN}✓ Docker Compose (插件) 已安装${NC}"
    docker compose version
else
    echo -e "${RED}✗ Docker Compose 未安装${NC}"
    echo "安装命令:"
    echo "sudo curl -L \"https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
    echo "sudo chmod +x /usr/local/bin/docker-compose"
fi

# 检查防火墙
echo -e "\n${CYAN}6. 防火墙状态${NC}"
if command -v ufw &> /dev/null; then
    echo "UFW状态: $(ufw status | head -1)"
elif command -v firewall-cmd &> /dev/null; then
    echo "Firewalld状态: $(firewall-cmd --state 2>/dev/null || echo 'inactive')"
elif command -v iptables &> /dev/null; then
    echo "iptables规则数: $(iptables -L | wc -l)"
else
    echo "未检测到防火墙工具"
fi

# 检查必要工具
echo -e "\n${CYAN}7. 必要工具检查${NC}"
TOOLS=(curl wget git nano vim)
for tool in "${TOOLS[@]}"; do
    if command -v $tool &> /dev/null; then
        echo -e "${GREEN}✓ $tool 已安装${NC}"
    else
        echo -e "${YELLOW}⚠ $tool 未安装${NC}"
    fi
done

# 系统建议
echo -e "\n${CYAN}=== 系统建议 ===${NC}"

# 内存检查
MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
if [ $MEMORY_GB -lt 2 ]; then
    echo -e "${YELLOW}⚠ 建议至少2GB内存用于生产环境${NC}"
fi

# 磁盘空间检查
DISK_USAGE=$(df / | awk 'NR==2{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo -e "${YELLOW}⚠ 磁盘使用率较高 ($DISK_USAGE%)${NC}"
fi

# 交换空间检查
SWAP_SIZE=$(free -m | awk '/^Swap:/{print $2}')
if [ $SWAP_SIZE -eq 0 ]; then
    echo -e "${YELLOW}⚠ 建议配置交换空间${NC}"
fi

echo -e "\n${GREEN}环境检查完成！${NC}"
echo -e "\n${CYAN}下一步操作:${NC}"
echo "1. 如果Docker未安装，请先安装Docker"
echo "2. 上传项目文件到服务器"
echo "3. 配置生产环境变量文件"
echo "4. 运行部署脚本: ./deploy-prod.sh"
