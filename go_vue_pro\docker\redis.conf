# Redis生产环境配置

# 网络设置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# 内存设置
maxmemory 128mb
maxmemory-policy allkeys-lru

# 持久化设置
save 900 1
save 300 10
save 60 10000

# AOF持久化
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志设置
loglevel notice
logfile /var/log/redis/redis.log

# 安全设置
# requirepass your_redis_password_here

# 性能优化
tcp-backlog 511
databases 16
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes

# 客户端设置
maxclients 10000

# 慢日志
slowlog-log-slower-than 10000
slowlog-max-len 128
