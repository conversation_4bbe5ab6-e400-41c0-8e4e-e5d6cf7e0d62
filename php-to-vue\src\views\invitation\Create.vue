<template>
  <div class="invitation-create">
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? '编辑邀请码' : '创建邀请码' }}</h1>
      <el-button @click="$router.push('/invitation')">返回列表</el-button>
    </div>

    <el-card shadow="hover" class="form-container">
      <el-form 
        ref="invitationForm" 
        :model="invitationForm" 
        :rules="rules" 
        label-width="100px"
        v-loading="loading">
        
        <el-form-item label="邀请码" prop="code">
          <el-input v-model="invitationForm.code" placeholder="留空将自动生成" :disabled="isEdit"></el-input>
          <span class="form-tip">{{ isEdit ? '邀请码不可修改' : '如不填写将自动生成随机邀请码' }}</span>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="invitationForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入邀请码描述"></el-input>
        </el-form-item>

        <el-form-item label="使用限制" prop="limit">
          <el-input-number v-model="invitationForm.limit" :min="0" placeholder="0表示不限制"></el-input-number>
          <span class="form-tip">0表示不限制使用次数</span>
        </el-form-item>

        <el-form-item label="过期时间" prop="expired_at">
          <el-date-picker
            v-model="invitationForm.expired_at"
            type="datetime"
            placeholder="选择过期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="{disabledDate(date) { return date.getTime() < Date.now() - 8.64e7 }}">
          </el-date-picker>
          <span class="form-tip">不设置表示永不过期</span>
        </el-form-item>
        
        <el-form-item label="状态" prop="state">
          <el-switch
            v-model="invitationForm.state"
            :active-value="1"
            :inactive-value="0">
          </el-switch>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">保存</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 使用记录 -->
    <el-card shadow="hover" class="usage-container" v-if="isEdit">
      <div slot="header">
        <span>使用记录</span>
      </div>
      <div v-if="usageLoading" class="usage-loading">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="usageRecords.length === 0" class="no-data">
        暂无使用记录
      </div>
      <el-table v-else :data="usageRecords" border stripe>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="user_id" label="用户ID" width="100"></el-table-column>
        <el-table-column prop="username" label="用户名"></el-table-column>
        <el-table-column prop="ip" label="IP地址" width="150"></el-table-column>
        <el-table-column prop="created_at" label="使用时间" width="160"></el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getInvitation, createInvitation, updateInvitation } from '@/api/invitation'

export default {
  name: 'InvitationCreate',
  data() {
    return {
      isEdit: false,
      invitationId: null,
      loading: false,
      submitting: false,
      usageLoading: false,
      
      // 邀请码表单
      invitationForm: {
        code: '',
        description: '',
        limit: 0,
        expired_at: '',
        state: 1
      },
      
      // 表单验证规则
      rules: {
        code: [
          { required: true, message: '请输入邀请码', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        description: [
          { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
        ]
      },
      
      // 使用记录
      usageRecords: []
    }
  },
  created() {
    // 检查URL中是否有id参数，如果有则为编辑模式
    const id = this.$route.query.id
    if (id) {
      this.isEdit = true
      this.invitationId = id
      this.fetchInvitationDetail()
    } else {
      // 非编辑模式，生成一个随机邀请码
      this.invitationForm.code = this.generateRandomCode()
    }
  },
  methods: {
    // 提交表单
    submitForm() {
      this.$refs.invitationForm.validate(valid => {
        if (!valid) {
          return false
        }
        
        this.submitting = true
        
        // 准备提交的数据
        const submitData = {
          ...this.invitationForm,
          count: 1 // 确保count字段有值且至少为1
        }
        
        // 处理可选字段
        if (!submitData.expired_at) delete submitData.expired_at
        
        if (this.isEdit) {
          // 更新邀请码
          updateInvitation(this.invitationId, submitData)
            .then(() => {
              this.$message.success('邀请码更新成功')
              this.$router.push('/invitation')
            })
            .catch(error => {
              this.$message.error(`更新失败: ${error.message}`)
            })
            .finally(() => {
              this.submitting = false
            })
        } else {
          // 创建邀请码
          createInvitation(submitData)
            .then(() => {
              this.$message.success('邀请码创建成功')
              this.$router.push('/invitation')
            })
            .catch(error => {
              this.$message.error(`创建失败: ${error.message}`)
            })
            .finally(() => {
              this.submitting = false
            })
        }
      })
    },
    
    // 重置表单
    resetForm() {
      this.$refs.invitationForm.resetFields()
      
      if (!this.isEdit) {
        // 如果是新建，则重置为默认值并生成新的随机邀请码
        this.invitationForm = {
          code: this.generateRandomCode(),
          description: '',
          limit: 0,
          expired_at: '',
          state: 1
        }
      } else {
        // 如果是编辑，则重新获取原始数据
        this.fetchInvitationDetail()
      }
    },
    
    // 获取邀请码详情
    fetchInvitationDetail() {
      if (!this.invitationId) {
        return
      }
      
      this.loading = true
      getInvitation(this.invitationId)
        .then(response => {
          this.invitationForm = {
            code: response.code || '',
            description: response.description || '',
            limit: response.limit || 0,
            expired_at: response.expired_at || '',
            state: response.state || 0
          }
          
          // 如果有使用记录，设置使用记录
          if (response.usage && Array.isArray(response.usage)) {
            this.usageRecords = response.usage
          }
        })
        .catch(error => {
          this.$message.error(`获取邀请码详情失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    
    // 生成随机邀请码
    generateRandomCode() {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
      let code = ''
      for (let i = 0; i < 8; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return code
    }
  }
}
</script>

<style scoped>
.invitation-create {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.form-container {
  margin-bottom: 20px;
}

.usage-container {
  margin-bottom: 20px;
}

.form-tip {
  display: block;
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.usage-loading {
  padding: 20px 0;
}

.no-data {
  padding: 30px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
}
</style> 