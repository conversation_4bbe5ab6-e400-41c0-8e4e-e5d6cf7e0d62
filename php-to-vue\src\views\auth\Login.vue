<template>
  <div class="login-container">
    <!-- 背景矩阵雨特效 -->
    <canvas id="matrix-canvas" ref="matrixCanvas"></canvas>

    <div class="login-box">
      <!-- 网站Logo或标题 -->
      <div class="logo-container">
        <img v-if="siteLogo" :src="siteLogo" alt="网站Logo" class="site-logo" @error="handleLogoError" />
        <h1 v-else class="login-title">XSS</h1>
      </div>
      
      <!-- 维护模式提示 -->
      <el-alert
        v-if="isSiteClosed"
        type="error"
        :closable="false"
        class="maintenance-alert"
        show-icon
      >
        <div class="maintenance-content">
          <h3>网站维护中</h3>
          <div v-html="closedMessage"></div>
          <div class="admin-login-hint" @click="toggleAdminLogin">
            管理员登录 <i :class="showAdminLogin ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </div>
        </div>
      </el-alert>
      
      <!-- 管理员登录表单 - 在维护模式下显示 -->
      <el-card class="login-card admin-login-card" v-if="isSiteClosed && showAdminLogin">
        <div class="admin-login-title">管理员登录</div>
        <el-form
          :model="loginForm"
          :rules="rules"
          ref="loginForm"
          label-width="0"
          @submit.native.prevent
        >
          <!-- 用户名 -->
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="管理员用户名"
              prefix-icon="el-icon-user"
            >
            </el-input>
          </el-form-item>

          <!-- 密码 -->
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="管理员密码"
              show-password
              prefix-icon="el-icon-lock"
            >
            </el-input>
          </el-form-item>

          <!-- 验证码 -->
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="验证码"
                prefix-icon="el-icon-key"
              ></el-input>
              <div class="captcha-image" @click="refreshCaptcha">
                <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                <div v-else class="captcha-loading">
                  <i class="el-icon-loading"></i>
                </div>
              </div>
            </div>
          </el-form-item>

          <!-- 登录按钮 -->
          <el-form-item>
            <el-button
              type="danger"
              :loading="loading"
              class="login-button admin-login-button"
              @click="handleAdminLogin"
            >
              管理员登录 <i class="el-icon-right"></i>
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 网站描述 -->
      <p class="login-subtitle" v-if="!isSiteClosed && siteDescription">{{ siteDescription }}</p>
      <p class="login-subtitle" v-else-if="!isSiteClosed">欢迎回来，请登录您的账号。</p>

      <!-- 登录表单 - 仅在非维护模式下显示 -->
      <el-card class="login-card" v-if="!isSiteClosed">
        <el-form
          :model="loginForm"
          :rules="rules"
          ref="loginForm"
          label-width="0"
          @submit.native.prevent
        >
          <!-- 用户名 -->
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              prefix-icon="el-icon-user"
            >
            </el-input>
          </el-form-item>

          <!-- 密码 -->
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              show-password
              prefix-icon="el-icon-lock"
            >
            </el-input>
          </el-form-item>

          <!-- 验证码 -->
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="loginForm.captcha"
                placeholder="验证码"
                prefix-icon="el-icon-key"
              ></el-input>
              <div class="captcha-image" @click="refreshCaptcha">
                <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                <div v-else class="captcha-loading">
                  <i class="el-icon-loading"></i>
                </div>
              </div>
            </div>
          </el-form-item>

          <!-- 记住我 -->
          <el-form-item>
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
          </el-form-item>

          <!-- 登录按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              :loading="loading"
              class="login-button"
              @click="handleLogin"
            >
              登录 <i class="el-icon-right"></i>
            </el-button>
          </el-form-item>

          <!-- 额外链接 -->
          <div class="login-links">
            <router-link v-if="!isRegisterClosed" to="/signup" class="link"
              >立即注册</router-link
            >
            <router-link to="/forgot-password" class="link"
              >找回密码</router-link
            >
          </div>
        </el-form>
      </el-card>

      <!-- 自定义页脚信息 -->
      <div class="copyright" v-if="siteFooter" v-html="siteFooter"></div>
      <div class="copyright" v-else>
        © 2020 - {{ new Date().getFullYear() }} XSS测试平台
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { getCaptcha } from "@/api/auth";
import { getPublicSettings } from "@/api/settings";

export default {
  name: "Login",
  data() {
    return {
      loading: false,
      captchaImage: "",
      captchaKey: "",
      rememberMe: false,
      isRegisterClosed: false, // 是否关闭注册
      isSiteClosed: false, // 网站是否处于维护模式
      closedMessage: "网站正在维护中，请稍后再试。", // 维护模式提示信息
      siteDescription: "", // 网站描述
      siteFooter: "", // 页脚信息
      siteLogo: "", // 网站Logo
      loginForm: {
        username: "",
        password: "",
        captcha: "",
        captcha_key: "",
      },
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            min: 3,
            max: 20,
            message: "长度在 3 到 20 个字符",
            trigger: "blur",
          },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
        captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      showAdminLogin: false,
    };
  },
  created() {
    // 获取公开设置
    this.loadPublicSettings().then(() => {
      // 在非维护模式下获取验证码，或者在维护模式下但显示管理员登录表单时获取验证码
      if (!this.isSiteClosed || this.showAdminLogin) {
        this.refreshCaptcha();
      }
    });

    // 如果存在记住我标记，则设置rememberMe
    if (localStorage.getItem("remember_me") === "1") {
      this.rememberMe = true;
    }

    // 如果已经登录，重定向到首页
    if (this.$store.getters.isLoggedIn) {
      const redirect = this.$route.query.redirect || "/";
      this.$router.push(redirect);
    }
  },
  mounted() {
    // 初始化矩阵雨效果
    this.initMatrixRain();
    // 窗口大小改变时重新调整画布大小
    window.addEventListener("resize", this.resizeCanvas);
  },
  beforeDestroy() {
    // 清除事件监听
    window.removeEventListener("resize", this.resizeCanvas);
    // 停止动画
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  },
  methods: {
    ...mapActions(["login", "logout"]),
    
    // 加载公开设置
    loadPublicSettings() {
      return getPublicSettings()
        .then((response) => {
          // 检查网站是否处于维护模式 - 严格检查字符串值
          this.isSiteClosed = response.site_closed === "true";
          
          // 如果处于维护模式，设置维护信息
          if (this.isSiteClosed && response.closed_message) {
            this.closedMessage = response.closed_message;
          }
          
          // 设置其他公开设置
          this.isRegisterClosed = response.register_closed === "true";
          
          // 设置网站描述
          if (response.site_description) {
            this.siteDescription = response.site_description;
          }
          
          // 设置页脚信息
          if (response.site_footer) {
            this.siteFooter = response.site_footer;
          }
          
          // 设置网站Logo
          if (response.site_logo) {
            // 如果是相对路径，添加基础URL
            if (response.site_logo.startsWith('/')) {
              this.siteLogo = `http://localhost:3000${response.site_logo}`;
            } else {
              this.siteLogo = response.site_logo;
            }
          }
          
          return response;
        })
        .catch((error) => {
          console.error("获取公开设置失败:", error);
          // 静默失败，使用默认值
          this.isRegisterClosed = false;
          this.isSiteClosed = false;
          return Promise.resolve(); // 确保链式调用可以继续
        });
    },
    
    // 处理Logo加载失败
    handleLogoError() {
      console.log("Logo加载失败，使用文本标题替代");
      this.siteLogo = ""; // 清空Logo URL，触发显示文本标题
    },

    // 刷新验证码
    refreshCaptcha() {
      // 在维护模式下，只有显示管理员登录表单时才获取验证码
      if (this.isSiteClosed && !this.showAdminLogin) {
        return;
      }
      
      this.loading = true;
      getCaptcha()
        .then((res) => {
          this.captchaImage = res.image_data;
          this.captchaKey = res.captcha_key;
          this.loginForm.captcha_key = res.captcha_key;
        })
        .catch(() => {
          this.$message.error("获取验证码失败，请重试");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理登录逻辑
    handleLogin() {
      // 如果网站处于维护模式，阻止登录
      if (this.isSiteClosed) {
        this.$message.error("网站维护中，暂时无法登录");
        return;
      }
      
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 添加记住我参数到登录数据中
          const loginData = {
            ...this.loginForm,
            remember: this.rememberMe ? 1 : 0,
          };

          this.login(loginData)
            .then(() => {
              this.$message.success("登录成功");

              // 如果选择了记住我，设置本地存储标记
              if (this.rememberMe) {
                localStorage.setItem("remember_me", "1");
              } else {
                localStorage.removeItem("remember_me");
              }

              // 如果有重定向地址，则跳转到该地址
              const redirectUrl = this.$route.query.redirect || "/";
              this.$router.push(redirectUrl);
            })
            .catch((error) => {
              // 提取更详细的错误信息
              let errorMsg = "登录失败，请重试";
              if (error.response && error.response.data) {
                errorMsg = error.response.data.message || errorMsg;
              } else if (error.message) {
                errorMsg = error.message;
              }
              this.$message.error(errorMsg);

              // 刷新验证码
              this.refreshCaptcha();
              this.loginForm.captcha = "";
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    // 初始化矩阵雨效果
    initMatrixRain() {
      const canvas = this.$refs.matrixCanvas;
      const ctx = canvas.getContext("2d");

      // 设置画布大小
      this.resizeCanvas();

      // 字符集
      const chars =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz$+-*/=%\"'#&_(),.;:?!\\|{}<>[]^~";

      // 每列的当前位置
      const drops = [];

      // 初始化 drops
      for (let i = 0; i < canvas.width / 20; i++) {
        drops[i] = 1;
      }

      // 绘制矩阵雨
      const draw = () => {
        // 半透明黑色背景，形成拖尾效果
        ctx.fillStyle = "rgba(0, 0, 0, 0.05)";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 设置字体
        ctx.font = "15px monospace";

        // 循环绘制每一列
        for (let i = 0; i < drops.length; i++) {
          // 随机选择一个字符
          const text = chars[Math.floor(Math.random() * chars.length)];

          // 随机字符颜色
          const green = Math.floor(Math.random() * 156 + 100); // 生成较亮的绿色
          ctx.fillStyle = `rgba(0, ${green}, 0, 0.8)`;

          // 绘制字符
          ctx.fillText(text, i * 20, drops[i] * 20);

          // 更新位置
          if (drops[i] * 20 > canvas.height && Math.random() > 0.975) {
            drops[i] = 0;
          }

          drops[i]++;
        }

        // 动画循环
        this.animationId = requestAnimationFrame(draw);
      };

      // 开始动画
      draw();
    },

    // 调整画布大小
    resizeCanvas() {
      const canvas = this.$refs.matrixCanvas;
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    },

    // 处理管理员登录逻辑
    handleAdminLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 添加记住我参数到登录数据中
          const loginData = {
            ...this.loginForm,
            remember: this.rememberMe ? 1 : 0,
            is_admin_login: true // 标记为管理员登录
          };

          this.login(loginData)
            .then((response) => {
              // 检查是否为管理员
              const isAdmin = response && response.user_info && 
                (response.user_info.is_admin === true || response.user_info.role === 1);
              
              if (isAdmin) {
                this.$message.success("管理员登录成功");
                
                // 如果选择了记住我，设置本地存储标记
                if (this.rememberMe) {
                  localStorage.setItem("remember_me", "1");
                } else {
                  localStorage.removeItem("remember_me");
                }

                // 如果有重定向地址，则跳转到该地址
                const redirectUrl = this.$route.query.redirect || "/";
                this.$router.push(redirectUrl);
              } else {
                // 如果不是管理员，注销并显示错误
                this.logout().then(() => {
                  this.$message.error("非管理员账号无法在维护模式下登录");
                  this.refreshCaptcha();
                  this.loginForm.captcha = "";
                });
              }
            })
            .catch((error) => {
              // 提取更详细的错误信息
              let errorMsg = "登录失败，请重试";
              if (error.response && error.response.data) {
                errorMsg = error.response.data.message || errorMsg;
              } else if (error.message) {
                errorMsg = error.message;
              }
              this.$message.error(errorMsg);

              // 刷新验证码
              this.refreshCaptcha();
              this.loginForm.captcha = "";
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    // 切换管理员登录表单的显示状态
    toggleAdminLogin() {
      this.showAdminLogin = !this.showAdminLogin;
      if (this.showAdminLogin) {
        this.refreshCaptcha();
      }
    },
  },
};
</script>

<style scoped>
.login-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #000;
  color: #fff;
}

#matrix-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.login-box {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 450px;
  width: 90%;
}

.logo-container {
  margin-bottom: 1rem;
}

.site-logo {
  max-width: 180px;
  max-height: 80px;
  margin-bottom: 1rem;
}

.login-title {
  font-size: 3rem;
  font-weight: 300;
  margin-bottom: 0.5rem;
  color: #fff;
}

.login-subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  line-height: 1.5;
}

/* 维护模式提示样式 */
.maintenance-alert {
  margin-bottom: 2rem;
  background-color: rgba(255, 68, 68, 0.2) !important;
  border-color: rgba(255, 68, 68, 0.5) !important;
}

.maintenance-alert /deep/ .el-alert__content {
  width: 100%;
  padding: 10px 0;
}

.maintenance-content {
  text-align: left;
  color: #fff;
}

.maintenance-content h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.2rem;
  color: #ff4444;
}

.login-card {
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(50, 205, 50, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 0 10px rgba(50, 205, 50, 0.2);
}

.login-card /deep/ .el-card__body {
  padding: 20px 30px;
}

.login-card /deep/ .el-input__inner {
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(50, 205, 50, 0.5);
  color: #fff;
}

.login-card /deep/ .el-input__icon {
  color: rgba(50, 205, 50, 0.8);
}

.login-card /deep/ .el-form-item__label {
  color: #fff;
}

.login-card /deep/ .el-checkbox__label {
  color: #fff;
}

.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-image {
  margin-left: 10px;
  width: 120px;
  height: 40px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(50, 205, 50, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.captcha-image img {
  max-width: 100%;
  max-height: 100%;
}

.captcha-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: rgba(50, 205, 50, 0.8);
}

.login-button {
  width: 100%;
  background-color: rgba(50, 205, 50, 0.7);
  border-color: rgba(50, 205, 50, 0.9);
  transition: all 0.3s;
}

.login-button:hover {
  background-color: rgba(50, 205, 50, 0.9);
  box-shadow: 0 0 10px rgba(50, 205, 50, 0.7);
}

.login-links {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.link {
  color: rgba(50, 205, 50, 0.9);
  text-decoration: none;
  transition: all 0.3s;
}

.link:hover {
  color: #fff;
  text-shadow: 0 0 5px rgba(50, 205, 50, 0.9);
}

.copyright {
  margin-top: 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  line-height: 1.5;
}

.copyright /deep/ a {
  color: rgba(50, 205, 50, 0.7);
  text-decoration: none;
}

.copyright /deep/ a:hover {
  color: rgba(50, 205, 50, 1);
  text-decoration: underline;
}

.admin-login-hint {
  margin-top: 15px;
  color: rgba(50, 205, 50, 0.9);
  cursor: pointer;
  font-size: 0.9rem;
  text-align: center;
  transition: all 0.3s;
}

.admin-login-hint:hover {
  color: #fff;
  text-shadow: 0 0 5px rgba(50, 205, 50, 0.9);
}

.admin-login-card {
  margin-top: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 68, 68, 0.3);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.2);
}

.admin-login-title {
  font-size: 1.5rem;
  font-weight: 300;
  margin-bottom: 1rem;
  color: #ff4444;
}

.admin-login-button {
  width: 100%;
  background-color: rgba(255, 68, 68, 0.7) !important;
  border-color: rgba(255, 68, 68, 0.9) !important;
  transition: all 0.3s;
}

.admin-login-button:hover {
  background-color: rgba(255, 68, 68, 0.9) !important;
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.7);
}
</style>
