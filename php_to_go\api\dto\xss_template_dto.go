package dto

import "time"

// CreateXssTemplateRequest 创建XSS模板请求
type CreateXssTemplateRequest struct {
	ProjectID uint64 `json:"project_id" validate:"required" example:"1"`
	Title     string `json:"title" validate:"required,max=255" example:"XSS测试模板"`
	Content   string `json:"content" validate:"required" example:"<script>alert('XSS')</script>"`
}

// UpdateXssTemplateRequest 更新XSS模板请求
type UpdateXssTemplateRequest struct {
	Title   *string `json:"title,omitempty" validate:"omitempty,max=255" example:"更新的XSS测试模板"`
	Content *string `json:"content,omitempty" example:"<script>alert('Updated XSS')</script>"`
}

// XssTemplateResponse XSS模板响应
type XssTemplateResponse struct {
	ID        uint64    `json:"id" example:"1"`
	ProjectID uint64    `json:"project_id" example:"1"`
	Title     string    `json:"title" example:"XSS测试模板"`
	Content   string    `json:"content" example:"<script>alert('XSS')</script>"`
	CreatedAt time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt time.Time `json:"updated_at" example:"2023-01-01T00:00:00Z"`
}

// XssTemplateListRequest XSS模板列表请求
type XssTemplateListRequest struct {
	ProjectID *uint64 `json:"project_id,omitempty" form:"project_id" example:"1"`
	Page      int     `json:"page,omitempty" form:"page" validate:"min=1" example:"1"`
	PageSize  int     `json:"page_size,omitempty" form:"page_size" validate:"min=1,max=100" example:"10"`
	Title     string  `json:"title,omitempty" form:"title" example:"测试"`
}

// XssTemplateListResponse XSS模板列表响应
type XssTemplateListResponse struct {
	List     []XssTemplateResponse `json:"list"`
	Total    int64                 `json:"total" example:"100"`
	Page     int                   `json:"page" example:"1"`
	PageSize int                   `json:"page_size" example:"10"`
}
