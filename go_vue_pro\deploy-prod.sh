#!/bin/bash

# Go Vue Pro 生产环境部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Go Vue Pro 生产环境部署脚本 ===${NC}"

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo -e "${YELLOW}警告: 建议不要使用root用户运行此脚本${NC}"
fi

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker 未安装${NC}"
    echo "请先安装Docker: https://docs.docker.com/engine/install/"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${RED}错误: Docker Compose 未安装${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Docker 环境检查通过${NC}"

# 进入项目目录
cd "$(dirname "$0")"
PROJECT_ROOT=$(pwd)

echo -e "${YELLOW}项目根目录: $PROJECT_ROOT${NC}"

# 检查必要文件
REQUIRED_FILES=(
    "docker/docker-compose.prod.yml"
    "docker/Dockerfile.server.prod"
    "docker/Dockerfile.nginx.prod"
    "docker/nginx.prod.conf"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo -e "${RED}错误: 缺少必要文件 $file${NC}"
        exit 1
    fi
done

echo -e "${GREEN}✓ 必要文件检查通过${NC}"

# 检查环境变量文件
if [ ! -f "docker/.env.prod" ]; then
    echo -e "${YELLOW}创建生产环境配置文件...${NC}"
    if [ -f "docker/.env.prod.example" ]; then
        cp docker/.env.prod.example docker/.env.prod
        echo -e "${YELLOW}请编辑 docker/.env.prod 文件，设置正确的生产环境变量${NC}"
        echo -e "${YELLOW}按任意键继续...${NC}"
        read -n 1
    else
        echo -e "${RED}错误: 缺少 .env.prod.example 文件${NC}"
        exit 1
    fi
fi

# 进入docker目录
cd docker

# 停止现有容器
echo -e "${YELLOW}停止现有容器...${NC}"
docker-compose -f docker-compose.prod.yml down || true

# 清理旧镜像（可选）
read -p "是否清理旧的Docker镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}清理旧镜像...${NC}"
    docker system prune -f
fi

# 构建并启动服务
echo -e "${YELLOW}构建并启动生产环境服务...${NC}"
docker-compose -f docker-compose.prod.yml up -d --build

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 30

# 检查服务状态
echo -e "${YELLOW}检查服务状态...${NC}"
docker-compose -f docker-compose.prod.yml ps

# 健康检查
echo -e "${YELLOW}执行健康检查...${NC}"
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if curl -f http://localhost/health &> /dev/null; then
        echo -e "${GREEN}✓ 应用健康检查通过${NC}"
        break
    fi
    
    attempt=$((attempt + 1))
    echo -e "${YELLOW}等待应用启动... ($attempt/$max_attempts)${NC}"
    sleep 10
done

if [ $attempt -eq $max_attempts ]; then
    echo -e "${RED}❌ 应用启动失败或健康检查超时${NC}"
    echo -e "${YELLOW}查看日志:${NC}"
    docker-compose -f docker-compose.prod.yml logs --tail=20
    exit 1
fi

# 显示部署结果
echo -e "\n${GREEN}🎉 生产环境部署成功！${NC}"
echo -e "\n${CYAN}=== 服务信息 ===${NC}"
echo -e "应用地址: http://$(hostname -I | awk '{print $1}')"
echo -e "健康检查: http://$(hostname -I | awk '{print $1}')/health"
echo -e "API文档: http://$(hostname -I | awk '{print $1}')/swagger/"

echo -e "\n${CYAN}=== 管理命令 ===${NC}"
echo -e "查看日志: docker-compose -f docker-compose.prod.yml logs"
echo -e "停止服务: docker-compose -f docker-compose.prod.yml down"
echo -e "重启服务: docker-compose -f docker-compose.prod.yml restart"
echo -e "查看状态: docker-compose -f docker-compose.prod.yml ps"

echo -e "\n${CYAN}=== 安全提醒 ===${NC}"
echo -e "1. 请确保防火墙只开放必要端口 (80, 443)"
echo -e "2. 定期备份数据库和Redis数据"
echo -e "3. 监控系统资源使用情况"
echo -e "4. 定期更新Docker镜像"

echo -e "\n${GREEN}部署完成！${NC}"
