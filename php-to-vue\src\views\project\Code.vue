<template>
  <div class="project-code">
    <div class="page-header">
      <h1 class="page-title">项目代码</h1>
      <div class="page-actions">
        <el-button @click="$router.push(`/project/${projectId}`)">
          <i class="el-icon-back"></i> 返回项目详情
        </el-button>
        <el-button type="primary" @click="copyCode">
          <i class="el-icon-document-copy"></i> 复制代码
        </el-button>
      </div>
    </div>

    <el-card shadow="hover" v-loading="loading">
      <template v-if="code">
        <div class="code-container">
          <pre><code>{{ code }}</code></pre>
        </div>
      </template>
      <div v-else-if="!loading" class="empty-state">
        <i class="el-icon-warning-outline"></i>
        <p>没有可用的代码或项目不存在</p>
        <el-button type="primary" @click="$router.push('/project')">返回项目列表</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { viewProjectCode } from '@/api/project'

export default {
  name: 'ProjectCode',
  data() {
    return {
      projectId: this.$route.params.id,
      code: '',
      loading: false
    }
  },
  methods: {
    fetchProjectCode() {
      this.loading = true
      viewProjectCode(this.projectId)
        .then(data => {
          this.code = data
        })
        .catch(error => {
          this.$message.error(`获取项目代码失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    copyCode() {
      if (!this.code) {
        this.$message.warning('没有可复制的代码')
        return
      }
      
      // 创建一个临时textarea元素来复制文本
      const textarea = document.createElement('textarea')
      textarea.value = this.code
      document.body.appendChild(textarea)
      textarea.select()
      
      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success('代码已复制到剪贴板')
        } else {
          this.$message.error('复制失败')
        }
      } catch (err) {
        this.$message.error('复制失败: ' + err)
      }
      
      document.body.removeChild(textarea)
    }
  },
  created() {
    this.fetchProjectCode()
  }
}
</script>

<style scoped>
.project-code {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.code-container {
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 20px;
  max-height: calc(100vh - 180px);
  overflow: auto;
}

.code-container pre {
  margin: 0;
  color: #d4d4d4;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.empty-state i {
  font-size: 48px;
  color: #909399;
  margin-bottom: 20px;
}

.empty-state p {
  color: #606266;
  margin-bottom: 20px;
}
</style> 