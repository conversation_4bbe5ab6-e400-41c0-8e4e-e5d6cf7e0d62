#!/bin/bash

# Go Vue Pro 项目停止脚本
# Linux/WSL 版本

echo "=== Go Vue Pro 项目停止服务 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 进入 docker 目录
cd docker || {
    echo -e "${RED}错误: docker 目录不存在${NC}"
    exit 1
}

# 检查是否有运行中的容器
echo -e "${YELLOW}检查运行中的容器...${NC}"
if ! docker compose ps -q | grep -q .; then
    echo -e "${YELLOW}没有运行中的容器${NC}"
    cd ..
    exit 0
fi

# 显示当前运行的容器
echo -e "${CYAN}当前运行的容器:${NC}"
docker compose ps

# 询问是否停止
echo -e "\n${YELLOW}是否停止所有服务? (Y/n)${NC}"
read -r confirm
if [ "$confirm" = "n" ] || [ "$confirm" = "N" ]; then
    echo -e "${YELLOW}操作已取消${NC}"
    cd ..
    exit 0
fi

# 停止所有服务
echo -e "${YELLOW}正在停止所有服务...${NC}"
docker compose down

# 询问是否清理
echo -e "\n${YELLOW}是否清理未使用的 Docker 资源? (y/N)${NC}"
read -r cleanup
if [ "$cleanup" = "y" ] || [ "$cleanup" = "Y" ]; then
    echo -e "${YELLOW}清理未使用的 Docker 资源...${NC}"
    docker system prune -f
    echo -e "${GREEN}清理完成${NC}"
fi

echo -e "\n${GREEN}✅ 所有服务已停止${NC}"
echo "要重新启动服务，请运行: ./start.sh"

# 返回原目录
cd ..
