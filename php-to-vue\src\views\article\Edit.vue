<template>
  <div class="article-edit-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑公告' : '创建公告' }}</span>
      </div>
      
      <el-form 
        ref="articleForm" 
        :model="articleForm" 
        :rules="rules" 
        label-position="top" 
        v-loading="loading">
        
        <!-- 标题 -->
        <el-form-item label="标题" prop="title">
          <el-input v-model="articleForm.title" placeholder="请输入公告标题"></el-input>
        </el-form-item>
        
        <!-- 作者 -->
        <el-form-item label="作者" prop="author">
          <el-input v-model="articleForm.author" placeholder="请输入作者名称"></el-input>
        </el-form-item>
        
        <!-- 状态 -->
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="articleForm.status">
            <el-radio :label="1">发布</el-radio>
            <el-radio :label="0">草稿</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 内容 -->
        <el-form-item label="内容" prop="description">
          <el-input 
            v-model="articleForm.description" 
            type="textarea" 
            :rows="10" 
            placeholder="请输入公告内容">
          </el-input>
        </el-form-item>
        
        <!-- 按钮 -->
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">提交</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getArticleDetail, createArticle, updateArticle } from '@/api/article'

export default {
  name: 'ArticleEdit',
  data() {
    return {
      isEdit: false,
      loading: false,
      submitLoading: false,
      articleForm: {
        title: '',
        author: '',
        description: '',
        status: 1
      },
      rules: {
        title: [
          { required: true, message: '请输入公告标题', trigger: 'blur' },
          { min: 2, max: 255, message: '长度在 2 到 255 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入公告内容', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择公告状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    if (this.$route.params.id) {
      this.isEdit = true
      this.fetchArticle(this.$route.params.id)
    }
  },
  methods: {
    // 获取公告详情
    fetchArticle(id) {
      this.loading = true
      getArticleDetail(id).then(response => {
        this.articleForm = response
        this.loading = false
      }).catch(() => {
        this.$message.error('获取公告详情失败')
        this.loading = false
      })
    },
    
    // 提交表单
    submitForm() {
      this.$refs.articleForm.validate(valid => {
        if (valid) {
          this.submitLoading = true
          
          const method = this.isEdit 
            ? updateArticle(this.$route.params.id, this.articleForm) 
            : createArticle(this.articleForm)
          
          method.then(() => {
            this.$message({
              type: 'success',
              message: this.isEdit ? '更新成功' : '创建成功'
            })
            this.submitLoading = false
            this.$router.push('/article/list')
          }).catch(error => {
            console.error('提交失败:', error)
            this.$message.error(this.isEdit ? '更新失败' : '创建失败')
            this.submitLoading = false
          })
        }
      })
    },
    
    // 取消
    cancel() {
      this.$router.push('/article/list')
    }
  }
}
</script>

<style scoped>
.article-edit-container {
  padding: 20px;
}

.box-card {
  width: 100%;
  margin-bottom: 20px;
}
</style> 