<template>
  <div class="domain-create">
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? '编辑域名' : '添加域名' }}</h1>
      <el-button @click="$router.push('/domain')">返回列表</el-button>
    </div>

    <el-card shadow="hover" class="form-container">
      <el-form 
        ref="domainForm" 
        :model="domainForm" 
        :rules="rules" 
        label-width="100px"
        v-loading="loading">
        
        <el-form-item label="域名" prop="domain">
          <el-input v-model="domainForm.domain" placeholder="请输入域名"></el-input>
          <span class="form-tip">支持通配符，例如 *.example.com</span>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="domainForm.description" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入域名描述"></el-input>
        </el-form-item>
        
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="domainForm.type">
            <el-radio :label="10">过滤域名</el-radio>
            <el-radio :label="20">切换域名</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="状态" prop="state">
          <el-switch
            v-model="domainForm.state"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用">
          </el-switch>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">保存</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 批量导入框 -->
    <el-card shadow="hover" class="import-container" v-if="!isEdit">
      <div slot="header">
        <span>批量导入域名</span>
      </div>
      <el-form :model="importForm" label-width="100px">
        <el-form-item label="域名列表">
          <el-input 
            type="textarea" 
            :rows="10" 
            placeholder="请输入域名列表，每行一个域名" 
            v-model="importForm.domains"></el-input>
          <span class="form-tip">每行一个域名，支持通配符</span>
        </el-form-item>
        
        <el-form-item label="类型">
          <el-radio-group v-model="importForm.type">
            <el-radio :label="10">过滤域名</el-radio>
            <el-radio :label="20">切换域名</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="importForm.state"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用">
          </el-switch>
        </el-form-item>
        
        <el-form-item>
          <el-button type="success" @click="batchImport" :loading="importing">批量导入</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getDomain, createDomain, updateDomain, batchImportDomains } from '@/api/domain'

export default {
  name: 'DomainCreate',
  data() {
    return {
      isEdit: false,
      domainId: null,
      loading: false,
      submitting: false,
      importing: false,
      
      // 域名表单
      domainForm: {
        domain: '',
        description: '',
        type: 10,
        state: 1
      },
      
      // 表单验证规则
      rules: {
        domain: [
          { required: true, message: '请输入域名', trigger: 'blur' },
          { min: 2, max: 255, message: '长度在 2 到 255 个字符', trigger: 'blur' }
        ],
        description: [
          { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
        ]
      },
      
      // 批量导入表单
      importForm: {
        domains: '',
        type: 10,
        state: 1
      }
    }
  },
  methods: {
    // 提交表单
    submitForm() {
      this.$refs.domainForm.validate(valid => {
        if (!valid) {
          return false
        }
        
        this.submitting = true
        
        if (this.isEdit) {
          // 更新域名
          updateDomain(this.domainId, this.domainForm)
            .then(() => {
              this.$message.success('域名更新成功')
              this.$router.push('/domain')
            })
            .catch(error => {
              this.$message.error(`更新失败: ${error.message}`)
            })
            .finally(() => {
              this.submitting = false
            })
        } else {
          // 创建域名
          createDomain(this.domainForm)
            .then(() => {
              this.$message.success('域名创建成功')
              this.$router.push('/domain')
            })
            .catch(error => {
              this.$message.error(`创建失败: ${error.message}`)
            })
            .finally(() => {
              this.submitting = false
            })
        }
      })
    },
    
    // 重置表单
    resetForm() {
      this.$refs.domainForm.resetFields()
      
      if (!this.isEdit) {
        // 如果是新建，则重置为默认值
        this.domainForm = {
          domain: '',
          description: '',
          type: 10,
          state: 1
        }
      } else {
        // 如果是编辑，则重新获取原始数据
        this.fetchDomainDetail()
      }
    },
    
    // 获取域名详情
    fetchDomainDetail() {
      if (!this.domainId) {
        return
      }
      
      this.loading = true
      getDomain(this.domainId)
        .then(response => {
          this.domainForm = {
            domain: response.domain || '',
            description: response.description || '',
            type: response.type || 10,
            state: response.state || 0
          }
        })
        .catch(error => {
          this.$message.error(`获取域名详情失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    
    // 批量导入域名
    batchImport() {
      if (!this.importForm.domains) {
        this.$message.warning('请输入要导入的域名列表')
        return
      }
      
      // 预处理域名列表，计算有效域名数量
      const domains = this.importForm.domains.split('\n').filter(domain => domain.trim() !== '')
      if (domains.length === 0) {
        this.$message.warning('请输入有效的域名')
        return
      }
      
      this.importing = true
      batchImportDomains(this.importForm)
        .then(responses => {
          this.$message.success(`成功导入 ${responses.length} 个域名`)
          this.importForm.domains = ''
        })
        .catch(error => {
          this.$message.error(`导入失败: ${error.message}`)
        })
        .finally(() => {
          this.importing = false
        })
    }
  },
  created() {
    // 检查URL中是否有id参数，如果有则为编辑模式
    const id = this.$route.query.id
    if (id) {
      this.isEdit = true
      this.domainId = id
      this.fetchDomainDetail()
    }
  }
}
</script>

<style scoped>
.domain-create {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.form-container {
  margin-bottom: 20px;
}

.import-container {
  margin-bottom: 20px;
}

.form-tip {
  display: block;
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
</style> 