package dto

// ProjectRequest 项目请求DTO
type ProjectRequest struct {
	Title         string  `json:"title" validate:"required,min=2,max=255"`
	UniqueKey     string  `json:"unique_key" validate:"required,min=2,max=20"`
	Description   string  `json:"description" validate:"max=255"`
	Code          string  `json:"code"`
	ModuleID      string  `json:"module_id" validate:"required"`
	ModuleExtParam string `json:"module_ext_param"`
	State         int8    `json:"state" validate:"required,oneof=0 1"`
}

// ProjectResponse 项目响应DTO
type ProjectResponse struct {
	ID            uint64 `json:"id"`
	Title         string `json:"title"`
	UniqueKey     string `json:"unique_key"`
	Description   string `json:"description"`
	Code          string `json:"code"`
	UserID        uint64 `json:"user_id"`
	ModuleID      string `json:"module_id"`
	ModuleExtParam string `json:"module_ext_param"`
	IsNewRecord   bool   `json:"is_new_record"`
	State         int8   `json:"state"`
	CreatedAt     string `json:"created_at"`
	UpdatedAt     string `json:"updated_at"`
}

// ProjectListItem 项目列表项DTO
type ProjectListItem struct {
	ID            uint64 `json:"id"`
	Title         string `json:"title"`
	UniqueKey     string `json:"unique_key"`
	Description   string `json:"description"`
	State         int8   `json:"state"`
	UserID        uint64 `json:"user_id"`
	IsNewRecord   bool   `json:"is_new_record"`
	ContentCount  int64  `json:"content_count"` // 项目内容记录数
	Deleted       bool   `json:"deleted"`
	CreatedAt     string `json:"created_at"`
}

// ProjectUpdateRequest 项目更新请求DTO
type ProjectUpdateRequest struct {
	Title         *string `json:"title" validate:"omitempty,min=2,max=255"`
	Description   *string `json:"description" validate:"omitempty,max=255"`
	Code          *string `json:"code"`
	ModuleID      *string `json:"module_id"`
	ModuleExtParam *string `json:"module_ext_param"`
	IsNewRecord   *bool   `json:"is_new_record"`
	State         *int8   `json:"state" validate:"omitempty,oneof=0 1"`
}

// ProjectContentRequest 项目内容请求DTO
type ProjectContentRequest struct {
	ProjectID  uint64 `json:"project_id" validate:"required"`
	Content    string `json:"content" validate:"required"`
	Server     string `json:"server" validate:"required"`
	Domain     string `json:"domain" validate:"required,max=255"`
	Hash       string `json:"hash" validate:"required,max=32"`
	SourceFile string `json:"source_file"`
	Screenshot string `json:"screenshot" validate:"max=500"`
	State      int8   `json:"state" validate:"required,oneof=0 1"`
}

// ProjectContentResponse 项目内容响应DTO
type ProjectContentResponse struct {
	CID         uint64 `json:"cid"`
	ProjectID   uint64 `json:"project_id"`
	UserID      uint64 `json:"user_id"`
	Content     string `json:"content"`
	Server      string `json:"server"`
	Domain      string `json:"domain"`
	Hash        string `json:"hash"`
	SourceFile  string `json:"source_file"` // 源码文件URL路径
	Screenshot  string `json:"screenshot"`  // 屏幕截图URL路径
	State       int8   `json:"state"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// ProjectContentListItem 项目内容列表项DTO
type ProjectContentListItem struct {
	CID        uint64 `json:"cid"`
	ProjectID  uint64 `json:"project_id"`
	Domain     string `json:"domain"`
	Hash       string `json:"hash"`
	Content    string `json:"content"`
	Server     string `json:"server"`
	SourceFile string `json:"source_file"` // 源码文件URL路径
	Screenshot string `json:"screenshot"`  // 屏幕截图URL路径
	State      int8   `json:"state"`
	Deleted    bool   `json:"deleted"`     // 是否已删除
	CreatedAt  string `json:"created_at"`
}

// ProjectContentUpdateRequest 项目内容更新请求DTO
type ProjectContentUpdateRequest struct {
	Content *string `json:"content"`
	Server  *string `json:"server"`
	Domain  *string `json:"domain" validate:"omitempty,max=255"`
	Hash    *string `json:"hash" validate:"omitempty,max=32"`
	State   *int8   `json:"state" validate:"omitempty,oneof=0 1"`
} 