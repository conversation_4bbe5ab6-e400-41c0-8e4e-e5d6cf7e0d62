package queue

import (
	"fmt"
	"go-fiber-api/config"
	"log"
	"net/http"
	"time"

	"github.com/hibiken/asynq"
	"github.com/hibiken/asynqmon"
)

// MonitorOptions 监控选项
type MonitorOptions struct {
	ListenAddr string // 监听地址，例如 ":8080"
	RootPath   string // 根路径，例如 "/monitor"
	Username   string // 基本认证用户名
	Password   string // 基本认证密码
}

// DefaultMonitorOptions 默认监控选项
func DefaultMonitorOptions() MonitorOptions {
	return MonitorOptions{
		ListenAddr: ":8080",
		RootPath:   "/monitoring",
		Username:   "",
		Password:   "",
	}
}

// StartMonitor 启动队列监控服务
func StartMonitor(opts MonitorOptions) {
	cfg := config.LoadConfig().RedisConfig

	// 创建监控处理程序
	h := asynqmon.New(asynqmon.Options{
		RootPath: opts.RootPath,
		RedisConnOpt: asynq.RedisClientOpt{
			Addr:     cfg.Addr,
			Password: cfg.Password,
			DB:       cfg.DB,
		},
	})

	http.Handle(opts.RootPath+"/", h)
	server := &http.Server{
		Addr:         opts.ListenAddr,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
	}

	// 启动监控服务器
	log.Printf("Asynq监控服务已启动: http://%s%s\n", opts.ListenAddr, opts.RootPath)
	if err := server.ListenAndServe(); err != nil {
		if err != http.ErrServerClosed {
			log.Fatalf("监控服务器启动失败: %v", err)
		}
	}
}

// GetQueueStats 获取队列统计信息
func GetQueueStats() (map[string]interface{}, error) {
	cfg := config.LoadConfig().RedisConfig
	inspector := asynq.NewInspector(asynq.RedisClientOpt{
		Addr:     cfg.Addr,
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	// 获取队列信息
	info, err := inspector.GetQueueInfo("default")
	if err != nil {
		return nil, fmt.Errorf("获取队列信息失败: %v", err)
	}

	result := make(map[string]interface{})
	result["size"] = info.Size
	result["pending"] = info.Pending
	result["active"] = info.Active
	result["scheduled"] = info.Scheduled
	result["retry"] = info.Retry
	result["archived"] = info.Archived
	result["completed"] = info.Completed
	result["processed"] = info.Processed
	result["failed"] = info.Failed
	result["paused"] = info.Paused
	result["latency_seconds"] = info.Latency.Seconds()
	result["display_latency"] = info.Latency.String()

	return result, nil
} 