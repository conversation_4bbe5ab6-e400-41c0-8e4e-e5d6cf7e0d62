<template>
  <div class="forgot-container">
    <!-- 背景矩阵雨特效 -->
    <canvas id="matrix-canvas" ref="matrixCanvas"></canvas>
    
    <div class="forgot-box">
      <h1 class="forgot-title">XSS</h1>
      <p class="forgot-subtitle">找回您的账号密码</p>
      
      <el-card class="forgot-card">
        <el-form :model="forgotForm" :rules="rules" ref="forgotForm" label-width="0" @submit.native.prevent>
          <!-- 用户名 -->
          <el-form-item prop="username">
            <el-input 
              v-model="forgotForm.username" 
              placeholder="用户名" 
              prefix-icon="el-icon-user">
            </el-input>
          </el-form-item>
          
          <!-- 邮箱 -->
          <el-form-item prop="email">
            <el-input 
              v-model="forgotForm.email" 
              placeholder="邮箱" 
              prefix-icon="el-icon-message">
            </el-input>
          </el-form-item>
          
          <!-- 验证码 -->
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input v-model="forgotForm.captcha" placeholder="验证码" prefix-icon="el-icon-key"></el-input>
              <div class="captcha-image" @click="refreshCaptcha">
                <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                <div v-else class="captcha-loading">
                  <i class="el-icon-loading"></i>
                </div>
              </div>
            </div>
          </el-form-item>
          
          <!-- 提交按钮 -->
          <el-form-item>
            <el-button type="primary" :loading="loading" class="forgot-button" @click="handleSubmit">
              提交 <i class="el-icon-right"></i>
            </el-button>
          </el-form-item>
          
          <!-- 额外链接 -->
          <div class="forgot-links">
            <router-link to="/login" class="link">返回登录</router-link>
            <router-link to="/signup" class="link">注册新账号</router-link>
          </div>
        </el-form>
      </el-card>
      
      <!-- 版权信息 -->
      <div class="copyright">
        © 2020 - {{ new Date().getFullYear() }} XSS测试平台
      </div>
    </div>
  </div>
</template>

<script>
import { getCaptcha, forgotPassword } from '@/api/auth'

export default {
  name: 'ForgotPassword',
  data() {
    return {
      loading: false,
      captchaImage: '',
      captchaKey: '',
      forgotForm: {
        username: '',
        email: '',
        captcha: '',
        captcha_key: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        captcha: [
          { required: true, message: '请输入验证码', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 获取验证码
    this.refreshCaptcha()
  },
  mounted() {
    // 初始化矩阵雨效果
    this.initMatrixRain()
    // 窗口大小改变时重新调整画布大小
    window.addEventListener('resize', this.resizeCanvas)
  },
  beforeDestroy() {
    // 清除事件监听
    window.removeEventListener('resize', this.resizeCanvas)
    // 停止动画
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
  },
  methods: {
    // 刷新验证码
    refreshCaptcha() {
      getCaptcha().then(res => {
        this.captchaImage = res.image_data
        this.captchaKey = res.captcha_key
        this.forgotForm.captcha_key = res.captcha_key
      }).catch(() => {
        this.$message.error('获取验证码失败，请重试')
      })
    },
    
    // 处理找回密码逻辑
    handleSubmit() {
      this.$refs.forgotForm.validate(valid => {
        if (valid) {
          this.loading = true
          
          forgotPassword(this.forgotForm)
            .then(() => {
              this.$message({
                type: 'success',
                message: '密码重置邮件已发送，请检查您的邮箱'
              })
              setTimeout(() => {
                this.$router.push('/login')
              }, 1500)
            })
            .catch(error => {
              // 提取更详细的错误信息
              let errorMsg = '找回密码失败，请重试'
              if (error.response && error.response.data) {
                errorMsg = error.response.data.message || errorMsg
              } else if (error.message) {
                errorMsg = error.message
              }
              this.$message.error(errorMsg)
              
              // 刷新验证码
              this.refreshCaptcha()
              this.forgotForm.captcha = ''
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    
    // 初始化矩阵雨效果
    initMatrixRain() {
      const canvas = this.$refs.matrixCanvas
      const ctx = canvas.getContext('2d')
      
      // 设置画布大小
      this.resizeCanvas()
      
      // 字符集
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz$+-*/=%"\'#&_(),.;:?!\\|{}<>[]^~';
      
      // 每列的当前位置
      const drops = []
      
      // 初始化 drops
      for (let i = 0; i < canvas.width / 20; i++) {
        drops[i] = 1
      }
      
      // 绘制矩阵雨
      const draw = () => {
        // 半透明黑色背景，形成拖尾效果
        ctx.fillStyle = 'rgba(0, 0, 0, 0.05)'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        
        // 设置字体
        ctx.font = '15px monospace'
        
        // 循环绘制每一列
        for (let i = 0; i < drops.length; i++) {
          // 随机选择一个字符
          const text = chars[Math.floor(Math.random() * chars.length)]
          
          // 随机字符颜色
          const green = Math.floor(Math.random() * 156 + 100) // 生成较亮的绿色
          ctx.fillStyle = `rgba(0, ${green}, 0, 0.8)`
          
          // 绘制字符
          ctx.fillText(text, i * 20, drops[i] * 20)
          
          // 更新位置
          if (drops[i] * 20 > canvas.height && Math.random() > 0.975) {
            drops[i] = 0
          }
          
          drops[i]++
        }
        
        // 动画循环
        this.animationId = requestAnimationFrame(draw)
      }
      
      // 开始动画
      draw()
    },
    
    // 调整画布大小
    resizeCanvas() {
      const canvas = this.$refs.matrixCanvas
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }
  }
}
</script>

<style scoped>
.forgot-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #000;
  color: #fff;
}

#matrix-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.forgot-box {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 450px;
  width: 90%;
}

.forgot-title {
  font-size: 3rem;
  font-weight: 300;
  margin-bottom: 0.5rem;
  color: #fff;
}

.forgot-subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
}

.forgot-card {
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(50, 205, 50, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 0 10px rgba(50, 205, 50, 0.2);
}

.forgot-card /deep/ .el-card__body {
  padding: 20px 30px;
}

.forgot-card /deep/ .el-input__inner {
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(50, 205, 50, 0.5);
  color: #fff;
}

.forgot-card /deep/ .el-input__icon {
  color: rgba(50, 205, 50, 0.8);
}

.forgot-card /deep/ .el-form-item__label {
  color: #fff;
}

.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-image {
  margin-left: 10px;
  width: 120px;
  height: 40px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(50, 205, 50, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.captcha-image img {
  max-width: 100%;
  max-height: 100%;
}

.captcha-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: rgba(50, 205, 50, 0.8);
}

.forgot-button {
  width: 100%;
  background-color: rgba(50, 205, 50, 0.7);
  border-color: rgba(50, 205, 50, 0.9);
  transition: all 0.3s;
}

.forgot-button:hover {
  background-color: rgba(50, 205, 50, 0.9);
  box-shadow: 0 0 10px rgba(50, 205, 50, 0.7);
}

.forgot-links {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.link {
  color: rgba(50, 205, 50, 0.9);
  text-decoration: none;
  transition: all 0.3s;
}

.link:hover {
  color: #fff;
  text-shadow: 0 0 5px rgba(50, 205, 50, 0.9);
}

.copyright {
  margin-top: 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}
</style> 