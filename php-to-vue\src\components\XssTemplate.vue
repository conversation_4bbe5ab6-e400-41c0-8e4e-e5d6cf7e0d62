<template>
    <div class="xss-template">
        <!-- 极限代码 -->
        <div class="code-block">
            <h4>标准代码</h4>
            <el-input type="input" :value="standardCode" class="code-textarea" slot="append">
                <template #append>
                    <el-button size="small" type="primary" @click="copyCode(standardCode)">复制代码</el-button>
                </template>
            </el-input>
        </div>

        <div class="code-block">
            <h4>或者上面代码转换URL一次编码</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="3" :value="urlEncodedCode" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(urlEncodedCode)">复制代码</el-button>
            </div>
        </div>

        <div class="code-block">
            <h4>或者上面代码转换URL二次编码</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="3" :value="doubleUrlEncodedCode" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(doubleUrlEncodedCode)">复制代码</el-button>
            </div>
        </div>

        <!-- 加载其他代码 -->
        <div class="code-block">
            <h4>或者</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="3" :value="inputTagCode" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(inputTagCode)">复制代码</el-button>
            </div>
        </div>

        <div class="code-block">
            <h4>或者</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="3" :value="imgTagCode" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(imgTagCode)">复制代码</el-button>
            </div>
        </div>

        <div class="code-block">
            <h4>再或者 IMG 标签</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="3" :value="imgDetectionCode" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(imgDetectionCode)">复制代码</el-button>
            </div>
        </div>

        <div class="code-block">
            <h4>再或者以你任何想要的方式插入</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="3" :value="customImgCode" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(customImgCode)">复制代码</el-button>
            </div>
        </div>

        <div class="code-block">
            <h4 class="text-danger">通杀火狐谷歌360</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="3" :value="universalCode" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(universalCode)">复制代码</el-button>
            </div>
        </div>

        <div class="code-block">
            <h4 class="text-danger">标签iframe等，实体10进制编码</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="6" :value="iframe10Code" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(iframe10Code)">复制代码</el-button>
            </div>
        </div>

        <div class="code-block">
            <h4 class="text-danger">以上实体10进制编码进行一次URL编码</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="8" :value="iframe10CodeUrl" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(iframe10CodeUrl)">复制代码</el-button>
            </div>
        </div>

        <div class="code-block">
            <h4 class="text-danger">实体16进制编码</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="8" :value="iframe16Code" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(iframe16Code)">复制代码</el-button>
            </div>
        </div>

        <div class="code-block">
            <h4 class="text-danger">以上实体16进制编码进行一次URL编码</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="4" :value="iframe16CodeUrl" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(iframe16CodeUrl)">复制代码</el-button>
            </div>
        </div>

        <!-- XSS BY WAF -->
        <div class="code-block">
            <h4>若使用下方XSS代码请注意(下面代码会引起网页空白不得已慎用，注意如果使用下面的代码，一定要勾选"基础默认XSS"模块)</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="3" :value="wafBypassCode" class="code-textarea" />
                <el-button size="small" type="primary" @click="copyCode(wafBypassCode)">复制代码</el-button>
            </div>
        </div>

        <!-- 图片探测 -->
        <div class="code-block">
            <h4 class="text-danger">图片探测系统，只要对方网站可以调用外部图片(或可自定义HTML)，请填入下方图片地址(代码)，则探测对方数据</h4>
            <el-input type="input" :value="imgDetection1" class="code-textarea" slot="append">
                <template #append>
                    <el-button size="small" type="primary" @click="copyCode(imgDetection1)">复制代码</el-button>
                </template>
            </el-input>
            <el-input type="input" :value="imgDetection2" class="code-textarea" slot="append">
                <template #append>
                    <el-button size="small" type="primary" @click="copyCode(imgDetection2)">复制代码</el-button>
                </template>
            </el-input>
            <el-input type="input" :value="imgDetection3" class="code-textarea" slot="append">
                <template #append>
                    <el-button size="small" type="primary" @click="copyCode(imgDetection3)">复制代码</el-button>
                </template>
            </el-input>
        </div>

        <!-- 极限代码 -->
        <div class="code-block">
            <h4>极限代码 (可以不加最后的>回收符号，下面代码已测试成功)</h4>
            <div class="code-textarea-container">
                <el-input type="textarea" :rows="3" readonly :value="minimalCode" class="code-textarea" slot="append" />
                <el-button size="small" type="primary" @click="copyCode(minimalCode)">复制代码</el-button>
            </div>
        </div>
    </div>
</template>

<script>
// 导入xssTemplate工具
import xssTemplate from '@/utils/xssTemplate';

export default {
    name: 'XssTemplate',
    props: {
        domain: {
            type: String,
            required: true
        },
        protocol: {
            type: String,
            default: 'http'
        },
        uniqueKey: {
            type: String,
            required: true
        },
        templateContent: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            randKey: Math.random().toString(36).substring(2, 8)
        };
    },
    computed: {
        fullUrl() {
            return this.protocol.toLowerCase() + this.domain;
        },
        shortUrl() {
            return '//' + this.domain;
        },
        baseUrl() {
            return this.fullUrl + '/' + this.uniqueKey;
        },
        // 各种XSS代码
        standardCode() {
            return xssTemplate.standardCode(this.fullUrl, this.uniqueKey);
        },
        urlEncodedCode() {
            return xssTemplate.urlEncodedOnce(this.fullUrl, this.uniqueKey);
        },
        doubleUrlEncodedCode() {
            return xssTemplate.urlEncodedTwice(this.fullUrl, this.uniqueKey);
        },
        inputTagCode() {
            return xssTemplate.inputElementCode(this.fullUrl, this.uniqueKey);
        },
        imgTagCode() {
            return xssTemplate.imgElementCode(this.fullUrl, this.uniqueKey);
        },
        imgDetectionCode() {
            return xssTemplate.otherImgCode(this.fullUrl, this.uniqueKey);
        },
        customImgCode() {
            return xssTemplate.anotherImgCode(this.fullUrl, this.uniqueKey);
        },
        universalCode() {
            return xssTemplate.imgCodeForBrowsers(this.fullUrl, this.uniqueKey);
        },
        iframe10Code() {
            return xssTemplate.iframeEntity10Code(this.fullUrl, this.uniqueKey);
        },
        iframe10CodeUrl() {
            return xssTemplate.iframeEntity10UrlEncoded(this.fullUrl, this.uniqueKey);
        },
        iframe16Code() {
            return xssTemplate.iframeEntity16Code(this.fullUrl, this.uniqueKey);
        },
        iframe16CodeUrl() {
            return xssTemplate.iframeEntity16UrlEncoded(this.fullUrl, this.uniqueKey);
        },
        wafBypassCode() {
            return xssTemplate.imgCodeForWAF(this.fullUrl, this.uniqueKey);
        },
        imgDetection1() {
            return xssTemplate.imgDetectCode1(this.fullUrl, this.uniqueKey, this.randKey);
        },
        imgDetection2() {
            return xssTemplate.imgDetectCode2(this.fullUrl, this.uniqueKey, this.randKey);
        },
        imgDetection3() {
            return xssTemplate.imgDetectCode3(this.fullUrl, this.uniqueKey, this.randKey);
        },
        minimalCode() {
            return xssTemplate.minimalCode(this.shortUrl, this.uniqueKey);
        }
    },
    methods: {
        copyCode(code) {
            navigator.clipboard.writeText(code).then(() => {
                this.$message.success('代码已复制到剪贴板');
            }).catch(() => {
                this.$message.error('复制失败，请手动复制');
            });
        }
    }
};
</script>

<style scoped>
.xss-template {
    padding: 10px;
}

.code-block {
    margin-bottom: 5px;
    padding: 5px;
    background-color: #f8f8f8;
    border-radius: 4px;
    border: 1px solid #ebeef5;
}

.code-block h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #606266;
}

.code-textarea {
    margin-bottom: 10px;
    font-family: 'Courier New', Courier, monospace;
}

.text-danger {
    color: #F56C6C !important;
}

.code-textarea-container {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: center;
}
</style>