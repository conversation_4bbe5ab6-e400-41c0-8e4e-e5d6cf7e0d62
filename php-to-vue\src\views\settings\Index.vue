<template>
  <div class="settings-container">
    <el-card class="settings-card">
      <div slot="header" class="header">
        <h2>网站设置</h2>
        <el-button type="primary" size="small" :loading="loading" @click="saveSettings">保存设置</el-button>
      </div>

      <el-form ref="settingsForm" :model="form" :rules="rules" label-width="120px" v-loading="loading">
        <el-divider content-position="left">基本信息</el-divider>
        
        <el-form-item label="网站名称" prop="site_name">
          <el-input v-model="form.site_name" placeholder="请输入网站名称"></el-input>
        </el-form-item>
        
        <el-form-item label="网站描述" prop="site_description">
          <el-input type="textarea" v-model="form.site_description" placeholder="请输入网站描述"></el-input>
        </el-form-item>
        
        <el-form-item label="网站关键词" prop="site_keywords">
          <el-input v-model="form.site_keywords" placeholder="请输入网站关键词，多个关键词用逗号分隔"></el-input>
        </el-form-item>
        
        <el-form-item label="网站Logo" prop="site_logo">
          <el-input v-model="form.site_logo" placeholder="请输入网站Logo的URL地址"></el-input>
        </el-form-item>
        
        <el-form-item label="网站Favicon" prop="site_favicon">
          <el-input v-model="form.site_favicon" placeholder="请输入网站Favicon的URL地址"></el-input>
        </el-form-item>
        
        <el-divider content-position="left">系统状态</el-divider>
        
        <el-form-item label="网站状态">
          <el-switch
            v-model="form.site_closed"
            active-text="关闭"
            inactive-text="开启"
            :active-value="true"
            :inactive-value="false">
          </el-switch>
        </el-form-item>
        
        <el-form-item label="关闭提示信息" v-if="form.site_closed">
          <el-input type="textarea" v-model="form.closed_message" placeholder="网站关闭时显示的提示信息"></el-input>
        </el-form-item>
        
        <el-form-item label="注册功能">
          <el-switch
            v-model="form.register_closed"
            active-text="关闭"
            inactive-text="开启"
            :active-value="true"
            :inactive-value="false">
          </el-switch>
        </el-form-item>
        
        <el-divider content-position="left">邮件设置</el-divider>
        
        <el-form-item label="邮件服务器" prop="mail_host">
          <el-input v-model="form.mail_host" placeholder="请输入邮件服务器地址"></el-input>
        </el-form-item>
        
        <el-form-item label="邮件端口" prop="mail_port">
          <el-input-number v-model="form.mail_port" :min="1" :max="65535" placeholder="请输入邮件服务器端口"></el-input-number>
        </el-form-item>
        
        <el-form-item label="邮件用户名" prop="mail_username">
          <el-input v-model="form.mail_username" placeholder="请输入邮件用户名"></el-input>
        </el-form-item>
        
        <el-form-item label="邮件密码" prop="mail_password">
          <el-input v-model="form.mail_password" type="password" placeholder="请输入邮件密码"></el-input>
        </el-form-item>
        
        <el-form-item label="发件人名称" prop="mail_from_name">
          <el-input v-model="form.mail_from_name" placeholder="请输入发件人名称"></el-input>
        </el-form-item>
        
        <el-form-item label="发件人邮箱" prop="mail_from_email">
          <el-input v-model="form.mail_from_email" placeholder="请输入发件人邮箱"></el-input>
        </el-form-item>

        <el-divider content-position="left">其他设置</el-divider>
        
        <el-form-item label="网站公告" prop="site_notice">
          <el-input type="textarea" v-model="form.site_notice" placeholder="请输入网站公告" :rows="4"></el-input>
        </el-form-item>
        
        <el-form-item label="页脚信息" prop="site_footer">
          <el-input type="textarea" v-model="form.site_footer" placeholder="请输入页脚信息" :rows="4"></el-input>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getSettings, updateSettings } from '@/api/settings'

export default {
  name: 'Settings',
  data() {
    return {
      loading: false,
      form: {
        site_name: '',
        site_description: '',
        site_keywords: '',
        site_logo: '',
        site_favicon: '',
        site_closed: false,
        closed_message: '',
        register_closed: false,
        mail_host: '',
        mail_port: 25,
        mail_username: '',
        mail_password: '',
        mail_from_name: '',
        mail_from_email: '',
        site_notice: '',
        site_footer: ''
      },
      rules: {
        site_name: [
          { required: true, message: '请输入网站名称', trigger: 'blur' }
        ],
        mail_from_email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchSettings()
  },
  methods: {
    fetchSettings() {
      this.loading = true
      getSettings()
        .then(response => {
          console.log('获取到的设置数据:', response)
          
          // 字段映射，处理后端与前端字段名可能不一致的情况
          if (response) {
            // 处理驼峰命名和下划线命名的映射
            const fieldMapping = {
              site_name: 'site_name',
              site_description: 'site_description',
              site_keywords: 'site_keywords',
              site_logo: 'site_logo',
              site_favicon: 'site_favicon',
              site_footer: 'site_footer',
              site_notice: 'site_notice',
              site_closed: 'site_closed',
              closed_message: 'closed_message',
              register_closed: 'register_closed',
              mail_host: 'mail_host',
              mail_port: 'mail_port',
              mail_username: 'mail_username',
              mail_password: 'mail_password',
              mail_from_name: 'mail_from_name',
              mail_from_email: 'mail_from_email',
              // 可能的驼峰命名字段映射
              siteName: 'site_name',
              siteDescription: 'site_description',
              siteKeywords: 'site_keywords',
              siteLogo: 'site_logo',
              siteFavicon: 'site_favicon',
              siteFooter: 'site_footer',
              siteNotice: 'site_notice',
              siteClosed: 'site_closed',
              closedMessage: 'closed_message',
              registerClosed: 'register_closed',
              mailHost: 'mail_host',
              mailPort: 'mail_port', 
              mailUsername: 'mail_username',
              mailPassword: 'mail_password',
              mailFromName: 'mail_from_name',
              mailFromEmail: 'mail_from_email'
            }
            
            // 初始化一个新的表单对象
            const newForm = { ...this.form }
            
            // 遍历响应数据，根据映射设置表单值
            Object.keys(response).forEach(key => {
              const formKey = fieldMapping[key] || key
              if (formKey in newForm) {
                newForm[formKey] = response[key]
              }
            })
            
            // 更新表单数据
            this.form = newForm
          }
          
          this.loading = false
        })
        .catch(error => {
          console.error('获取设置失败:', error)
          this.$message.error('获取网站设置失败：' + error.message)
          this.loading = false
        })
    },
    saveSettings() {
      this.$refs.settingsForm.validate(valid => {
        if (!valid) {
          return false
        }
        
        this.loading = true
        
        // 确保数据类型正确
        // 根据后端 SettingsUpdateRequest 的需求，每个字段都需要是指针类型
        // 为了模拟这种行为，我们需要提供明确的值而不是undefined或null
        const formData = {
          site_name: this.form.site_name || "",
          site_description: this.form.site_description || "",
          site_keywords: this.form.site_keywords || "",
          site_logo: this.form.site_logo || "",
          site_favicon: this.form.site_favicon || "",
          site_footer: this.form.site_footer || "",
          site_notice: this.form.site_notice || "",
          closed_message: this.form.closed_message || "",
          mail_host: this.form.mail_host || "",
          mail_username: this.form.mail_username || "",
          mail_password: this.form.mail_password || "",
          mail_from_name: this.form.mail_from_name || "",
          mail_from_email: this.form.mail_from_email || "",
          mail_port: Number(this.form.mail_port) || 0,
          site_closed: Boolean(this.form.site_closed),
          register_closed: Boolean(this.form.register_closed)
        }
        
        console.log('保存的设置数据:', formData)
        
        updateSettings(formData)
          .then(response => {
            console.log('设置更新成功:', response)
            this.$message.success('网站设置更新成功')
            // 重新获取最新设置以显示服务器返回的数据
            this.fetchSettings()
          })
          .catch(error => {
            console.error('更新设置失败:', error)
            this.$message.error('更新网站设置失败：' + error.message)
            this.loading = false
          })
      })
    }
  }
}
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

.settings-card {
  margin-bottom: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 