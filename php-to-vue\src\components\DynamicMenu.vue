<template>
  <div class="dynamic-menu">
    <template v-for="item in menuItems">
      <!-- 无子菜单的菜单项 -->
      <el-menu-item
        v-if="!item.children || item.children.length === 0"
        :key="item.id"
        :index="item.uri || ''"
        @click="handleMenuClick(item)"
      >
        <i :class="item.icon || 'el-icon-menu'"></i>
        <span slot="title">{{ item.title }}</span>
      </el-menu-item>

      <!-- 有子菜单的菜单项 -->
      <el-submenu v-else :key="item.id" :index="item.uri || String(item.id)">
        <template slot="title">
          <i :class="item.icon || 'el-icon-folder'"></i>
          <span>{{ item.title }}</span>
        </template>

        <!-- 递归渲染子菜单 -->
        <dynamic-menu :menu-items="item.children" @menu-click="$emit('menu-click', $event)"></dynamic-menu>
      </el-submenu>
    </template>
  </div>
</template>

<script>
// 导入获取当前用户信息的API
import { getCurrentUser } from '@/api/auth'

export default {
  name: "DynamicMenu",
  props: {
    menuItems: {
      type: Array,
      required: true,
    },
  },
  methods: {
    // 获取用户菜单数据
    fetchUserMenus() {
      this.loading = true
      
      // 使用新的API获取当前用户的完整信息，包括菜单
      getCurrentUser()
        .then(response => {
          console.log('获取用户数据成功:', response)
          
          // 从响应中获取菜单数据
          const menuList = response.menus || []
          console.log('用户菜单数据:', menuList)
          
          // 检查用户是否是管理员
          const isAdmin = response.user ? response.user.is_admin : false
          console.log('用户是否为管理员:', isAdmin)
          
          // 构建树形结构
          this.menuTree = this.buildMenuTree(menuList)
          console.log('构建的菜单树:', this.menuTree)
        })
        .catch(error => {
          console.error('获取用户菜单失败:', error)
          this.$message.error('加载菜单失败，请刷新页面重试')
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleMenuClick(item) {
      // 如果有URI，则进行路由跳转
      if (item.uri) {
        // 发出菜单点击事件，由父组件处理路由导航
        this.$emit('menu-click', item);
      }
    }
  }
};
</script>

<style scoped>
.dynamic-menu .el-menu-item.is-active {
  color: #409EFF;
  background-color: #ecf5ff;
}
</style>
