<template>
  <div id="app">
    <template v-if="isAuth">
      <Layout>
        <router-view />
      </Layout>
    </template>
    <template v-else>
      <router-view />
    </template>
  </div>
</template>

<script>
import Layout from '@/components/Layout.vue'
import { mapGetters } from 'vuex'
import webSocketService from '@/utils/websocket'

export default {
  name: 'App',
  components: {
    Layout
  },
  computed: {
    ...mapGetters(['isLoggedIn']),
    isAuth() {
      return this.$route.meta.requiresAuth && this.isLoggedIn
    }
  },
  created() {
    // 检查是否有token，但token已过期
    if (this.isLoggedIn) {
      // 可以在此处添加token验证逻辑，例如向后端发送请求验证token
      // 如果token无效，可以清除它并重定向到登录页面
      // this.$store.dispatch('checkToken')
      
      // 连接WebSocket
      this.connectWebSocket()
    }
  },
  methods: {
    connectWebSocket() {
      // 初始化WebSocket连接
      webSocketService.connect()
      
      // 检查WebSocket连接状态
      setTimeout(() => {
        console.log('WebSocket连接状态:', webSocketService.connected ? '已连接' : '未连接')
        if (!webSocketService.connected) {
          console.warn('WebSocket连接失败，尝试重新连接...')
          webSocketService.connect()
        }
      }, 3000)
    }
  },
  beforeDestroy() {
    // 组件销毁时关闭WebSocket连接
    webSocketService.close()
  }
}
</script>

<style>
body {
  margin: 0;
  padding: 0;
}
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
.el-header {
  padding: 0;
}
.el-footer {
  text-align: center;
  line-height: 60px;
}
</style> 