# Go Vue Pro 项目服务检查脚本

Write-Host "=== Go Vue Pro 项目服务检查 ===" -ForegroundColor Green

Write-Host "`n检查前端服务..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ 前端应用: 正常运行 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ 前端应用: 无法访问" -ForegroundColor Red
}

Write-Host "`n检查API健康检查..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost/health" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ API 健康检查: 正常 (响应: $($response.Content))" -ForegroundColor Green
} catch {
    Write-Host "❌ API 健康检查: 失败" -ForegroundColor Red
}

Write-Host "`n检查队列监控..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 10 -UseBasicParsing
    Write-Host "✅ 队列监控: 正常运行 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 队列监控: 可能需要配置" -ForegroundColor Yellow
}

Write-Host "`n🎉 部署成功！" -ForegroundColor Green
Write-Host "`n📱 主要访问地址:" -ForegroundColor Cyan
Write-Host "   前端应用: http://localhost" -ForegroundColor White
Write-Host "   API健康检查: http://localhost/health" -ForegroundColor White
Write-Host "   队列监控: http://localhost:8080" -ForegroundColor White

Write-Host "`n🗄️ 数据库连接信息:" -ForegroundColor Cyan
Write-Host "   MySQL: localhost:3308 (用户: root, 密码: 123456)" -ForegroundColor White
Write-Host "   Redis: localhost:6379" -ForegroundColor White

$openBrowser = Read-Host "`n是否现在打开浏览器访问应用? (Y/n)"
if ($openBrowser -ne "n" -and $openBrowser -ne "N") {
    Write-Host "正在打开浏览器..." -ForegroundColor Yellow
    Start-Process "http://localhost"
}
