package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupDomainRoutes 设置域名管理相关路由
func SetupDomainRoutes(router fiber.Router) {
	// 域名路由组
	domains := router.Group("/domain")
	
	// 添加认证中间件
	domains.Use(utils.RequireAuthentication)

	// 域名CRUD路由
	domains.Get("/index", middleware.RequirePermission("domain.view"), handlers.GetDomains)           // 获取域名列表
	domains.Get("/index/:id", middleware.RequirePermission("domain.view"), handlers.GetDomain)        // 获取单个域名
	domains.Post("/index", middleware.RequirePermission("domain.create"), handlers.CreateDomain)        // 创建域名
	domains.Put("/index/:id", middleware.RequirePermission("domain.edit"), handlers.UpdateDomain)     // 更新域名
	domains.Delete("/index/:id", middleware.RequirePermission("domain.delete"), handlers.DeleteDomain)  // 删除域名
} 