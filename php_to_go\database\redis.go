package database

import (
	"context"
	"log"

	"go-fiber-api/config"

	"github.com/go-redis/redis/v8"
)

var (
	Rdb      *redis.Client
	RedisCtx = context.Background()
)

// InitRedis 初始化Redis连接
func InitRedis() error {
	cfg := config.LoadConfig().RedisConfig

	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Addr,
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	// 测试连接
	_, err := rdb.Ping(RedisCtx).Result()
	if err != nil {
		log.Printf("Redis连接失败: %v\n", err)
		return err
	}

	log.Println("Redis连接成功")
	Rdb = rdb // Assign to global variable after successful ping
	return nil
}
