import axios from 'axios'

// 创建 axios 实例
const service = axios.create({
  baseURL: 'http://localhost:3000/api',
  // 根据环境动态设置baseURL
  // baseURL: process.env.NODE_ENV === 'production' 
  //   ? `${window.location.protocol}//${window.location.host}/api` // 生产环境使用当前域名
  //   : process.env.VUE_APP_BASE_API, // 开发环境使用环境变量
  timeout: 5000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    // 从localStorage获取token并添加到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
      console.log('添加授权头:', `Bearer ${token}`)
    } else {
      console.warn('没有找到token')
    }
    
    // 打印请求信息，便于调试
    console.log(`发送 ${config.method.toUpperCase()} 请求到: ${config.baseURL}${config.url}`, config.data || '')
    
    return config
  },
  error => {
    // 处理请求错误
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    console.log('API响应数据:', res)
    
    // 如果业务状态码不为0，表示有错误
    if (res.code !== 0) {
      // 可以在这里处理一些特定的错误，例如token失效
      if (res.code === 401) {
        // 检查是否是登出请求
        const isLogoutRequest = response.config.url.includes('/auth/logout');
        
        // 清除token并重定向到登录页
        localStorage.removeItem('token');
        
        // 如果不是登出请求，才重定向
        if (!isLogoutRequest && window.location.hash !== '#/login') {
          window.location.href = '/#/login';
        }
      }
      return Promise.reject(new Error(res.message || '接口请求错误'))
    } else {
      // 返回数据，确保返回的是res.data
      return res.data || {}
    }
  },
  error => {
    console.log('err:', error) // for debug
    
    // 更详细地打印错误信息
    if (error.response) {
      console.log('错误状态码:', error.response.status)
      console.log('错误数据:', error.response.data)
    }
    
    let message = error.message || '请求失败'
    if (error.response) {
      // 如果响应中包含详细错误信息，优先使用
      if (error.response.data && error.response.data.message) {
        message = error.response.data.message
      } else {
        switch (error.response.status) {
          case 400:
            message = '请求参数错误'
            break
          case 401:
            message = '未授权，请重新登录'
            
            // 检查是否是登出请求或已经在登录页
            const isLogoutRequest = error.config && error.config.url && error.config.url.includes('/auth/logout');
            const isLoginPage = window.location.hash === '#/login';
            
            // 清除token
            localStorage.removeItem('token')
            
            // 如果不是登出请求且不在登录页，才重定向
            if (!isLogoutRequest && !isLoginPage) {
              window.location.href = '/#/login'
            }
            break
          case 403:
            message = '拒绝访问'
            break
          case 404:
            message = '请求错误,未找到该资源'
            break
          case 500:
            message = '服务器端出错'
            break
          default:
            message = `连接错误${error.response.status}`
        }
      }
    }
    
    // 创建一个包含更多信息的错误对象
    const enhancedError = new Error(message)
    enhancedError.response = error.response
    enhancedError.config = error.config
    
    return Promise.reject(enhancedError)
  }
)

export default service 