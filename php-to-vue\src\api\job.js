import request from './index'

/**
 * 获取任务列表
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getJobs(params) {
  return request({
    url: '/admin/job/index',
    method: 'get',
    params
  })
}

/**
 * 获取任务详情
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function getJob(id) {
  return request({
    url: `/admin/job/index/${id}`,
    method: 'get'
  })
}

/**
 * 创建任务
 * @param {Object} data - 任务数据
 * @returns {Promise}
 */
export function createJob(data) {
  return request({
    url: '/admin/job/index',
    method: 'post',
    data
  })
}

/**
 * 更新任务
 * @param {number} id - 任务ID
 * @param {Object} data - 更新数据
 * @returns {Promise}
 */
export function updateJob(id, data) {
  return request({
    url: `/admin/job/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除任务
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function deleteJob(id) {
  return request({
    url: `/admin/job/index/${id}`,
    method: 'delete'
  })
}

/**
 * 清空所有任务
 * @returns {Promise}
 */
export function clearAllJobs() {
  return request({
    url: '/admin/job/clear-all',
    method: 'delete'
  })
} 