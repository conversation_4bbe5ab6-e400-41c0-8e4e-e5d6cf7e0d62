package queue

import (
	"go-fiber-api/config"
	"log"
	"sync"

	"github.com/hibiken/asynq"
)

var (
	client     *Client
	clientOnce sync.Once
	clientMux  sync.Mutex
)

// Client 队列客户端
type Client struct {
	client *asynq.Client
}

// NewClient 创建新的队列客户端
func NewClient() *Client {
	clientOnce.Do(func() {
		cfg := config.LoadConfig().RedisConfig
		client = &Client{
			client: asynq.NewClient(asynq.RedisClientOpt{
				Addr:     cfg.Addr,
				Password: cfg.Password,
				DB:       cfg.DB,
			}),
		}
		log.Println("Asynq客户端初始化成功")
	})
	return client
}

// GetClient 获取全局单例客户端，不需要手动关闭
func GetClient() *Client {
	return NewClient()
}

// Enqueue 将任务入队（立即执行）
func (c *Client) Enqueue(task *asynq.Task) (*asynq.TaskInfo, error) {
	return c.client.Enqueue(task)
}

// EnqueueWithOptions 将任务入队（带选项）
func (c *Client) EnqueueWithOptions(task *asynq.Task, opts ...asynq.Option) (*asynq.TaskInfo, error) {
	return c.client.Enqueue(task, opts...)
}

// Close 关闭客户端
func (c *Client) Close() error {
	// 单例客户端不应由各个函数关闭
	// 仅在应用关闭时才应调用
	return nil
}

// ForceClose 强制关闭客户端连接（仅应在应用关闭时调用）
func (c *Client) ForceClose() error {
	clientMux.Lock()
	defer clientMux.Unlock()
	return c.client.Close()
}

// GetAsynqClient 获取原始asynq客户端
func (c *Client) GetAsynqClient() *asynq.Client {
	return c.client
}
