import request from './index'

/**
 * 获取验证码
 * @returns {Promise<{captcha_key: string, image_data: string}>} 返回验证码key和图片base64数据
 */
export function getCaptcha() {
  return request({
    url: '/admin/auth/captcha',
    method: 'get'
  })
}

/**
 * 用户登录
 * @param {Object} data - 登录数据
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @returns {Promise}
 */
export function login(data) {
  return request({
    url: '/admin/auth/login',
    method: 'post',
    data
  })
}

/**
 * 用户注册
 * @param {Object} data - 注册数据
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {string} data.name - 姓名
 * @param {string} data.email - 邮箱
 * @param {string} data.captcha - 验证码
 * @param {string} data.captcha_key - 验证码key
 * @param {string} [data.referee] - 推荐人邀请码
 * @returns {Promise}
 */
export function register(data) {
  return request({
    url: '/admin/auth/signup',
    method: 'post',
    data
  })
}

/**
 * 用户登出
 * @returns {Promise}
 */
export function logout() {
  return request({
    url: '/admin/auth/logout',
    method: 'post'
  })
}

/**
 * 忘记密码
 * @param {Object} data - 找回密码数据
 * @param {string} data.username - 用户名
 * @param {string} data.email - 邮箱
 * @param {string} data.captcha - 验证码
 * @param {string} data.captcha_key - 验证码key
 * @returns {Promise}
 */
export function forgotPassword(data) {
  return request({
    url: '/admin/auth/forgot',
    method: 'post',
    data
  })
}

/**
 * 重置密码
 * @param {Object} data - 重置密码数据
 * @param {string} data.token - 重置令牌
 * @param {string} data.new_password - 新密码
 * @returns {Promise}
 */
export function resetPassword(data) {
  return request({
    url: '/admin/auth/reset',
    method: 'post',
    data
  })
}

/**
 * 发送验证码
 * @param {Object} data - 发送验证码数据
 * @param {string} data.email - 邮箱
 * @param {string} [data.type='register'] - 验证码类型(register:注册, forgot:找回密码)
 * @returns {Promise}
 */
export function sendCaptcha(data) {
  return request({
    url: '/admin/auth/captcha',
    method: 'post',
    data
  })
}

/**
 * 验证Token
 * @returns {Promise}
 */
export function verifyToken() {
  return request({
    url: '/admin/auth/verify',
    method: 'get'
  });
}

/**
 * 获取当前用户信息
 * @returns {Promise}
 */
export function getUserInfo() {
  return request({
    url: '/admin/auth/me',
    method: 'get'
  })
}

/**
 * 更新当前用户信息
 * @param {Object} data - 用户信息
 * @returns {Promise}
 */
export function updateUserInfo(data) {
  return request({
    url: '/admin/auth/me',
    method: 'put',
    data
  })
}

/**
 * 修改密码
 * @param {Object} data - 密码数据
 * @param {string} data.old_password - 旧密码
 * @param {string} data.new_password - 新密码
 * @returns {Promise}
 */
export function changePassword(data) {
  return request({
    url: '/admin/auth/change-password',
    method: 'post',
    data
  })
}

/**
 * 获取用户个人资料
 * @returns {Promise<object>} 返回用户资料信息
 */
export function getProfile() {
  return request({
    url: '/admin/auth/profile',
    method: 'get'
  })
}

/**
 * 更新用户个人资料
 * @param {Object} data - 用户资料
 * @param {string} data.name - 姓名
 * @param {string} data.email - 邮箱
 * @param {string} data.avatar - 头像URL
 * @returns {Promise<object>} 返回更新后的用户资料
 */
export function updateProfile(data) {
  return request({
    url: '/admin/auth/profile',
    method: 'put',
    data
  })
}

/**
 * 上传用户头像
 * @param {File} file - 头像文件
 * @returns {Promise<{url: string}>} - 返回上传成功的头像URL
 */
export function uploadAvatar(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/admin/auth/upload/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取当前用户信息和权限
 * @returns {Promise}
 */
export function getCurrentUser() {
  return request({
    url: '/admin/auth/current-user',
    method: 'get'
  })
} 
