package utils

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
)

// 模板类型常量
const (
	TemplateTypeEmail = "emails"
	TemplateTypePage  = "pages"
)

// GetTemplate 从指定目录加载模板文件
// templateType: 模板类型，如 "emails"
// templateName: 模板名称，如 "password_reset"
func GetTemplate(templateType, templateName string) (string, error) {
	// 构建模板路径
	templatePath := filepath.Join("./assets", templateType, templateName+".html")
	
	// 读取模板文件
	content, err := os.ReadFile(templatePath)
	if err != nil {
		log.Printf("无法读取模板文件 %s: %v", templatePath, err)
		return "", fmt.Errorf("无法读取模板文件: %w", err)
	}
	
	return string(content), nil
}

// GetEmailTemplate 获取邮件模板
func GetEmailTemplate(templateName string) (string, error) {
	return GetTemplate(TemplateTypeEmail, templateName)
}

// GetPageTemplate 获取页面模板
func GetPageTemplate(templateName string) (string, error) {
	return GetTemplate(TemplateTypePage, templateName)
} 