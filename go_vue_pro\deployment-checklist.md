# 生产环境部署清单

## 📋 部署前检查

### 服务器准备
- [ ] 服务器已准备就绪（2GB+ RAM, 2+ CPU核心）
- [ ] 操作系统已更新到最新版本
- [ ] SSH访问已配置
- [ ] 防火墙规则已设置（开放80, 443端口）

### Docker环境
- [ ] Docker Engine 已安装
- [ ] Docker Compose 已安装
- [ ] Docker 服务正在运行
- [ ] 当前用户已添加到docker组

### 项目文件
- [ ] 项目代码已上传到服务器
- [ ] 所有必要的配置文件已准备
- [ ] 数据库初始化文件已准备

## 🔧 配置文件清单

### 必需文件
- [ ] `docker/docker-compose.prod.yml` - 生产环境Docker配置
- [ ] `docker/Dockerfile.server.prod` - 后端生产环境镜像
- [ ] `docker/Dockerfile.nginx.prod` - 前端生产环境镜像
- [ ] `docker/nginx.prod.conf` - Nginx生产环境配置
- [ ] `docker/.env.prod` - 生产环境变量（从.env.prod.example复制）

### 可选配置文件
- [ ] `docker/mysql.conf` - MySQL优化配置
- [ ] `docker/redis.conf` - Redis优化配置
- [ ] `docker/ssl/cert.pem` - SSL证书（如果使用HTTPS）
- [ ] `docker/ssl/key.pem` - SSL私钥（如果使用HTTPS）

## 🔐 安全配置检查

### 密码和密钥
- [ ] 数据库root密码已修改（DB_PASSWORD）
- [ ] JWT密钥已设置为强密码（JWT_SECRET）
- [ ] Redis密码已设置（REDIS_PASSWORD）
- [ ] SMTP密码已配置（如果使用邮件功能）

### 网络安全
- [ ] 防火墙已配置，只开放必要端口
- [ ] SSH密钥认证已启用（推荐）
- [ ] 禁用root用户SSH登录（推荐）

## 🚀 部署步骤

### 1. 环境检查
```bash
./server-check.sh
```
- [ ] 系统资源充足
- [ ] Docker环境正常
- [ ] 网络连接正常
- [ ] 端口未被占用

### 2. 配置环境变量
```bash
cd docker
cp .env.prod.example .env.prod
nano .env.prod
```
- [ ] 数据库配置正确
- [ ] Redis配置正确
- [ ] 域名配置正确
- [ ] 邮箱配置正确（如果需要）

### 3. 执行部署
```bash
./deploy-prod.sh
```
- [ ] 镜像构建成功
- [ ] 容器启动成功
- [ ] 健康检查通过

## ✅ 部署后验证

### 服务状态检查
- [ ] 所有容器都在运行
- [ ] 健康检查全部通过
- [ ] 日志中无严重错误

### 功能测试
- [ ] 前端页面可以正常访问
- [ ] API接口响应正常
- [ ] 数据库连接正常
- [ ] Redis缓存工作正常
- [ ] 项目路由功能正常（/p/{项目ID}/）

### 性能测试
- [ ] 页面加载速度正常
- [ ] API响应时间合理
- [ ] 静态资源缓存生效

## 🌐 域名和SSL配置

### 域名配置
- [ ] DNS记录已指向服务器IP
- [ ] 域名解析生效
- [ ] 环境变量中域名配置正确

### SSL证书（如果使用HTTPS）
- [ ] SSL证书已获取
- [ ] 证书文件已放置在正确位置
- [ ] Nginx HTTPS配置已启用
- [ ] HTTP自动重定向到HTTPS

## 📊 监控和维护

### 日志配置
- [ ] 日志轮转已配置
- [ ] 日志级别设置合理
- [ ] 错误日志监控已设置

### 备份策略
- [ ] 数据库备份脚本已配置
- [ ] 定期备份计划已设置
- [ ] 备份恢复流程已测试

### 更新策略
- [ ] 应用更新流程已确定
- [ ] 回滚策略已准备
- [ ] 维护窗口已规划

## 🔧 常用命令

### 服务管理
```bash
cd docker

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs

# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 停止服务
docker-compose -f docker-compose.prod.yml down
```

### 数据库管理
```bash
# 连接数据库
docker-compose -f docker-compose.prod.yml exec mysql mysql -u root -p

# 备份数据库
docker-compose -f docker-compose.prod.yml exec mysql mysqldump -u root -p go_fiber > backup.sql
```

### 系统监控
```bash
# 查看系统资源
htop
df -h
free -h

# 查看Docker资源使用
docker stats
```

## 🆘 故障排除

### 常见问题检查
- [ ] 端口冲突检查
- [ ] 内存不足检查
- [ ] 磁盘空间检查
- [ ] 网络连接检查
- [ ] 权限问题检查

### 紧急联系信息
- 服务器提供商支持：_______________
- 域名注册商支持：_______________
- SSL证书提供商：_______________
- 项目技术支持：_______________

---

## 📝 部署记录

**部署日期**：_______________
**部署人员**：_______________
**服务器信息**：_______________
**域名**：_______________
**备注**：_______________

---

**✅ 所有检查项完成后，您的Go Vue Pro项目就可以在生产环境中稳定运行了！**
