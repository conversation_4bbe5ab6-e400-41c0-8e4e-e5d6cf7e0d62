package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupProjectsContentRoutes 设置项目内容相关路由
func SetupProjectsContentRoutes(router fiber.Router) {
	projectsContent := router.Group("/projectscontent")
	
	// 应用认证中间件
	projectsContent.Use(utils.RequireAuthentication)
	
	// 项目内容管理路由
	projectsContent.Get("/index", middleware.RequirePermission("project.content.view"), handlers.GetProjectContents)
	projectsContent.Post("/index", middleware.RequirePermission("project.content.create"), handlers.CreateProjectContent)
	projectsContent.Get("/index/:id", middleware.RequirePermission("project.content.view"), handlers.GetProjectContent)
	projectsContent.Put("/index/:id", middleware.RequirePermission("project.content.edit"), handlers.UpdateProjectContent)
	projectsContent.Delete("/index/:id", middleware.RequirePermission("project.content.delete"), handlers.DeleteProjectContent)
	// 添加恢复项目内容路由
	projectsContent.Put("/restore/:id", middleware.RequirePermission("project.content.edit"), handlers.RestoreProjectContent)
	
	// 特殊路由 - 这些路由已在项目路由中定义
	router.Get("/project/my/:userid/:projectid", middleware.RequirePermission("project.content.view"), handlers.GetProjectContentDetail)
	router.Delete("/project/my/:userid/:projectid", middleware.RequirePermission("project.content.delete"), handlers.RemoveProjectContent)
} 