import request from './index'

/**
 * 获取管理员用户列表
 * @param {Object} params - 查询参数
 * @param {string} [params.username] - 用户名(模糊查询)
 * @param {string} [params.name] - 姓名(模糊查询)
 * @param {string} [params.email] - 邮箱(模糊查询)
 * @param {number} [params.state] - 状态(1:启用, 其他或不传:所有)
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getAdminUserList(params) {
  return request({
    url: '/admin/users',
    method: 'get',
    params
  })
}

/**
 * 创建管理员用户
 * @param {Object} data - 用户数据
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {string} data.name - 姓名
 * @param {string} [data.email] - 邮箱
 * @param {string} [data.avatar] - 头像URL
 * @param {number} [data.state] - 状态(1:启用, 0:禁用)
 * @param {string} [data.referee] - 推荐人
 * @param {number} [data.notify] - 通知设置
 * @param {Array} [data.role_ids] - 角色ID列表
 * @param {Array} [data.permission_ids] - 权限ID列表
 * @returns {Promise}
 */
export function createAdminUser(data) {
  return request({
    url: '/admin/users',
    method: 'post',
    data
  })
}

/**
 * 获取单个管理员用户
 * @param {number} id - 用户ID
 * @returns {Promise}
 */
export function getAdminUser(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'get'
  })
}

/**
 * 更新管理员用户
 * @param {number} id - 用户ID
 * @param {Object} data - 需要更新的用户数据
 * @param {string} [data.username] - 用户名
 * @param {string} [data.password] - 密码
 * @param {string} [data.name] - 姓名
 * @param {string} [data.email] - 邮箱
 * @param {string} [data.avatar] - 头像URL
 * @param {number} [data.state] - 状态(1:启用, 0:禁用)
 * @param {string} [data.referee] - 推荐人
 * @param {number} [data.notify] - 通知设置
 * @param {Array} [data.role_ids] - 角色ID列表
 * @param {Array} [data.permission_ids] - 权限ID列表
 * @returns {Promise}
 */
export function updateAdminUser(id, data) {
  return request({
    url: `/admin/users/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除管理员用户
 * @param {number} id - 用户ID
 * @returns {Promise}
 */
export function deleteAdminUser(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'delete'
  })
}

/**
 * 上传用户头像
 * @param {FormData} formData - 包含头像文件的表单数据
 * @returns {Promise<{url: string}>} - 返回上传成功的头像URL
 */
export function uploadAvatar(formData) {
  return request({
    url: '/admin/auth/upload/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
} 