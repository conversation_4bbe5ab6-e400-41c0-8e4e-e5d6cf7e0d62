import request from './index'

/**
 * 获取友情链接列表
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getFriendlyList(params) {
  return request({
    url: '/admin/friendly/index',
    method: 'get',
    params
  })
}

/**
 * 获取公开友情链接列表，无需认证
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=20] - 每页数量
 * @returns {Promise}
 */
export function getPublicFriendlyList(params) {
  return request({
    url: '/admin/friendly-list',
    method: 'get',
    params
  })
}

/**
 * 创建友情链接
 * @param {Object} data - 友情链接数据
 * @param {string} data.name - 友情链接名称
 * @param {string} data.href - 友情链接URL
 * @param {string} data.type - 友情链接类型
 * @param {string} [data.thumb] - 友情链接缩略图
 * @param {number} [data.sort=0] - 排序
 * @param {number} [data.state=1] - 状态(1:启用, 0:禁用)
 * @returns {Promise}
 */
export function createFriendly(data) {
  return request({
    url: '/admin/friendly/index',
    method: 'post',
    data
  })
}

/**
 * 获取友情链接详情
 * @param {number} id - 友情链接ID
 * @returns {Promise}
 */
export function getFriendlyDetail(id) {
  return request({
    url: `/admin/friendly/index/${id}`,
    method: 'get'
  })
}

/**
 * 更新友情链接
 * @param {number} id - 友情链接ID
 * @param {Object} data - 更新数据
 * @param {string} [data.name] - 友情链接名称
 * @param {string} [data.href] - 友情链接URL
 * @param {string} [data.type] - 友情链接类型
 * @param {string} [data.thumb] - 友情链接缩略图
 * @param {number} [data.sort] - 排序
 * @param {number} [data.state] - 状态(1:启用, 0:禁用)
 * @returns {Promise}
 */
export function updateFriendly(id, data) {
  return request({
    url: `/admin/friendly/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除友情链接
 * @param {number} id - 友情链接ID
 * @returns {Promise}
 */
export function deleteFriendly(id) {
  return request({
    url: `/admin/friendly/index/${id}`,
    method: 'delete'
  })
} 