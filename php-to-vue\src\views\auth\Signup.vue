<template>
  <div class="signup-container">
    <!-- 背景矩阵雨特效 -->
    <canvas id="matrix-canvas" ref="matrixCanvas"></canvas>

    <div class="signup-box">
      <h1 class="signup-title">XSS</h1>
      <p class="signup-subtitle">创建您的账号，开始体验</p>

      <el-card class="signup-card">
        <el-form
          :model="signupForm"
          :rules="rules"
          ref="signupForm"
          label-width="0"
          @submit.native.prevent
        >
          <!-- 用户名 -->
          <el-form-item prop="username">
            <el-input
              v-model="signupForm.username"
              placeholder="用户名"
              prefix-icon="el-icon-user"
            >
            </el-input>
          </el-form-item>

          <!-- 姓名 -->
          <el-form-item prop="name">
            <el-input
              v-model="signupForm.name"
              placeholder="姓名"
              prefix-icon="el-icon-user-solid"
            >
            </el-input>
          </el-form-item>

          <!-- 邮箱 -->
          <el-form-item prop="email">
            <el-input
              v-model="signupForm.email"
              placeholder="邮箱"
              prefix-icon="el-icon-message"
            >
            </el-input>
          </el-form-item>

          <!-- 密码 -->
          <el-form-item prop="password">
            <el-input
              v-model="signupForm.password"
              type="password"
              placeholder="密码"
              show-password
              prefix-icon="el-icon-lock"
            >
            </el-input>
          </el-form-item>

          <!-- 确认密码 -->
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="signupForm.confirmPassword"
              type="password"
              placeholder="确认密码"
              show-password
              prefix-icon="el-icon-lock"
            >
            </el-input>
          </el-form-item>

          <!-- 推荐人/邀请码 -->
          <el-form-item
            v-if="invitationRequired"
            prop="invitation_code"
            :rules="
              invitationRequired
                ? [{ required: true, message: '请输入邀请码', trigger: 'blur' }]
                : []
            "
          >
            <el-input
              v-model="signupForm.invitation_code"
              placeholder="邀请码 (必填)"
              prefix-icon="el-icon-key"
            >
            </el-input>
          </el-form-item>

          <!-- 推荐人 -->
          <el-form-item prop="referee">
            <el-input
              v-model="signupForm.referee"
              placeholder="推荐人 (可选)"
              prefix-icon="el-icon-user"
            >
            </el-input>
          </el-form-item>

          <!-- 验证码 -->
          <el-form-item prop="captcha">
            <div class="captcha-container">
              <el-input
                v-model="signupForm.captcha"
                placeholder="验证码"
                prefix-icon="el-icon-key"
              ></el-input>
              <div class="captcha-image" @click="refreshCaptcha">
                <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                <div v-else class="captcha-loading">
                  <i class="el-icon-loading"></i>
                </div>
              </div>
            </div>
          </el-form-item>

          <!-- 注册按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              :loading="loading"
              class="signup-button"
              @click="handleSignup"
            >
              注册 <i class="el-icon-right"></i>
            </el-button>
          </el-form-item>

          <!-- 额外链接 -->
          <div class="signup-links">
            <span>已有账号?</span>
            <router-link to="/login" class="link">前往登录</router-link>
          </div>
        </el-form>
      </el-card>

      <!-- 版权信息 -->
      <div class="copyright">
        © 2020 - {{ new Date().getFullYear() }} XSS测试平台
      </div>
    </div>
  </div>
</template>

<script>
import { getCaptcha, register } from "@/api/auth";
import { getPublicSettings } from "@/api/settings";

export default {
  name: "Signup",
  data() {
    // 自定义校验器：确认密码
    const validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.signupForm.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      captchaImage: "",
      captchaKey: "",
      invitationRequired: false, // 是否需要邀请码
      registerClosed: false, // 是否关闭注册
      signupForm: {
        username: "",
        name: "",
        email: "",
        password: "",
        confirmPassword: "",
        referee: "",
        invitation_code: "",
        captcha: "",
        captcha_key: "",
      },
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            min: 3,
            max: 20,
            message: "长度在 3 到 20 个字符",
            trigger: "blur",
          },
        ],
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        email: [
          { required: true, message: "请输入邮箱地址", trigger: "blur" },
          { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
        confirmPassword: [
          { required: true, message: "请再次输入密码", trigger: "blur" },
          { validator: validatePass2, trigger: "blur" },
        ],
        captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
    };
  },
  created() {
    // 获取验证码
    this.refreshCaptcha();

    // 获取公开设置
    this.getPublicSettings();

    // 如果注册已关闭，重定向到登录页
    if (this.registerClosed) {
      this.$router.push("/login");
      this.$message.warning("注册功能已关闭");
    }
  },
  mounted() {
    // 初始化矩阵雨效果
    this.initMatrixRain();
    // 窗口大小改变时重新调整画布大小
    window.addEventListener("resize", this.resizeCanvas);
  },
  beforeDestroy() {
    // 清除事件监听
    window.removeEventListener("resize", this.resizeCanvas);
    // 停止动画
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  },
  methods: {
    // 获取公开设置
    getPublicSettings() {
      getPublicSettings()
        .then((response) => {
          // 根据返回的设置更新状态
          this.invitationRequired = response.invitation_required === "true";
          this.registerClosed = response.register_closed === "true";

          // 如果注册已关闭，重定向到登录页
          if (this.registerClosed) {
            this.$router.push("/login");
            this.$message.warning("注册功能已关闭");
          }
        })
        .catch((error) => {
          console.error("获取公开设置失败:", error);
          // 默认设置为需要邀请码
          this.invitationRequired = true;
        });
    },

    // 刷新验证码
    refreshCaptcha() {
      getCaptcha()
        .then((res) => {
          this.captchaImage = res.image_data;
          this.captchaKey = res.captcha_key;
          this.signupForm.captcha_key = res.captcha_key;
        })
        .catch(() => {
          this.$message.error("获取验证码失败，请重试");
        });
    },

    // 处理注册逻辑
    handleSignup() {
      this.$refs.signupForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 准备提交的数据（删除确认密码字段，后端不需要）
          const submitData = { ...this.signupForm };
          delete submitData.confirmPassword;

          register(submitData)
            .then(() => {
              this.$message.success("注册成功，请登录");
              this.$router.push("/login");
            })
            .catch((error) => {
              // 提取更详细的错误信息
              let errorMsg = "注册失败，请重试";
              if (error.response && error.response.data) {
                errorMsg = error.response.data.message || errorMsg;
              } else if (error.message) {
                errorMsg = error.message;
              }
              this.$message.error(errorMsg);

              // 刷新验证码
              this.refreshCaptcha();
              this.signupForm.captcha = "";
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    // 初始化矩阵雨效果
    initMatrixRain() {
      const canvas = this.$refs.matrixCanvas;
      const ctx = canvas.getContext("2d");

      // 设置画布大小
      this.resizeCanvas();

      // 字符集
      const chars =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz$+-*/=%\"'#&_(),.;:?!\\|{}<>[]^~";

      // 每列的当前位置
      const drops = [];

      // 初始化 drops
      for (let i = 0; i < canvas.width / 20; i++) {
        drops[i] = 1;
      }

      // 绘制矩阵雨
      const draw = () => {
        // 半透明黑色背景，形成拖尾效果
        ctx.fillStyle = "rgba(0, 0, 0, 0.05)";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 设置字体
        ctx.font = "15px monospace";

        // 循环绘制每一列
        for (let i = 0; i < drops.length; i++) {
          // 随机选择一个字符
          const text = chars[Math.floor(Math.random() * chars.length)];

          // 随机字符颜色
          const green = Math.floor(Math.random() * 156 + 100); // 生成较亮的绿色
          ctx.fillStyle = `rgba(0, ${green}, 0, 0.8)`;

          // 绘制字符
          ctx.fillText(text, i * 20, drops[i] * 20);

          // 更新位置
          if (drops[i] * 20 > canvas.height && Math.random() > 0.975) {
            drops[i] = 0;
          }

          drops[i]++;
        }

        // 动画循环
        this.animationId = requestAnimationFrame(draw);
      };

      // 开始动画
      draw();
    },

    // 调整画布大小
    resizeCanvas() {
      const canvas = this.$refs.matrixCanvas;
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    },
  },
};
</script>

<style scoped>
.signup-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #000;
  color: #fff;
}

#matrix-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.signup-box {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 500px;
  width: 90%;
}

.signup-title {
  font-size: 3rem;
  font-weight: 300;
  margin-bottom: 0.5rem;
  color: #fff;
}

.signup-subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
}

.signup-card {
  background-color: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(50, 205, 50, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 0 10px rgba(50, 205, 50, 0.2);
}

.signup-card /deep/ .el-card__body {
  padding: 20px 30px;
}

.signup-card /deep/ .el-input__inner {
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(50, 205, 50, 0.5);
  color: #fff;
}

.signup-card /deep/ .el-input__icon {
  color: rgba(50, 205, 50, 0.8);
}

.signup-card /deep/ .el-form-item__label {
  color: #fff;
}

.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-image {
  margin-left: 10px;
  width: 120px;
  height: 40px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(50, 205, 50, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.captcha-image img {
  max-width: 100%;
  max-height: 100%;
}

.captcha-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: rgba(50, 205, 50, 0.8);
}

.signup-button {
  width: 100%;
  background-color: rgba(50, 205, 50, 0.7);
  border-color: rgba(50, 205, 50, 0.9);
  transition: all 0.3s;
}

.signup-button:hover {
  background-color: rgba(50, 205, 50, 0.9);
  box-shadow: 0 0 10px rgba(50, 205, 50, 0.7);
}

.signup-links {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  color: rgba(255, 255, 255, 0.7);
}

.link {
  color: rgba(50, 205, 50, 0.9);
  text-decoration: none;
  transition: all 0.3s;
}

.link:hover {
  color: #fff;
  text-shadow: 0 0 5px rgba(50, 205, 50, 0.9);
}

.copyright {
  margin-top: 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}
</style>
