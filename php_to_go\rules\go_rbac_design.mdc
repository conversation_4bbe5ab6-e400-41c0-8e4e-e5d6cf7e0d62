---
description: 权限，权限设计
globs: 
alwaysApply: false
---
# Go Fiber RBAC 权限系统设计

## 一、系统概述

RBAC (Role-Based Access Control) 是基于角色的访问控制系统，通过角色分配权限，再将角色授予用户，实现权限管理。本项目采用的RBAC模型包含以下核心组件：

- **用户 (Users)**: 系统使用者
- **角色 (Roles)**: 权限的集合
- **权限 (Permissions)**: 执行特定操作的能力
- **菜单 (Menus)**: 系统功能界面的入口

## 二、数据模型

### 2.1 核心表结构

| 表名 | 说明 | 主要字段 |
| ---- | ---- | -------- |
| admin_users | 用户表 | id, username, password, name, email, avatar |
| admin_roles | 角色表 | id, name, slug, remark |
| admin_permissions | 权限表 | id, name, slug, http_method, http_path, parent_id |
| admin_menu | 菜单表 | id, parent_id, order, title, icon, uri |

### 2.2 关联表结构

| 表名 | 说明 | 关联关系 |
| ---- | ---- | -------- |
| admin_role_users | 角色-用户关联 | role_id, user_id |
| admin_role_permissions | 角色-权限关联 | role_id, permission_id |
| admin_role_menu | 角色-菜单关联 | role_id, menu_id |
| admin_permission_menu | 权限-菜单关联 | permission_id, menu_id |

## 三、权限验证流程

1. **用户认证**：用户登录后，系统生成JWT token，包含用户ID和角色信息
2. **权限验证**：
   - 通过中间件验证用户请求路径是否有权限访问
   - 验证用户角色是否拥有请求操作的权限
3. **菜单构建**：根据用户角色动态构建前端菜单

### 3.1 权限检查中间件流程

```go
// 权限检查流程伪代码
func CheckPermission(c *fiber.Ctx) error {
    // 1. 获取当前用户ID和角色
    userID := getUserFromContext(c)
    roles := getUserRoles(userID)
    
    // 2. 获取当前请求路径和方法
    path := c.Path()
    method := c.Method()
    
    // 3. 检查是否是管理员角色(直接放行)
    if isAdmin(roles) {
        return c.Next()
    }
    
    // 4. 检查角色是否有权限访问此路径
    if hasPermission(roles, path, method) {
        return c.Next()
    }
    
    // 5. 无权限，返回403错误
    return c.Status(403).JSON(fiber.Map{
        "success": false,
        "message": "权限不足",
    })
}
```

## 四、实现指南

### 4.1 用户权限管理

1. **管理员判断**
```go
func IsAdmin(c *fiber.Ctx) bool {
    // 获取用户ID
    userID := GetUserIDFromContext(c)
    
    // 获取用户角色
    var user models.AdminUser
    if err := database.DB.Preload("Roles").First(&user, userID).Error; err != nil {
        return false
    }
    
    // 检查是否有管理员角色
    for _, role := range user.Roles {
        if role.Name == "Administrator" || role.Slug == "administrator" {
            return true
        }
    }
    
    return false
}
```

2. **权限检查**
```go
func HasPermission(userID uint64, path string, method string) bool {
    var permissions []models.Permission
    
    // 获取用户所有角色的权限
    database.DB.Raw(`
        SELECT p.* FROM admin_permissions p
        JOIN admin_role_permissions rp ON p.id = rp.permission_id
        JOIN admin_role_users ru ON rp.role_id = ru.role_id
        WHERE ru.user_id = ? AND p.http_method LIKE ?
    `, userID, "%"+method+"%").Scan(&permissions)
    
    // 检查是否有匹配的权限
    for _, perm := range permissions {
        if matchPath(path, perm.HttpPath) {
            return true
        }
    }
    
    return false
}
```

### 4.2 菜单权限管理

构建用户有权限访问的菜单树：

```go
func GetUserMenuTree(userID uint64) []dto.MenuResponse {
    // 获取用户角色
    var roleIDs []uint64
    
    database.DB.Table("admin_role_users").
        Where("user_id = ?", userID).
        Pluck("role_id", &roleIDs)
    
    // 如果是管理员，获取所有菜单
    if isAdmin(roleIDs) {
        var allMenus []models.Menu
        database.DB.Order("`order` ASC").Find(&allMenus)
        return BuildMenuTree(allMenus, 0)
    }
    
    // 获取角色对应的菜单
    var menuIDs []uint64
    if len(roleIDs) > 0 {
        database.DB.Table("admin_role_menu").
            Where("role_id IN ?", roleIDs).
            Pluck("menu_id", &menuIDs)
    }
    
    // 加载有权限的菜单
    var userMenus []models.Menu
    database.DB.Where("id IN ?", menuIDs).Order("`order` ASC").Find(&userMenus)
    
    return BuildMenuTree(userMenus, 0)
}
```

## 五、前端权限控制

1. **按钮级权限控制**

```javascript
// 检查当前用户是否有特定权限
function hasPermission(permission) {
  const userPermissions = store.state.user.permissions;
  return userPermissions.includes(permission);
}

// 使用示例
<el-button v-if="hasPermission('user.create')">添加用户</el-button>
```

2. **路由权限控制**

```javascript
// 路由守卫
router.beforeEach((to, from, next) => {
  if (to.meta.permissions) {
    if (hasPermissions(to.meta.permissions)) {
      next();
    } else {
      next('/403');
    }
  } else {
    next();
  }
});
```

## 六、最佳实践

1. **权限粒度控制**
   - 使用HTTP方法细分权限(GET-查看, POST-添加, PUT-修改, DELETE-删除)
   - 使用父子结构组织权限，便于权限继承

2. **角色设计**
   - 设计明确的角色体系，避免角色过多导致管理复杂
   - 为经常需要一起分配的权限创建角色模板

3. **性能优化**
   - 用户权限缓存，避免频繁数据库查询
   - 使用JWT中包含基本权限信息，减少数据库查询

4. **安全建议**
   - 定期审查权限分配，遵循最小权限原则
   - 关键操作添加二次验证
   - 记录权限变更日志，方便审计 