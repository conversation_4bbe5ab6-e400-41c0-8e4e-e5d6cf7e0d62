package queue

import (
	"context"
	"go-fiber-api/config"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils/queue/processors"
	"go-fiber-api/utils/queue/tasks"
	"log"
	"time"

	"github.com/hibiken/asynq"
)

type MyLogger struct {
	*log.Logger
}

func (l *MyLogger) Debug(args ...interface{}) {
	l.Println(append([]interface{}{"[DEBUG]"}, args...)...)
}
func (l *MyLogger) Info(args ...interface{}) { l.Println(append([]interface{}{"[INFO]"}, args...)...) }
func (l *MyLogger) Warn(args ...interface{}) { l.Println(append([]interface{}{"[WARN]"}, args...)...) }
func (l *MyLogger) Error(args ...interface{}) {
	l.Println(append([]interface{}{"[ERROR]"}, args...)...)
}

// NewServer 创建新的队列服务器
func NewServer(concurrency int) *asynq.Server {
	cfg := config.LoadConfig().RedisConfig

	if concurrency <= 0 {
		concurrency = 10 // 默认并发数
	}

	return asynq.NewServer(
		asynq.RedisClientOpt{
			Addr:     cfg.Addr,
			Password: cfg.Password,
			DB:       cfg.DB,
		},
		asynq.Config{
			Concurrency: concurrency,
			Queues: map[string]int{
				"critical": 10, // 高优先级队列
				"default":  5,  // 默认队列
				"low":      1,  // 低优先级队列
			},
			Logger: &MyLogger{log.New(log.Writer(), "asynq: ", log.LstdFlags)},
			ErrorHandler: asynq.ErrorHandlerFunc(func(ctx context.Context, task *asynq.Task, err error) {
				record := models.FailedJob{
					Connection: "redis",
					Queue:      task.Type(),
					Payload:    string(task.Payload()),
					Exception:  err.Error(),
					FailedAt:   time.Now(),
				}
				if dbErr := database.DB.Create(&record).Error; dbErr != nil {
					log.Printf("写入失败任务到数据库失败: %v", dbErr)
				}
			}),
		},
	)
}

// RegisterHandlers 注册任务处理器
func RegisterHandlers(mux *asynq.ServeMux) {
	// 注册邮件任务处理器
	mux.HandleFunc(tasks.TypeEmailSend, processors.EmailProcessor)

	// 注册通知任务处理器
	mux.HandleFunc(tasks.TypeNotificationSend, processors.NotificationProcessor)

	// 注册文件处理任务处理器
	mux.HandleFunc(tasks.TypeFileProcess, processors.FileProcessor)

	// 注册数据导出任务处理器
	mux.HandleFunc(tasks.TypeDataExport, processors.DataExportProcessor)

	// 注册公告发布任务处理器
	mux.HandleFunc(tasks.TypeArticlePublish, processors.ArticlePublishProcessor)

	log.Println("Asynq任务处理器注册成功")
}
