# 多阶段构建 - 前端构建阶段
FROM node:18.18.0-alpine AS frontend-builder

# 安装git
RUN apk add --no-cache git

# 设置工作目录
WORKDIR /app

# 配置npm镜像源
RUN npm config set registry https://registry.npmmirror.com

# 克隆前端项目代码（使用私人令牌）
RUN git clone https://density11:<EMAIL>/liguangsheng1/php-to-vue.git . || (echo "Failed to clone repository" && exit 1)

# 安装依赖
RUN npm install || (echo "Failed to install dependencies" && exit 1)

# 创建生产环境变量
RUN echo "VUE_APP_BASE_API=/api" > .env.production && \
    echo "VUE_APP_WS_API=ws://api/ws" >> .env.production && \
    echo "NODE_ENV=production" >> .env.production

# 构建前端项目
RUN npm run build || (echo "Failed to build project" && exit 1)

# Nginx生产环境
FROM nginx:alpine

# 配置Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装必要工具
RUN apk add --no-cache curl

# 删除默认配置
RUN rm /etc/nginx/conf.d/default.conf

# 创建简化的nginx配置
RUN printf 'server {\n\
    listen 80;\n\
    server_name _;\n\
    \n\
    gzip on;\n\
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;\n\
    \n\
    client_max_body_size 50M;\n\
    \n\
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {\n\
        root /usr/share/nginx/html;\n\
        expires 1y;\n\
        add_header Cache-Control "public, immutable";\n\
        try_files $uri =404;\n\
    }\n\
    \n\
    location /api/ {\n\
        proxy_pass http://api:3000/api/;\n\
        proxy_http_version 1.1;\n\
        proxy_set_header Upgrade $http_upgrade;\n\
        proxy_set_header Connection '"'"'upgrade'"'"';\n\
        proxy_set_header Host $host;\n\
        proxy_set_header X-Real-IP $remote_addr;\n\
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n\
        proxy_set_header X-Forwarded-Proto $scheme;\n\
        proxy_cache_bypass $http_upgrade;\n\
    }\n\
    \n\
    location /ws/ {\n\
        proxy_pass http://api:3000/ws/;\n\
        proxy_http_version 1.1;\n\
        proxy_set_header Upgrade $http_upgrade;\n\
        proxy_set_header Connection "upgrade";\n\
        proxy_set_header Host $host;\n\
        proxy_set_header X-Real-IP $remote_addr;\n\
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n\
        proxy_cache_bypass $http_upgrade;\n\
    }\n\
    \n\
    location /swagger/ {\n\
        proxy_pass http://api:3000/swagger/;\n\
        proxy_http_version 1.1;\n\
        proxy_set_header Host $host;\n\
        proxy_set_header X-Real-IP $remote_addr;\n\
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n\
        proxy_set_header X-Forwarded-Proto $scheme;\n\
    }\n\
    \n\
    location /health {\n\
        proxy_pass http://api:3000/health;\n\
        access_log off;\n\
    }\n\
    \n\
    location ~ ^/p/(.+)/?$ {\n\
        proxy_pass http://api:3000/$1/;\n\
        proxy_http_version 1.1;\n\
        proxy_set_header Upgrade $http_upgrade;\n\
        proxy_set_header Connection '"'"'upgrade'"'"';\n\
        proxy_set_header Host $host;\n\
        proxy_set_header X-Real-IP $remote_addr;\n\
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n\
        proxy_set_header X-Forwarded-Proto $scheme;\n\
        proxy_cache_bypass $http_upgrade;\n\
    }\n\
    \n\
    location / {\n\
        root /usr/share/nginx/html;\n\
        index index.html;\n\
        try_files $uri $uri/ /index.html;\n\
    }\n\
}\n' > /etc/nginx/conf.d/default.conf

# 从前端构建阶段复制构建文件
COPY --from=frontend-builder /app/dist /usr/share/nginx/html

# 创建日志目录
RUN mkdir -p /var/log/nginx

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html
RUN chown -R nginx:nginx /var/log/nginx

# 创建SSL证书目录
RUN mkdir -p /etc/nginx/ssl

# 暴露端口
EXPOSE 80 443

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
