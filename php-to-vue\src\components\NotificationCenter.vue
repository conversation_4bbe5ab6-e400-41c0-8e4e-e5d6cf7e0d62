<template>
  <div class="notification-center">
    <el-popover
      placement="bottom-end"
      width="350"
      trigger="click"
      v-model="visible"
    >
      <div class="notification-header">
        <span class="title">系统通知</span>
        <div class="actions">
          <el-button type="text" size="mini" @click="markAllAsRead"
            >全部已读</el-button
          >
          <el-button type="text" size="mini" @click="clearNotifications"
            >清除全部</el-button
          >
        </div>
      </div>
      <el-divider></el-divider>
      <div class="notification-list" v-if="notifications.length > 0">
        <div
          v-for="item in notifications"
          :key="item.id"
          class="notification-item"
          :class="{ 'is-read': item.read }"
          @click="viewNotification(item)"
        >
          <div class="notification-title">
            <span>{{ item.title }}</span>
            <el-tag size="mini" type="info" v-if="!item.read">新</el-tag>
          </div>
          <div class="notification-content">{{ item.content }}</div>
          <div class="notification-meta">
            <span>{{ item.author }}</span>
            <span>{{ item.time }}</span>
          </div>
        </div>
      </div>
      <div class="empty-notifications" v-else>
        <i class="el-icon-bell"></i>
        <p>暂无通知</p>
      </div>
      <el-badge
        :value="unreadCount"
        :hidden="unreadCount === 0"
        slot="reference"
      >
        <el-button
          type="text"
          icon="el-icon-bell"
          class="notification-button"
        ></el-button>
      </el-badge>

      <!-- 调试信息 -->
      <div v-if="debugMode && notifications.length === 0" class="debug-info">
        <el-alert
          title="无通知数据"
          type="info"
          description="当前没有任何通知数据，可能是WebSocket连接未建立或未收到任何通知。"
          show-icon
          :closable="false"
        >
        </el-alert>
      </div>
    </el-popover>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "NotificationCenter",
  data() {
    return {
      visible: false,
      debugMode: false, // 添加调试模式
    };
  },
  computed: {
    ...mapGetters({
      notifications: "getNotifications",
      unreadCount: "getUnreadCount",
    }),
  },
  created() {
    // 监听自定义事件：收到新通知
    this.$root.$on("new-notification", (notification) => {
      console.log("NotificationCenter收到新通知:", notification);
      this.showToast("收到新通知: " + notification.title);
    });

    // 调试信息
    if (this.debugMode) {
      console.log("NotificationCenter初始化完成");
      // 每隔10秒检查一次通知状态
      this.debugInterval = setInterval(() => {
        console.log(
          "当前通知数量:",
          this.notifications.length,
          "未读数量:",
          this.unreadCount
        );
      }, 10000);
    }
  },
  beforeDestroy() {
    // 移除事件监听
    this.$root.$off("new-notification");
    // 清除调试定时器
    if (this.debugInterval) {
      clearInterval(this.debugInterval);
    }
  },
  methods: {
    clearNotifications() {
      this.$store.dispatch("clearNotifications");
    },
    markAllAsRead() {
      this.$store.dispatch("markAllAsRead");
    },
    viewNotification(notification) {
      // 标记为已读
      this.$store.dispatch("markAsRead", notification.id);

      // 根据通知类型导航到相应页面
      if (notification.type === "article") {
        // 导航到公告详情页
        try {
          this.$router.push({
            name: "ArticleDetail",
            params: { id: notification.id },
          });
          console.log("正在导航到公告详情页:", notification.id);
        } catch (error) {
          console.error("导航错误:", error);
          // 尝试使用路径导航
          this.$router.push(`/article/detail/${notification.id}`);
        }
      }

      // 关闭弹出框
      this.visible = false;
    },

    // 显示提示消息
    showToast(message) {
      this.$message({
        message: message,
        type: "success",
        duration: 3000,
      });
    },
  },
};
</script>

<style scoped>
.notification-center {
  display: inline-block;
}

.notification-button {
  font-size: 20px;
  padding: 0;
  margin: 0 10px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.notification-header .title {
  font-weight: bold;
  font-size: 16px;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.notification-item:hover {
  background-color: #f8f8f8;
}

.notification-item.is-read {
  opacity: 0.7;
}

.notification-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  font-weight: bold;
}

.notification-content {
  margin-bottom: 5px;
  color: #606266;
  word-break: break-all;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  color: #909399;
  font-size: 12px;
}

.empty-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  color: #909399;
}

.empty-notifications i {
  font-size: 30px;
  margin-bottom: 10px;
}

/* 调试信息样式 */
.debug-info {
  position: absolute;
  top: 50px;
  right: 0;
  width: 300px;
  z-index: 1000;
}

.debug-buttons {
  margin-top: 10px;
  text-align: center;
}
</style>
