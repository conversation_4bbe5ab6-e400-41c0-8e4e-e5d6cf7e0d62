<template>
  <div class="about">
    <h1 class="page-title">关于我们</h1>
    <div class="content">
      <p>欢迎访问我们的平台！这是一个集成多功能的综合管理系统，为用户提供便捷高效的项目管理、内容发布以及系统维护服务。</p>
      <p>我们致力于提供最佳的用户体验和安全可靠的服务，帮助您实现工作流程的优化和管理效率的提升。</p>
    </div>

    <el-divider content-position="center">友情链接</el-divider>
    <FriendlyLinks :limit="10" />
  </div>
</template>

<script>
import FriendlyLinks from '@/components/FriendlyLinks.vue'

export default {
  name: 'About',
  components: {
    FriendlyLinks
  }
}
</script>

<style scoped>
.about {
  max-width: 800px;
  margin: 0 auto;
}
.content {
  text-align: center;
  margin-bottom: 30px;
}
.page-title {
  text-align: center;
  margin-bottom: 20px;
  color: #409EFF;
}
</style> 