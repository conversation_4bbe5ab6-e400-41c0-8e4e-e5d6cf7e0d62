# 多阶段构建 - 生产环境优化版本
FROM golang:1.24-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置Go模块代理和环境变量
ENV GOPROXY=https://goproxy.cn,direct
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

# 配置Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装构建依赖
RUN apk add --no-cache git ca-certificates tzdata

# 先复制.env.prod文件到临时位置
COPY .env.prod /tmp/.env.prod

# 克隆后端项目代码（使用私人令牌）
RUN git clone https://density11:<EMAIL>/density11/php_to_go.git .

# 复制.env文件到工作目录
RUN cp /tmp/.env.prod .env

# 验证.env文件是否存在
RUN ls -la .env && echo "=== .env文件内容 ===" && cat .env

# 下载依赖
RUN go mod download

# 构建API服务器
RUN go build -ldflags="-w -s" -o server ./cmd/server/main.go

# 构建Worker服务
RUN go build -ldflags="-w -s" -o worker ./cmd/worker/main.go

# 生产环境运行阶段
FROM alpine:latest

# 配置Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装运行时依赖
RUN apk add --no-cache ca-certificates tzdata wget curl

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/server /app/server
COPY --from=builder /app/worker /app/worker
COPY --from=builder /app/assets /app/assets

# 复制配置文件
COPY --from=builder /app/config /app/config

# 设置文件权限
RUN chown -R appuser:appgroup /app
RUN chmod +x /app/server /app/worker

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 3000 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/health || exit 1

# 默认启动API服务器
CMD ["/app/server"]
