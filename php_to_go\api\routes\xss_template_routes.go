package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupXssTemplateRoutes 设置XSS模板路由
func SetupXssTemplateRoutes(router fiber.Router) {
	// XSS模板管理路由组
	xssTemplates := router.Group("/xss-templates")

	// 添加认证中间件
	xssTemplates.Use(utils.RequireAuthentication)

	// XSS模板CRUD操作
	xssTemplates.Post("/", middleware.RequirePermission("xss.template.create"), handlers.CreateXssTemplate)     // 创建XSS模板
	xssTemplates.Get("/", middleware.RequirePermission("xss.template.view"), handlers.GetXssTemplates)         // 获取XSS模板列表
	xssTemplates.Get("/:id", middleware.RequirePermission("xss.template.view"), handlers.GetXssTemplate)       // 获取XSS模板详情
	xssTemplates.Put("/:id", middleware.RequirePermission("xss.template.edit"), handlers.UpdateXssTemplate)    // 更新XSS模板
	xssTemplates.Delete("/:id", middleware.RequirePermission("xss.template.delete"), handlers.DeleteXssTemplate) // 删除XSS模板
}
