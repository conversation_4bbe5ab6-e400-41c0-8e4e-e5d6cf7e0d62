package models

// Job 队列任务模型
type Job struct {
    ID          uint64 `json:"id" gorm:"primaryKey;type:bigint unsigned;not null"`
    Queue       string `json:"queue" gorm:"type:varchar(255);not null"`
    Payload     string `json:"payload" gorm:"type:longtext;not null"`
    Attempts    uint8  `json:"attempts" gorm:"type:tinyint unsigned;not null"`
    ReservedAt  *uint  `json:"reserved_at" gorm:"type:int unsigned;default:null"`
    AvailableAt uint   `json:"available_at" gorm:"type:int unsigned;not null"`
    CreatedAt   uint   `json:"created_at" gorm:"type:int unsigned;not null"`
}

// TableName 指定表名
func (Job) TableName() string {
    return "jobs"
}