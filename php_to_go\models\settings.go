package models

import (
	"time"

	"gorm.io/gorm"
)

// Settings 网站设置模型
type Settings struct {
	ID              uint64         `json:"id" gorm:"primaryKey;type:int unsigned;not null;comment:设置ID"`
	Key             string         `json:"key" gorm:"not null;size:255;uniqueIndex;comment:设置键名"`
	Value           string         `json:"value" gorm:"type:text;comment:设置值"`
	CreatedAt       time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt       time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`
}

// TableName 指定表名
func (Settings) TableName() string {
	return "settings"
} 