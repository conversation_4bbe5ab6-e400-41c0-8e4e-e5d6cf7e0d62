import request from './index'

/**
 * 获取子域名扫描任务列表
 * @param {Object} params 查询参数
 * @returns {Promise<Array>} 返回任务列表
 */
export function getSubdomainTasks(params) {
  return request({
    url: '/admin/scan/subdomain/list',
    method: 'get',
    params
  })
}

/**
 * 创建子域名扫描任务
 * @param {Object} data 任务数据
 * @param {string} data.domain 目标域名
 * @param {string} data.scan_type 扫描类型
 * @param {Object} data.options 扫描选项
 * @returns {Promise<Object>} 返回任务信息
 */
export function createSubdomainScan(data) {
  return request({
    url: '/admin/scan/subdomain/create',
    method: 'post',
    data
  })
}

/**
 * 获取子域名扫描任务详情
 * @param {number} id 任务ID
 * @returns {Promise<Object>} 返回任务详情
 */
export function getSubdomainTask(id) {
  return request({
    url: `/admin/scan/subdomain/${id}`,
    method: 'get'
  })
}

/**
 * 停止子域名扫描任务
 * @param {number} id 任务ID
 * @returns {Promise<Object>} 返回操作结果
 */
export function stopSubdomainTask(id) {
  return request({
    url: `/admin/scan/subdomain/${id}/stop`,
    method: 'post'
  })
}

/**
 * 删除子域名扫描任务
 * @param {number} id 任务ID
 * @returns {Promise<Object>} 返回操作结果
 */
export function deleteSubdomainTask(id) {
  return request({
    url: `/admin/scan/subdomain/${id}`,
    method: 'delete'
  })
}

/**
 * 获取扫描结果
 * @param {number} id 任务ID
 * @returns {Promise<Array>} 返回扫描结果
 */
export function getSubdomainResults(id) {
  return request({
    url: `/admin/scan/subdomain/${id}/results`,
    method: 'get'
  })
}

/**
 * 导出扫描结果
 * @param {number} id 任务ID
 * @param {string} format 导出格式, 如 "csv", "json"
 * @returns {Promise<Blob>} 返回导出文件
 */
export function exportSubdomainResults(id, format) {
  return request({
    url: `/admin/scan/subdomain/${id}/export`,
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}

/**
 * 获取扫描任务列表
 * @param {Object} params - 查询参数
 * @param {string} [params.name] - 任务名称(模糊查询)
 * @param {number} [params.status] - 状态(1:等待中, 2:进行中, 3:已完成, 4:失败, 不传:所有)
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getScanList(params) {
  return request({
    url: '/scans',
    method: 'get',
    params
  })
}

/**
 * 创建扫描任务
 * @param {Object} data - 任务数据
 * @param {string} data.name - 任务名称
 * @param {string} data.target - 扫描目标(URL或IP)
 * @param {number} [data.type=1] - 扫描类型(1:Web漏洞, 2:端口扫描, 3:目录扫描)
 * @param {Object} [data.options] - 扫描选项
 * @param {string} [data.description] - 任务描述
 * @returns {Promise}
 */
export function createScan(data) {
  return request({
    url: '/scans',
    method: 'post',
    data
  })
}

/**
 * 获取单个扫描任务详情
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function getScan(id) {
  return request({
    url: `/scans/${id}`,
    method: 'get'
  })
}

/**
 * 更新扫描任务
 * @param {number} id - 任务ID
 * @param {Object} data - 需要更新的任务数据
 * @param {string} [data.name] - 任务名称
 * @param {string} [data.description] - 任务描述
 * @returns {Promise}
 */
export function updateScan(id, data) {
  return request({
    url: `/scans/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除扫描任务
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function deleteScan(id) {
  return request({
    url: `/scans/${id}`,
    method: 'delete'
  })
}

/**
 * 启动扫描任务
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function startScan(id) {
  return request({
    url: `/scans/${id}/start`,
    method: 'post'
  })
}

/**
 * 停止扫描任务
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function stopScan(id) {
  return request({
    url: `/scans/${id}/stop`,
    method: 'post'
  })
}

/**
 * 获取扫描任务结果
 * @param {number} id - 任务ID
 * @returns {Promise}
 */
export function getScanResult(id) {
  return request({
    url: `/scans/${id}/result`,
    method: 'get'
  })
}

/**
 * 获取扫描任务统计数据
 * @returns {Promise}
 */
export function getScanStats() {
  return request({
    url: '/scans/stats',
    method: 'get'
  })
} 