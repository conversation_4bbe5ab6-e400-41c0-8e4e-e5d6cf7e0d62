<template>
  <div class="job-list">
    <div class="page-header">
      <h1 class="page-title">任务管理</h1>
      <div class="page-actions">
        <el-button type="primary" @click="showCreateDialog">创建任务</el-button>
        <el-button type="danger" @click="handleClearAll">清空所有任务</el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="任务名称">
          <el-input v-model="filterForm.name" placeholder="请输入任务名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchJobs">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card shadow="hover" class="job-table-container">
      <div slot="header">
        <span>任务列表</span>
      </div>
      <el-table
        :data="jobList"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="loading">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="queue" label="队列" min-width="120"></el-table-column>
        <el-table-column prop="attempts" label="尝试次数" width="100"></el-table-column>
        <el-table-column label="预计执行时间" width="180">
          <template slot-scope="scope">
            {{ formatTimestamp(scope.row.available_at) }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template slot-scope="scope">
            {{ formatTimestamp(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row)">
              {{ getStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="viewJobDetail(scope.row)">详情</el-button>
            <el-button size="mini" type="success" @click="showUpdateDialog(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="deleteJobItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" :visible.sync="detailDialogVisible" width="600px">
      <div v-loading="detailLoading">
        <div v-if="currentJob">
          <el-descriptions border>
            <el-descriptions-item label="ID">{{ currentJob.id }}</el-descriptions-item>
            <el-descriptions-item label="队列">{{ currentJob.queue }}</el-descriptions-item>
            <el-descriptions-item label="尝试次数">{{ currentJob.attempts }}</el-descriptions-item>
            <el-descriptions-item label="预计执行时间">{{ formatTimestamp(currentJob.available_at) }}</el-descriptions-item>
            <el-descriptions-item label="保留时间" v-if="currentJob.reserved_at">{{ formatTimestamp(currentJob.reserved_at) }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatTimestamp(currentJob.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="状态">{{ getStatusText(currentJob) }}</el-descriptions-item>
            <el-descriptions-item label="数据" v-if="currentJob.payload">
              <pre>{{ formatPayload(currentJob.payload) }}</pre>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

    <!-- 创建/编辑任务对话框 -->
    <el-dialog :title="dialogType === 'create' ? '创建任务' : '编辑任务'" :visible.sync="formDialogVisible" width="500px">
      <el-form :model="jobForm" :rules="jobFormRules" ref="jobForm" label-width="100px" v-loading="formLoading">
        <el-form-item label="队列" prop="queue">
          <el-input v-model="jobForm.queue" placeholder="请输入队列名称"></el-input>
        </el-form-item>
        <el-form-item label="尝试次数" prop="attempts">
          <el-input-number v-model="jobForm.attempts" :min="0" :max="10" placeholder="请输入尝试次数"></el-input-number>
        </el-form-item>
        <el-form-item label="可用时间" prop="available_at">
          <el-date-picker
            v-model="jobForm.available_at_date"
            type="datetime"
            placeholder="选择可用时间"
            value-format="timestamp"
            @change="updateAvailableAt">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="任务数据" prop="payload">
          <el-input type="textarea" v-model="jobForm.payload" :rows="6" placeholder="请输入任务数据 (JSON格式)"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitJobForm" :loading="formLoading">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getJobs, getJob, createJob, updateJob, deleteJob, clearAllJobs } from '@/api/job'

export default {
  name: 'JobList',
  data() {
    return {
      loading: false,
      detailLoading: false,
      formLoading: false,
      // 筛选表单
      filterForm: {
        name: ''
      },
      // 任务列表数据
      jobList: [],
      // 分页
      pagination: {
        page: 1,
        page_size: 10,
        total: 0
      },
      // 选中的行
      multipleSelection: [],
      // 详情对话框
      detailDialogVisible: false,
      currentJob: null,
      // 表单对话框
      formDialogVisible: false,
      // 对话框类型：create-创建，update-更新
      dialogType: 'create',
      // 任务表单
      jobForm: {
        queue: '',
        attempts: 1,
        available_at: 0,
        available_at_date: null,
        payload: ''
      },
      // 表单验证规则
      jobFormRules: {
        queue: [
          { required: true, message: '请输入队列名称', trigger: 'blur' }
        ],
        attempts: [
          { required: true, message: '请输入尝试次数', trigger: 'change' }
        ],
        available_at_date: [
          { required: true, message: '请选择可用时间', trigger: 'change' }
        ],
        payload: [
          { required: true, message: '请输入任务数据', trigger: 'blur' },
          { validator: this.validateJSON, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchJobList()
  },
  methods: {
    // 获取任务列表
    fetchJobList() {
      this.loading = true
      const params = {
        page: this.pagination.page,
        page_size: this.pagination.page_size,
        ...this.filterForm
      }
      
      // 移除值为null或空字符串的参数
      Object.keys(params).forEach(key => {
        if (params[key] === null || params[key] === '') {
          delete params[key]
        }
      })

      getJobs(params)
        .then(response => {
          this.jobList = response.items || []
          this.pagination.total = response.total || 0
        })
        .catch(error => {
          this.$message.error(`获取任务列表失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 验证JSON格式
    validateJSON(rule, value, callback) {
      if (!value) {
        callback()
        return
      }
      try {
        JSON.parse(value)
        callback()
      } catch (error) {
        callback(new Error('请输入有效的JSON格式'))
      }
    },
    // 显示创建任务对话框
    showCreateDialog() {
      this.dialogType = 'create'
      this.resetJobForm()
      // 设置默认可用时间为当前时间
      const now = new Date();
      this.jobForm.available_at_date = now.getTime();
      this.jobForm.available_at = Math.floor(now.getTime() / 1000);
      this.formDialogVisible = true
    },
    // 显示更新任务对话框
    showUpdateDialog(row) {
      this.dialogType = 'update'
      this.resetJobForm()
      this.formLoading = true
      
      getJob(row.id)
        .then(data => {
          this.jobForm = {
            id: data.id,
            queue: data.queue || '',
            attempts: data.attempts || 1,
            available_at: data.available_at || 0,
            available_at_date: data.available_at ? data.available_at * 1000 : null,
            payload: data.payload || ''
          }
        })
        .catch(error => {
          this.$message.error(`获取任务详情失败: ${error.message}`)
          this.formDialogVisible = false
        })
        .finally(() => {
          this.formLoading = false
          this.formDialogVisible = true
        })
    },
    // 更新可用时间
    updateAvailableAt(date) {
      if (date) {
        // 将毫秒时间戳转换为秒
        this.jobForm.available_at = Math.floor(date / 1000);
      } else {
        this.jobForm.available_at = 0;
      }
    },
    // 重置任务表单
    resetJobForm() {
      this.jobForm = {
        queue: '',
        attempts: 1,
        available_at: 0,
        available_at_date: null,
        payload: ''
      }
      // 如果表单已经被创建，则重置验证
      if (this.$refs.jobForm) {
        this.$refs.jobForm.resetFields()
      }
    },
    // 提交任务表单
    submitJobForm() {
      this.$refs.jobForm.validate(valid => {
        if (!valid) {
          return false
        }
        
        this.formLoading = true
        
        // 构建API需要的数据结构
        let formData = {
          queue: this.jobForm.queue,
          attempts: this.jobForm.attempts,
          available_at: this.jobForm.available_at,
          payload: this.jobForm.payload // 直接发送字符串形式的payload
        }
        
        try {
          // 验证payload是否是有效的JSON
          JSON.parse(this.jobForm.payload)
        } catch (error) {
          this.$message.error('任务数据格式错误，请确保是有效的JSON格式')
          this.formLoading = false
          return
        }
        
        // 根据对话框类型决定是创建还是更新
        let apiCall = this.dialogType === 'create' 
          ? createJob(formData)
          : updateJob(this.jobForm.id, formData)
        
        apiCall
          .then(() => {
            this.$message.success(this.dialogType === 'create' ? '创建成功' : '更新成功')
            this.formDialogVisible = false
            this.fetchJobList()
          })
          .catch(error => {
            this.$message.error(`${this.dialogType === 'create' ? '创建' : '更新'}失败: ${error.message}`)
          })
          .finally(() => {
            this.formLoading = false
          })
      })
    },
    // 搜索任务
    searchJobs() {
      this.pagination.page = 1
      this.fetchJobList()
    },
    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        name: ''
      }
      this.searchJobs()
    },
    // 选择行变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.page_size = val
      this.fetchJobList()
    },
    // 页码变化
    handleCurrentChange(val) {
      this.pagination.page = val
      this.fetchJobList()
    },
    // 查看任务详情
    viewJobDetail(row) {
      this.detailDialogVisible = true
      this.detailLoading = true
      this.currentJob = null
      
      getJob(row.id)
        .then(data => {
          this.currentJob = data
        })
        .catch(error => {
          this.$message.error(`获取任务详情失败: ${error.message}`)
        })
        .finally(() => {
          this.detailLoading = false
        })
    },
    // 删除任务
    deleteJobItem(row) {
      this.$confirm(`确认删除任务 "${row.name || row.id}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteJob(row.id)
          .then(() => {
            this.$message.success('删除成功')
            this.fetchJobList()
          })
          .catch(error => {
            this.$message.error(`删除失败: ${error.message}`)
          })
          .finally(() => {
            this.loading = false
          })
      }).catch(() => {
        // 取消删除
      })
    },
    // 清空所有任务
    handleClearAll() {
      this.$confirm('确认清空所有任务吗？此操作不可恢复！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        clearAllJobs()
          .then(() => {
            this.$message.success('清空成功')
            this.fetchJobList()
          })
          .catch(error => {
            this.$message.error(`清空失败: ${error.message}`)
          })
          .finally(() => {
            this.loading = false
          })
      }).catch(() => {
        // 取消操作
      })
    },
    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return '未设置';
      const date = new Date(timestamp * 1000);
      return date.toLocaleString();
    },
    // 格式化payload数据
    formatPayload(payload) {
      if (!payload) return '';
      try {
        if (typeof payload === 'string') {
          return JSON.stringify(JSON.parse(payload), null, 2);
        } else {
          return JSON.stringify(payload, null, 2);
        }
      } catch (e) {
        return payload;
      }
    },
    // 获取任务状态
    getStatusText(job) {
      if (!job) return '未知';
      if (job.reserved_at) return '处理中';
      if (job.available_at > Math.floor(Date.now() / 1000)) return '等待中';
      return '待执行';
    },
    // 获取状态标签类型
    getStatusType(job) {
      if (!job) return '';
      if (job.reserved_at) return 'warning';
      if (job.available_at > Math.floor(Date.now() / 1000)) return 'info';
      return 'success';
    },
    // 格式化状态
    formatStatus(status) {
      const statusMap = {
        pending: '等待中',
        processing: '处理中',
        completed: '已完成',
        failed: '失败'
      }
      return statusMap[status] || status
    }
  }
}
</script>

<style scoped>
.job-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.job-table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 