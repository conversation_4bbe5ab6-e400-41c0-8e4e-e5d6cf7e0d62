package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetProjects 获取项目列表
// @Summary 获取项目列表
// @Description 获取项目列表，支持分页
// @Tags 项目管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为10"
// @Param title query string false "项目名称过滤(模糊匹配)"
// @Param state query int false "状态过滤(1:启用, 0:禁用)"
// @Param with_deleted query bool false "是否包含已删除记录"
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.ProjectListItem}}
// @Router /admin/project/my [get]
func GetProjects(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Project{})
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 获取分页和过滤参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "10"))
	title := c.Query("title", "")
	stateStr := c.Query("state", "")
	withDeleted := c.Query("with_deleted", "false") == "true"
	
	// 构建查询
	var query *gorm.DB
	isAdmin := utils.IsAdmin(c)
	
	// 管理员可以查看已删除的记录
	if withDeleted && isAdmin {
		query = db.Unscoped() // 包括已删除的记录
	} else {
		query = db
	}
	
	// 应用过滤条件
	// 普通用户只能查看自己的项目，管理员可以查看所有项目
	if !isAdmin {
		query = query.Where("user_id = ?", userID)
	}
	
	// 按标题过滤（如果提供）
	if title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}
	
	// 按状态过滤（如果提供）
	if stateStr != "" {
		stateInt, _ := strconv.Atoi(stateStr)
		query = query.Where("state = ?", stateInt)
	}
	
	// 计算总数
	var total int64
	query.Count(&total)
	
	// 获取分页数据
	var projects []models.Project
	query.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&projects)
	
	// 转换为DTO并统计内容记录数
	var projectDTOs []dto.ProjectListItem
	for _, project := range projects {
		// 统计项目内容记录数
		var contentCount int64
		database.DB.Model(&models.ProjectContent{}).Where("project_id = ?", project.ID).Count(&contentCount)

		deleted := false
		if !project.DeletedAt.Time.IsZero() {
			deleted = true
		}
		
		projectDTOs = append(projectDTOs, dto.ProjectListItem{
			ID:          project.ID,
			Title:       project.Title,
			UniqueKey:   project.UniqueKey,
			Description: project.Description,
			State:       project.State,
			UserID:      project.UserID,
			IsNewRecord: project.IsNewRecord,
			ContentCount: contentCount,
			Deleted:     deleted,
			CreatedAt:   project.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	
	// 返回分页响应
	return utils.SuccessPaginated(c, "获取项目列表成功", projectDTOs, total, page, pageSize)
}

// GetProject 获取项目详情
// @Summary 获取项目详情
// @Description 根据ID获取项目详情
// @Tags 项目管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "项目ID"
// @Success 200 {object} dto.StandardResponse{data=dto.ProjectResponse}
// @Router /admin/project/my/{id} [get]
func GetProject(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Project{})
	id, err := strconv.Atoi(c.Params("id"))
	if err != nil {
		return utils.BadRequest(c, "无效的项目ID", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	var project models.Project
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目不存在或无权访问")
		}
		return utils.ServerError(c, "获取项目失败", err)
	}
	
	// 转换为DTO
	projectDTO := dto.ProjectResponse{
		ID:            project.ID,
		Title:         project.Title,
		UniqueKey:     project.UniqueKey,
		Description:   project.Description,
		Code:          project.Code,
		UserID:        project.UserID,
		ModuleID:      project.ModuleID,
		ModuleExtParam: project.ModuleExtParam,
		IsNewRecord:   project.IsNewRecord,
		State:         project.State,
		CreatedAt:     project.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     project.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "获取项目成功", projectDTO)
}

// CreateProject 创建项目
// @Summary 创建项目
// @Description 创建新的项目
// @Tags 项目管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param project body dto.ProjectRequest true "项目信息"
// @Success 200 {object} dto.StandardResponse{data=dto.ProjectResponse}
// @Router /admin/project/my [post]
func CreateProject(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Project{})
	
	// 解析请求体
	var req dto.ProjectRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 检查UniqueKey是否已存在
	var count int64
	db.Model(&models.Project{}).Where("unique_key = ?", req.UniqueKey).Count(&count)
	if count > 0 {
		return utils.BadRequest(c, "唯一标识已存在，请更换", nil)
	}
	
	// 创建新项目
	project := models.Project{
		Title:         req.Title,
		UniqueKey:     req.UniqueKey,
		Description:   req.Description,
		Code:          req.Code,
		UserID:        userID,
		ModuleID:      req.ModuleID,
		ModuleExtParam: req.ModuleExtParam,
		IsNewRecord:   false,
		State:         req.State,
	}
	
	if err := db.Create(&project).Error; err != nil {
		return utils.ServerError(c, "创建项目失败", err)
	}
	
	// 转换为DTO
	projectDTO := dto.ProjectResponse{
		ID:            project.ID,
		Title:         project.Title,
		UniqueKey:     project.UniqueKey,
		Description:   project.Description,
		Code:          project.Code,
		UserID:        project.UserID,
		ModuleID:      project.ModuleID,
		ModuleExtParam: project.ModuleExtParam,
		IsNewRecord:   project.IsNewRecord,
		State:         project.State,
		CreatedAt:     project.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     project.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "创建项目成功", projectDTO)
}

// UpdateProject 更新项目
// @Summary 更新项目
// @Description 更新现有项目信息
// @Tags 项目管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "项目ID"
// @Param project body dto.ProjectUpdateRequest true "项目更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.ProjectResponse}
// @Router /admin/project/my/{id} [put]
func UpdateProject(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Project{})
	id, err := strconv.Atoi(c.Params("id"))
	if err != nil {
		return utils.BadRequest(c, "无效的项目ID", err)
	}
	
	// 解析请求体
	var req dto.ProjectUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 查找项目
	var project models.Project
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目不存在或无权访问")
		}
		return utils.ServerError(c, "获取项目失败", err)
	}
	
	// 更新字段
	updates := make(map[string]interface{})
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Code != nil {
		updates["code"] = *req.Code
	}
	if req.ModuleID != nil {
		updates["module_id"] = *req.ModuleID
	}
	if req.ModuleExtParam != nil {
		updates["module_ext_param"] = *req.ModuleExtParam
	}
	if req.State != nil {
		updates["state"] = *req.State
	}
	if req.IsNewRecord != nil {
		updates["is_new_record"] = *req.IsNewRecord
	}
	
	// 添加更新时间
	updates["updated_at"] = time.Now()
	
	// 执行更新
	if err := db.Model(&project).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新项目失败", err)
	}
	
	// 重新获取更新后的项目
	if err := db.First(&project, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的项目失败", err)
	}
	
	// 转换为DTO
	projectDTO := dto.ProjectResponse{
		ID:            project.ID,
		Title:         project.Title,
		UniqueKey:     project.UniqueKey,
		Description:   project.Description,
		Code:          project.Code,
		UserID:        project.UserID,
		ModuleID:      project.ModuleID,
		ModuleExtParam: project.ModuleExtParam,
		IsNewRecord:   project.IsNewRecord,
		State:         project.State,
		CreatedAt:     project.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     project.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "更新项目成功", projectDTO)
}

// DeleteProject 删除项目
// @Summary 删除项目
// @Description 根据ID删除项目
// @Tags 项目管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "项目ID"
// @Success 200 {object} dto.StandardResponse
// @Router /admin/project/my/{id} [delete]
func DeleteProject(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Project{})
	id, err := strconv.Atoi(c.Params("id"))
	if err != nil {
		return utils.BadRequest(c, "无效的项目ID", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 查找项目
	var project models.Project
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目不存在或无权访问")
		}
		return utils.ServerError(c, "获取项目失败", err)
	}
	
	// 执行软删除
	if err := db.Delete(&project).Error; err != nil {
		return utils.ServerError(c, "删除项目失败", err)
	}
	
	return utils.Success(c, "删除项目成功", nil)
}

// ViewCode 查看项目代码
// @Summary 查看项目代码
// @Description 根据项目ID查看代码
// @Tags 项目管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param projectid path int true "项目ID"
// @Success 200 {object} dto.StandardResponse{data=string}
// @Router /admin/project/viewcode/{projectid} [get]
func ViewCode(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Project{})
	projectID, err := strconv.Atoi(c.Params("projectid"))
	if err != nil {
		return utils.BadRequest(c, "无效的项目ID", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 查找项目
	var project models.Project
	if err := db.Where("id = ? AND user_id = ?", projectID, userID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目不存在或无权访问")
		}
		return utils.ServerError(c, "获取项目失败", err)
	}
	
	return utils.Success(c, "获取项目代码成功", project.Code)
}

// Preview 预览项目
// @Summary 预览项目
// @Description 预览项目内容
// @Tags 项目管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param project_id query int true "项目ID"
// @Success 200 {object} dto.StandardResponse
// @Router /admin/project/my/preview [get]
func Preview(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Project{})
	
	// 从查询参数中获取项目ID
	projectIDStr := c.Query("project_id")
	if projectIDStr == "" {
		return utils.BadRequest(c, "缺少项目ID参数", nil)
	}
	
	projectID, err := strconv.Atoi(projectIDStr)
	if err != nil {
		return utils.BadRequest(c, "无效的项目ID", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 查找项目
	var project models.Project
	if err := db.Where("id = ? AND user_id = ?", projectID, userID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目不存在或无权访问")
		}
		return utils.ServerError(c, "获取项目失败", err)
	}

	return utils.Success(c, "获取项目预览成功", map[string]interface{}{
		"title": project.Title,
		"code":  project.Code,
	})
}

// ToggleProjectState 切换项目状态
// @Summary 切换项目状态
// @Description 切换项目的启用/禁用状态
// @Tags 项目管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "项目ID"
// @Success 200 {object} dto.StandardResponse{data=dto.ProjectResponse}
// @Router /admin/project/my/{id}/toggle [put]
func ToggleProjectState(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Project{})
	id, err := strconv.Atoi(c.Params("id"))
	if err != nil {
		return utils.BadRequest(c, "无效的项目ID", err)
	}

	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)

	// 查找项目
	var project models.Project
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目不存在或无权访问")
		}
		return utils.ServerError(c, "获取项目失败", err)
	}

	// 切换状态
	newState := int8(0)
	if project.State == 0 {
		newState = 1
	}

	// 更新状态
	updates := map[string]interface{}{
		"state":      newState,
		"updated_at": time.Now(),
	}

	if err := db.Model(&project).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新项目状态失败", err)
	}

	// 重新获取更新后的项目
	if err := db.First(&project, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的项目失败", err)
	}

	// 转换为DTO
	projectDTO := dto.ProjectResponse{
		ID:            project.ID,
		Title:         project.Title,
		UniqueKey:     project.UniqueKey,
		Description:   project.Description,
		Code:          project.Code,
		UserID:        project.UserID,
		ModuleID:      project.ModuleID,
		ModuleExtParam: project.ModuleExtParam,
		IsNewRecord:   project.IsNewRecord,
		State:         project.State,
		CreatedAt:     project.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     project.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	return utils.Success(c, "切换项目状态成功", projectDTO)
}

// RestoreProject 恢复已删除的项目
// @Summary 恢复已删除的项目
// @Description 根据ID恢复被软删除的项目
// @Tags 项目管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "项目ID"
// @Success 200 {object} dto.StandardResponse
// @Router /admin/project/my/restore/{id} [put]
func RestoreProject(c *fiber.Ctx) error {
	db := database.DB.Model(&models.Project{})
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的项目ID", err)
	}
	
	// 执行恢复操作
	// Unscoped用于查询包括已删除记录
	var project models.Project
	result := db.Unscoped().Where("id = ?", id).First(&project)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "项目不存在")
		}
		return utils.ServerError(c, "获取项目失败", result.Error)
	}
	
	// 检查记录是否已被删除
	if project.DeletedAt.Time.IsZero() {
		return utils.BadRequest(c, "此项目未被删除", nil)
	}

	// 检查权限：普通用户只能恢复自己的项目
	if !utils.IsAdmin(c) {
		userID := utils.GetUserIDFromContext(c)
		if project.UserID != userID {
			return utils.Forbidden(c, "您没有权限恢复此项目", nil)
		}
	}
	
	// 执行恢复
	if err := db.Unscoped().Model(&models.Project{}).Where("id = ?", id).Update("deleted_at", nil).Error; err != nil {
		return utils.ServerError(c, "恢复项目失败", err)
	}
	
	return utils.Success(c, "恢复项目成功", nil)
}