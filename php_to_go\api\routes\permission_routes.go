package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupPermissionRoutes 注册权限管理相关路由
func SetupPermissionRoutes(router fiber.Router) {
	// 权限管理路由组
	permissionGroup := router.Group("/permissions")

	// 添加认证中间件
	permissionGroup.Use(utils.RequireAuthentication)

	// 权限列表
	permissionGroup.Get("/", middleware.RequirePermission("permissions.view"), handlers.GetPermissions)
	
	// 获取权限树形结构
	permissionGroup.Get("/tree", middleware.RequirePermission("permissions.view"), handlers.GetPermissionTree)
	
	// 获取权限详情
	permissionGroup.Get("/:id", middleware.RequirePermission("permissions.view"), handlers.GetPermissionDetail)
	
	// 创建权限
	permissionGroup.Post("/", middleware.RequirePermission("permissions.create"), handlers.CreatePermission)
	
	// 批量删除权限
	permissionGroup.Post("/batch-delete", middleware.RequirePermission("permissions.delete"), handlers.BatchDeletePermissions)
	
	// 检查当前用户权限
	permissionGroup.Get("/check/:slug", utils.RequireAuthentication, handlers.CheckUserPermission)
	
	// 更新权限
	permissionGroup.Put("/:id", middleware.RequirePermission("permissions.edit"), handlers.UpdatePermission)
	
	// 删除权限
	permissionGroup.Delete("/:id", middleware.RequirePermission("permissions.delete"), handlers.DeletePermission)
} 