package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"log"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetMenus 获取菜单列表
// @Summary 获取菜单列表
// @Description 获取系统中的所有菜单
// @Tags 菜单管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param title query string false "菜单标题(模糊搜索)"
// @Success 200 {object} dto.StandardResponse{data=[]dto.MenuResponse}
// @Router /admin/menu/index [get]
func GetMenus(c *fiber.Ctx) error {
	// 获取查询参数
	title := c.Query("title", "")

	// 构建查询
	query := database.DB.Model(&models.Menu{})

	// 应用筛选条件
	if title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}

	// 获取菜单列表
	var menus []models.Menu
	if err := query.Order("parent_id ASC, `order` ASC").Find(&menus).Error; err != nil {
		return utils.ServerError(c, "获取菜单列表失败", err)
	}

	// 构建菜单树
	menuTree := BuildMenuTree(menus, 0)

	return utils.Success(c, "获取菜单列表成功", menuTree)
}

// GetMenuDetail 获取菜单详情
// @Summary 获取菜单详情
// @Description 获取指定ID的菜单详情
// @Tags 菜单管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "菜单ID"
// @Success 200 {object} dto.StandardResponse{data=dto.MenuDetailResponse}
// @Failure 404 {object} dto.StandardResponse "菜单不存在"
// @Router /admin/menu/index/{id} [get]
func GetMenuDetail(c *fiber.Ctx) error {
	// 获取菜单ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的菜单ID", err)
	}

	// 查询菜单
	var menu models.Menu
	if err := database.DB.Preload("Roles").First(&menu, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "菜单不存在")
		}
		return utils.ServerError(c, "获取菜单失败", err)
	}

	// 获取关联的角色ID列表
	var roleIDs []uint64
	for _, role := range menu.Roles {
		roleIDs = append(roleIDs, role.ID)
	}

	// 转换为DTO
	response := dto.MenuDetailResponse{
		ID:       menu.ID,
		ParentID: menu.ParentID,
		Order:    menu.Order,
		Title:    menu.Title,
		Icon:     menu.Icon,
		URI:      menu.URI,
		RoleIDs:  roleIDs,
	}

	return utils.Success(c, "获取菜单详情成功", response)
}

// CreateMenu 创建菜单
// @Summary 创建菜单
// @Description 创建一个新的菜单
// @Tags 菜单管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param menu body dto.MenuCreateDTO true "菜单信息"
// @Success 200 {object} dto.StandardResponse{data=dto.MenuResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/menu/index [post]
func CreateMenu(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.MenuCreateDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 创建菜单
	menu := models.Menu{
		ParentID: req.ParentID,
		Order:    req.Order,
		Title:    req.Title,
		Icon:     req.Icon,
		URI:      req.URI,
	}

	err := database.DB.Transaction(func(tx *gorm.DB) error {
		// 创建菜单
		if err := tx.Create(&menu).Error; err != nil {
			return err
		}

		// 处理角色关联
		if len(req.RoleIDs) > 0 {
			var roles []models.Role
			if err := tx.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
				return err
			}
			if err := tx.Model(&menu).Association("Roles").Replace(roles); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "创建菜单失败", err)
	}

	// 转换为DTO
	response := dto.MenuResponse{
		ID:       menu.ID,
		ParentID: menu.ParentID,
		Order:    menu.Order,
		Title:    menu.Title,
		Icon:     menu.Icon,
		URI:      menu.URI,
	}

	return utils.Success(c, "创建菜单成功", response)
}

// UpdateMenu 更新菜单
// @Summary 更新菜单
// @Description 更新已存在的菜单信息
// @Tags 菜单管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "菜单ID"
// @Param menu body dto.MenuUpdateDTO true "菜单更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.MenuResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "菜单不存在"
// @Router /admin/menu/index/{id} [put]
func UpdateMenu(c *fiber.Ctx) error {
	// 获取菜单ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的菜单ID", err)
	}

	// 查询菜单
	var menu models.Menu
	if err := database.DB.First(&menu, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "菜单不存在")
		}
		return utils.ServerError(c, "获取菜单失败", err)
	}

	// 解析请求体
	var req dto.MenuUpdateDTO
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}

	// 更新菜单
	menu.ParentID = req.ParentID
	menu.Order = req.Order
	menu.Title = req.Title
	menu.Icon = req.Icon
	menu.URI = req.URI

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 更新菜单基本信息
		if err := tx.Save(&menu).Error; err != nil {
			return err
		}

		// 处理角色关联
		if req.RoleIDs != nil {
			var roles []models.Role
			if err := tx.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
				return err
			}
			if err := tx.Model(&menu).Association("Roles").Replace(roles); err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "更新菜单失败", err)
	}

	// 转换为DTO
	response := dto.MenuResponse{
		ID:       menu.ID,
		ParentID: menu.ParentID,
		Order:    menu.Order,
		Title:    menu.Title,
		Icon:     menu.Icon,
		URI:      menu.URI,
	}

	return utils.Success(c, "更新菜单成功", response)
}

// DeleteMenu 删除菜单
// @Summary 删除菜单
// @Description 删除指定的菜单
// @Tags 菜单管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "菜单ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "菜单不存在"
// @Router /admin/menu/index/{id} [delete]
func DeleteMenu(c *fiber.Ctx) error {
	// 获取菜单ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的菜单ID", err)
	}

	// 检查是否有子菜单
	var childCount int64
	if err := database.DB.Model(&models.Menu{}).Where("parent_id = ?", id).Count(&childCount).Error; err != nil {
		return utils.ServerError(c, "检查子菜单失败", err)
	}

	if childCount > 0 {
		return utils.BadRequest(c, "无法删除含有子菜单的菜单", nil)
	}

	// 查询菜单
	var menu models.Menu
	if err := database.DB.First(&menu, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "菜单不存在")
		}
		return utils.ServerError(c, "获取菜单失败", err)
	}

	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 清除关联
		if err := tx.Model(&menu).Association("Roles").Clear(); err != nil {
			return err
		}

		// 删除菜单
		if err := tx.Delete(&menu).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return utils.ServerError(c, "删除菜单失败", err)
	}

	return utils.Success(c, "删除菜单成功", nil)
}

// GetUserMenuTree 获取当前用户有权限访问的菜单树
// @Summary 获取用户菜单树
// @Description 获取当前登录用户有权限访问的菜单树形结构
// @Tags 菜单管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} dto.StandardResponse{data=[]dto.MenuResponse}
// @Router /admin/menu/tree [get]
func GetUserMenuTree(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		log.Printf("菜单树API: 未获取到用户ID")
		return utils.Unauthorized(c, "未获取到用户信息", nil)
	}
	
	log.Printf("菜单树API: 获取用户ID=%d成功", userID)

	// 检查是否是管理员
	isAdmin := utils.IsAdmin(c)
	log.Printf("菜单树API: 用户ID=%d, 是否为管理员=%v", userID, isAdmin)

	// 获取所有菜单，以便构建完整菜单树
	var allMenus []models.Menu
	if err := database.DB.Order("parent_id ASC, `order` ASC").Find(&allMenus).Error; err != nil {
		log.Printf("菜单树API: 获取所有菜单失败: %v", err)
		return utils.ServerError(c, "获取所有菜单失败", err)
	}
	
	log.Printf("菜单树API: 获取到总菜单数量: %d", len(allMenus))
	
	// 如果是管理员，返回所有菜单
	if isAdmin {
		log.Printf("菜单树API: 用户是管理员，返回所有菜单")
		menuTree := BuildMenuTree(allMenus, 0)
		return utils.Success(c, "获取菜单树成功", menuTree)
	}
	
	// 非管理员，需要获取用户角色和对应的菜单权限
	log.Printf("菜单树API: 用户不是管理员，开始获取角色关联的菜单")
	
	// 获取用户角色
	var roles []models.Role
	if err := database.DB.Table("admin_roles").
		Select("admin_roles.*").
		Joins("JOIN admin_role_users ON admin_roles.id = admin_role_users.role_id").
		Where("admin_role_users.user_id = ?", userID).
		Find(&roles).Error; err != nil {
		log.Printf("菜单树API: 获取用户角色失败: %v", err)
		return utils.ServerError(c, "获取用户角色失败", err)
	}
	
	var roleIDs []uint64
	for _, role := range roles {
		roleIDs = append(roleIDs, role.ID)
		log.Printf("菜单树API: 用户角色: ID=%d, 名称=%s", role.ID, role.Name)
		
		// 检查角色是否有完全菜单访问权限
		if role.AllMenuAccess {
			log.Printf("菜单树API: 用户角色(ID=%d)拥有完全菜单访问权限，返回所有菜单", role.ID)
			menuTree := BuildMenuTree(allMenus, 0)
			return utils.Success(c, "获取菜单树成功", menuTree)
		}
	}
	
	log.Printf("菜单树API: 用户拥有 %d 个角色, 角色IDs: %v", len(roleIDs), roleIDs)
	
	// 如果用户没有任何角色，返回空菜单
	if len(roleIDs) == 0 {
		log.Printf("菜单树API: 用户没有任何角色，返回空菜单")
		return utils.Success(c, "获取菜单树成功", []dto.MenuResponse{})
	}
	
	// 获取用户角色关联的所有菜单ID
	var menuIDs []uint64
	err := database.DB.Table("admin_role_menu").
		Select("DISTINCT menu_id").
		Where("role_id IN ?", roleIDs).
		Pluck("menu_id", &menuIDs).Error
	
	if err != nil {
		log.Printf("菜单树API: 获取角色关联的菜单失败: %v", err)
		return utils.ServerError(c, "获取用户菜单权限失败", err)
	}
	
	log.Printf("菜单树API: 用户角色关联的菜单IDs: %v，共 %d 个", menuIDs, len(menuIDs))
	
	// 如果用户没有任何菜单权限，返回空菜单
	if len(menuIDs) == 0 {
		log.Printf("菜单树API: 用户角色没有关联任何菜单，返回空菜单")
		return utils.Success(c, "获取菜单树成功", []dto.MenuResponse{})
	}
	
	// 构建菜单ID映射，用于快速检查权限
	menuIDMap := make(map[uint64]bool)
	for _, id := range menuIDs {
		menuIDMap[id] = true
	}
	
	// 递归查找所有父级菜单ID，确保菜单结构完整
	parentMenuIDs := make(map[uint64]bool)
	
	// 递归函数，查找所有父菜单
	var findParentMenus func(menuID uint64)
	findParentMenus = func(menuID uint64) {
		for _, menu := range allMenus {
			if menu.ID == menuID {
				// 如果有父菜单且尚未处理过
				if menu.ParentID > 0 && !parentMenuIDs[menu.ParentID] && !menuIDMap[menu.ParentID] {
					parentMenuIDs[menu.ParentID] = true
					log.Printf("菜单树API: 为菜单ID=%d添加父菜单ID=%d", menuID, menu.ParentID)
					// 递归处理父菜单的父菜单
					findParentMenus(menu.ParentID)
				}
				break
			}
		}
	}
	
	// 为每个用户有权限的菜单查找其父菜单
	for menuID := range menuIDMap {
		findParentMenus(menuID)
	}
	
	log.Printf("菜单树API: 找到 %d 个父级菜单", len(parentMenuIDs))
	
	// 合并用户有权限访问的菜单和必要的父菜单
	authorizedMenuIDs := make(map[uint64]bool)
	for menuID := range menuIDMap {
		authorizedMenuIDs[menuID] = true
	}
	for menuID := range parentMenuIDs {
		authorizedMenuIDs[menuID] = true
	}
	
	log.Printf("菜单树API: 合并后总共有 %d 个菜单ID", len(authorizedMenuIDs))
	
	// 创建授权菜单列表
	var authorizedMenus []models.Menu
	for _, menu := range allMenus {
		if authorizedMenuIDs[menu.ID] {
			authorizedMenus = append(authorizedMenus, menu)
		}
	}
	
	log.Printf("菜单树API: 过滤后得到 %d 个授权菜单", len(authorizedMenus))
	
	// 构建菜单树
	menuTree := BuildMenuTreeWithPermission(authorizedMenus, 0, menuIDMap)
	log.Printf("菜单树API: 生成的菜单树有 %d 个顶级菜单项", len(menuTree))
	
	return utils.Success(c, "获取菜单树成功", menuTree)
}

// BuildMenuTreeWithPermission 递归构建菜单树，包含权限检查
func BuildMenuTreeWithPermission(menus []models.Menu, parentID uint64, permissionMap map[uint64]bool) []dto.MenuResponse {
	var tree []dto.MenuResponse

	for _, menu := range menus {
		if menu.ParentID == parentID {
			// 创建菜单节点
			node := dto.MenuResponse{
				ID:       menu.ID,
				ParentID: menu.ParentID,
				Order:    menu.Order,
				Title:    menu.Title,
				Icon:     menu.Icon,
				URI:      menu.URI,
			}
			
			// 递归查找子菜单，只包含有权限的子菜单
			children := BuildMenuTreeWithPermission(menus, menu.ID, permissionMap)
			
			// 仅当当前菜单有权限访问或者有子菜单时才添加到树中
			if permissionMap[menu.ID] || len(children) > 0 {
				if len(children) > 0 {
					node.Children = children
				}
				tree = append(tree, node)
			}
		}
	}

	return tree
}

// BuildMenuTree 递归构建菜单树
func BuildMenuTree(menus []models.Menu, parentID uint64) []dto.MenuResponse {
	var tree []dto.MenuResponse

	for _, menu := range menus {
		if menu.ParentID == parentID {
			node := dto.MenuResponse{
				ID:       menu.ID,
				ParentID: menu.ParentID,
				Order:    menu.Order,
				Title:    menu.Title,
				Icon:     menu.Icon,
				URI:      menu.URI,
			}
			
			// 查找子菜单
			children := BuildMenuTree(menus, menu.ID)
			if len(children) > 0 {
				node.Children = children
			}
			
			tree = append(tree, node)
		}
	}

	return tree
}
