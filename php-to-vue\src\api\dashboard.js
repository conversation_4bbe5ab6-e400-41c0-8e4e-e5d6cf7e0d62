import request from './index'

/**
 * 获取控制面板数据
 * @returns {Promise<Object>} 控制面板数据
 */
export function getDashboardData() {
  return request({
    url: '/admin/dashboard',
    method: 'get'
  })
}

/**
 * 获取最近操作日志
 * @param {Number} limit 限制数量，默认为10
 * @returns {Promise<Array>} 操作日志列表
 */
export function getDashboardLogs(limit = 10) {
  return request({
    url: '/admin/dashboard/logs',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取系统信息
 * @returns {Promise<Object>} 系统信息
 */
export function getSystemInfo() {
  return request({
    url: '/admin/dashboard/system',
    method: 'get'
  })
} 