<template>
  <div class="user-profile">
    <el-card shadow="hover" class="profile-card">
      <div slot="header">
        <span>个人资料</span>
      </div>
      
      <el-row :gutter="20" v-loading="loading">
        <el-col :span="8">
          <div class="avatar-container">
            <el-avatar :size="120" :src="userAvatar" class="avatar"></el-avatar>
            <div class="upload-avatar">
              <el-upload
                class="avatar-uploader"
                :http-request="handleAvatarUpload"
                action="#"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload">
                <el-button size="small" type="primary">更换头像</el-button>
              </el-upload>
            </div>
          </div>
          <div class="user-info">
            <h3>{{ profile.username || '加载中...' }}</h3>
            <p>
              <el-tag v-for="(role, index) in profile.roles" :key="index" type="info" 
                      class="role-tag">{{ role.name }}</el-tag>
            </p>
          </div>
        </el-col>
        
        <el-col :span="16">
          <el-form 
            ref="profileForm" 
            :model="profileForm" 
            :rules="rules" 
            label-width="80px"
            status-icon
          >
            <el-form-item label="用户名" prop="username">
              <el-input v-model="profileForm.username" disabled></el-input>
            </el-form-item>
            
            <el-form-item label="姓名" prop="name">
              <el-input v-model="profileForm.name"></el-input>
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="profileForm.email" type="email"></el-input>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :loading="submitting" @click="submitForm">保存修改</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getProfile, updateProfile, uploadAvatar } from '@/api/auth'
import { getImageUrl } from '@/utils/helpers'

export default {
  name: 'UserProfile',
  data() {
    const validateEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入邮箱地址'))
      } else {
        const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
        if (!emailPattern.test(value)) {
          callback(new Error('请输入有效的邮箱地址'))
        } else {
          callback()
        }
      }
    }

    return {
      loading: false,
      submitting: false,
      uploadingAvatar: false,
      profile: {
        id: null,
        username: '',
        name: '',
        email: '',
        avatar: '',
        roles: [],
        permissions: [],
        roleSlugs: []
      },
      profileForm: {
        username: '',
        name: '',
        email: '',
        avatar: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    userAvatar() {
      // 如果有头像，显示用户头像，否则显示默认头像
      if (this.profile.avatar) {
        return getImageUrl(this.profile.avatar);
      }
      return 'https://cube.elemecdn.com/3/7c/********************************.png'
    }
  },
  methods: {
    // 获取用户资料
    async fetchUserProfile() {
      this.loading = true
      try {
        const response = await getProfile()
        this.profile = response
        // 将数据复制到表单对象
        this.profileForm = {
          username: this.profile.username,
          name: this.profile.name,
          email: this.profile.email,
          avatar: this.profile.avatar
        }
        // 保存到本地存储
        localStorage.setItem('user', JSON.stringify({
          id: this.profile.id,
          username: this.profile.username,
          name: this.profile.name,
          avatar: this.profile.avatar,
          role: this.profile.roles && this.profile.roles.length > 0 ? 1 : 0,
          is_admin: this.profile.roleSlugs && this.profile.roleSlugs.includes('admin')
        }))
      } catch (error) {
        console.error('获取用户资料出错:', error)
        this.$message.error('获取用户资料失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 提交表单
    submitForm() {
      this.$refs.profileForm.validate(async (valid) => {
        if (!valid) {
          return false
        }

        this.submitting = true
        try {
          const response = await updateProfile({
            name: this.profileForm.name,
            email: this.profileForm.email,
            avatar: this.profileForm.avatar
          })

          this.$message.success('资料更新成功')
          this.profile = response
          
          // 更新存储的用户信息
          const userInfo = JSON.parse(localStorage.getItem('user') || '{}')
          userInfo.name = this.profile.name
          userInfo.avatar = this.profile.avatar
          localStorage.setItem('user', JSON.stringify(userInfo))
        } catch (error) {
          console.error('更新用户资料失败:', error)
          this.$message.error('更新用户资料失败: ' + error.message)
        } finally {
          this.submitting = false
        }
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.profileForm.resetFields()
      // 将原始数据重新填入表单
      this.profileForm = {
        username: this.profile.username,
        name: this.profile.name,
        email: this.profile.email,
        avatar: this.profile.avatar
      }
    },

    // 自定义头像上传处理函数
    async handleAvatarUpload(options) {
      try {
        this.uploadingAvatar = true
        const response = await uploadAvatar(options.file)

        // 直接处理成功的响应
        if (response && response.url) {

          this.profileForm.avatar = response.url
          
          // 更新存储的用户信息
          const userInfo = JSON.parse(localStorage.getItem('user') || '{}')
          userInfo.avatar = response.url
          localStorage.setItem('user', JSON.stringify(userInfo))
        } else {
          this.$message.error('头像上传失败: 未获取到有效的图片URL')
        }
        return response
      } catch (error) {
        this.$message.error('头像上传失败: ' + error.message)
        options.onError(error)
      } finally {
        this.uploadingAvatar = false
      }
    },

    // 头像上传前的验证
    beforeAvatarUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('上传头像图片只能是图片格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isImage && isLt2M
    },

    // 头像上传成功的回调 (当使用action时会调用此函数)
    handleAvatarSuccess(response) {
      console.log('头像上传响应:', response)
      // 检查响应格式
      if (response.url) {
        this.profileForm.avatar = response.url
        this.$message.success('头像上传成功')
        
        // 更新存储的用户信息
        const userInfo = JSON.parse(localStorage.getItem('user') || '{}')
        userInfo.avatar = response.url
        localStorage.setItem('user', JSON.stringify(userInfo))
        
        // 立即更新个人资料
        this.submitForm()
      } else {
        this.$message.error(response.message || '头像上传失败: 未获取到有效的图片URL')
      }
    }
  },
  created() {
    this.fetchUserProfile()
  }
}
</script>

<style scoped>
.user-profile {
  padding: 20px;
}

.profile-card {
  margin-bottom: 20px;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.avatar {
  margin-bottom: 15px;
  border: 2px solid #eee;
}

.upload-avatar {
  margin-top: 10px;
}

.user-info {
  text-align: center;
  margin-bottom: 20px;
}

.user-info h3 {
  margin-bottom: 10px;
}

.role-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.permissions-section {
  margin-top: 20px;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.permission-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.no-permissions {
  color: #909399;
  font-style: italic;
}
</style> 