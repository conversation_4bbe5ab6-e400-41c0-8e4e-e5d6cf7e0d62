services:
  # MySQL服务 - 生产环境配置
  mysql:
    image: mysql:8.0.42
    container_name: go-fiber-mysql
    restart: unless-stopped
    env_file:
      - .env
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
    ports:
      - "3308:3306"  # 避免与系统MySQL冲突
    volumes:
      - mysql_data:/var/lib/mysql
      - ../database/go_fiber.sql:/docker-entrypoint-initdb.d/go_fiber.sql
      - ./mysql.conf:/etc/mysql/conf.d/mysql.conf
    networks:
      - go-fiber-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    command: --default-authentication-plugin=mysql_native_password --innodb-buffer-pool-size=256M

  # 使用服务器现有的Redis服务，不部署Redis容器

  # API服务器 - 生产环境
  api:
    build:
      context: .
      dockerfile: Dockerfile.server.prod
    container_name: go-fiber-api
    restart: unless-stopped
    env_file:
      - .env
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - go-fiber-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 队列Worker服务 - 生产环境
  worker:
    build:
      context: .
      dockerfile: Dockerfile.server.prod
    container_name: go-fiber-worker
    restart: unless-stopped
    env_file:
      - .env
    depends_on:
      api:
        condition: service_healthy
    networks:
      - go-fiber-network
    command: /app/worker -monitor -monitor-addr :8080
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx反向代理 - 生产环境
  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx.prod
    container_name: go-fiber-nginx
    restart: unless-stopped
    ports:
      - "88:80"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      api:
        condition: service_healthy
      worker:
        condition: service_healthy
    networks:
      - go-fiber-network
    environment:
      NODE_ENV: production
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

networks:
  go-fiber-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  nginx_logs:
    driver: local
