package processors

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"go-fiber-api/database"
	"go-fiber-api/utils/queue/tasks"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/hibiken/asynq"
)

// DataExportProcessor 处理数据导出任务
func DataExportProcessor(ctx context.Context, t *asynq.Task) error {
	var payload tasks.DataExportPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("错误的任务负载: %v", err)
	}

	log.Printf("处理用户 %d 的数据导出, 类型: %s\n", payload.UserID, payload.ExportType)

	// 根据导出类型执行不同的导出操作
	var err error
	var filePath string

	switch payload.ExportType {
	case "user_data":
		filePath, err = exportUserData(payload.UserID)
	case "transactions":
		filePath, err = exportTransactions(payload.UserID, payload.Filters)
	case "activities":
		filePath, err = exportActivities(payload.UserID, payload.Filters)
	default:
		return fmt.Errorf("不支持的导出类型: %s", payload.ExportType)
	}

	if err != nil {
		return fmt.Errorf("导出失败: %v", err)
	}

	// 记录导出完成
	exportRecord := map[string]interface{}{
		"user_id":     payload.UserID,
		"export_type": payload.ExportType,
		"file_path":   filePath,
		"status":      "completed",
		"created_at":  time.Now(),
	}

	if err := database.DB.Table("data_exports").Create(exportRecord).Error; err != nil {
		return fmt.Errorf("记录导出信息失败: %v", err)
	}

	// 可以在这里添加通知用户导出完成的逻辑
	// 例如，发送邮件或创建系统通知

	log.Printf("数据导出完成，文件保存在: %s\n", filePath)
	return nil
}

// exportUserData 导出用户数据
func exportUserData(userID uint64) (string, error) {
	// 创建导出目录
	exportDir := filepath.Join("assets", "exports", fmt.Sprintf("user_%d", userID))
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return "", fmt.Errorf("创建导出目录失败: %v", err)
	}

	// 创建CSV文件
	fileName := fmt.Sprintf("user_data_%d_%s.csv", userID, time.Now().Format("20060102_150405"))
	filePath := filepath.Join(exportDir, fileName)
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	// 创建CSV写入器
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入标题行
	headers := []string{"ID", "用户名", "邮箱", "注册时间", "最后登录时间"}
	if err := writer.Write(headers); err != nil {
		return "", fmt.Errorf("写入标题失败: %v", err)
	}

	// 查询用户数据
	var userData map[string]interface{}
	if err := database.DB.Table("users").Where("id = ?", userID).First(&userData).Error; err != nil {
		return "", fmt.Errorf("查询用户数据失败: %v", err)
	}

	// 写入用户数据
	row := []string{
		fmt.Sprintf("%v", userData["id"]),
		fmt.Sprintf("%v", userData["username"]),
		fmt.Sprintf("%v", userData["email"]),
		fmt.Sprintf("%v", userData["created_at"]),
		fmt.Sprintf("%v", userData["last_login_at"]),
	}
	if err := writer.Write(row); err != nil {
		return "", fmt.Errorf("写入数据失败: %v", err)
	}

	return filePath, nil
}

// exportTransactions 导出交易数据
func exportTransactions(userID uint64, filters string) (string, error) {
	// 示例实现，实际应根据项目需求实现
	exportDir := filepath.Join("assets", "exports", fmt.Sprintf("user_%d", userID))
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return "", fmt.Errorf("创建导出目录失败: %v", err)
	}

	fileName := fmt.Sprintf("transactions_%d_%s.csv", userID, time.Now().Format("20060102_150405"))
	filePath := filepath.Join(exportDir, fileName)

	// 这里应该是实际的交易数据导出逻辑
	log.Printf("导出用户 %d 的交易数据到 %s\n", userID, filePath)

	return filePath, nil
}

// exportActivities 导出活动数据
func exportActivities(userID uint64, filters string) (string, error) {
	// 示例实现，实际应根据项目需求实现
	exportDir := filepath.Join("assets", "exports", fmt.Sprintf("user_%d", userID))
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return "", fmt.Errorf("创建导出目录失败: %v", err)
	}

	fileName := fmt.Sprintf("activities_%d_%s.csv", userID, time.Now().Format("20060102_150405"))
	filePath := filepath.Join(exportDir, fileName)

	// 这里应该是实际的活动数据导出逻辑
	log.Printf("导出用户 %d 的活动数据到 %s\n", userID, filePath)

	return filePath, nil
} 