<template>
  <div class="project-content-edit">
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? '编辑项目内容' : '创建项目内容' }}</h1>
      <el-button @click="$router.push('/project-content')" icon="el-icon-back">返回列表</el-button>
    </div>

    <el-card shadow="hover">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" v-loading="loading">
        <el-form-item label="项目ID" prop="project_id" v-if="!isEdit">
          <el-input v-model.number="form.project_id" placeholder="请输入项目ID"></el-input>
        </el-form-item>
        
        <el-form-item label="域名" prop="domain">
          <el-input v-model="form.domain" placeholder="请输入域名"></el-input>
        </el-form-item>
        
        <el-form-item label="哈希值" prop="hash">
          <el-input v-model="form.hash" placeholder="请输入哈希值"></el-input>
        </el-form-item>
        
        <el-form-item label="状态" prop="state">
          <el-radio-group v-model="form.state">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="发送内容" prop="content">
          <el-input
            type="textarea"
            v-model="form.content"
            :rows="6"
            placeholder="请输入发送内容"></el-input>
        </el-form-item>
        
        <el-form-item label="接收内容" prop="server">
          <el-input
            type="textarea"
            v-model="form.server"
            :rows="6"
            placeholder="请输入接收内容"></el-input>
        </el-form-item>
        
        <el-form-item label="源码文件" prop="source_file">
          <el-input
            type="textarea"
            v-model="form.source_file"
            :rows="6"
            placeholder="请输入源码文件内容"></el-input>
        </el-form-item>
        
        <el-form-item label="屏幕截图" prop="screenshot">
          <el-input v-model="form.screenshot" placeholder="请输入截图URL路径"></el-input>
          <div v-if="form.screenshot" class="screenshot-preview">
            <img :src="form.screenshot" alt="屏幕截图预览" class="preview-image" />
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">{{ isEdit ? '保存修改' : '创建' }}</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { createProjectContent, getProjectContentDetail, updateProjectContent } from '@/api/projectContent'

export default {
  name: 'ProjectContentEdit',
  data() {
    return {
      loading: false,
      isEdit: false,
      contentId: null,
      form: {
        project_id: '',
        domain: '',
        server: '',
        hash: '',
        state: 1,
        content: '',
        received_content: '',
        source_file: '',
        screenshot: ''
      },
      rules: {
        project_id: [
          { required: true, message: '请输入项目ID', trigger: 'blur' },
          { type: 'number', message: '项目ID必须为数字', trigger: 'blur' }
        ],
        domain: [
          { required: true, message: '请输入域名', trigger: 'blur' },
          { max: 255, message: '域名长度不能超过255个字符', trigger: 'blur' }
        ],
        server: [
          { required: true, message: '请输入服务器内容', trigger: 'blur' }
        ],
        hash: [
          { required: true, message: '请输入哈希值', trigger: 'blur' },
          { max: 32, message: '哈希值长度不能超过32个字符', trigger: 'blur' }
        ],
        state: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入发送内容', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 从URL参数中获取项目ID
    const projectId = this.$route.query.project_id
    if (projectId && !this.isEdit) {
      this.form.project_id = parseInt(projectId)
    }
    
    // 判断是否为编辑模式
    const id = this.$route.params.id
    if (id) {
      this.isEdit = true
      this.contentId = parseInt(id)
      this.fetchContentDetail()
    }
  },
  methods: {
    // 获取项目内容详情
    fetchContentDetail() {
      if (!this.contentId) return;
      
      this.loading = true;
      getProjectContentDetail(this.contentId)
        .then(data => {
          // 填充表单
          this.form = {
            project_id: data.project_id,
            domain: data.domain,
            server: data.server,
            hash: data.hash,
            state: data.state,
            content: data.content,
            server: data.server || '',
            source_file: data.source_file || '',
            screenshot: data.screenshot || ''
          };
          
          this.loading = false;
        })
        .catch(error => {
          this.$message.error(`获取内容详情失败: ${error.message}`);
          this.loading = false;
        });
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          return false
        }
        
        this.loading = true
        const actionPromise = this.isEdit
          ? updateProjectContent(this.contentId, this.form)
          : createProjectContent(this.form)
        
        actionPromise
          .then(() => {
            this.$message.success(this.isEdit ? '更新成功' : '创建成功')
            this.loading = false
            // 跳转到列表页
            this.$router.push('/project-content')
          })
          .catch(error => {
            this.$message.error(`${this.isEdit ? '更新' : '创建'}失败: ${error.message}`)
            this.loading = false
          })
      })
    },
    // 重置表单
    resetForm() {
      if (this.isEdit) {
        // 编辑模式下重新获取详情
        this.fetchContentDetail()
      } else {
        // 创建模式下重置为空
        this.$refs.form.resetFields()
      }
    }
  }
}
</script>

<style scoped>
.project-content-edit {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.screenshot-preview {
  margin-top: 10px;
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
</style> 