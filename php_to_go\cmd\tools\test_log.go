package main

import (
	"fmt"
	"log"
	"time"

	"go-fiber-api/database"
	"go-fiber-api/models"

	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Printf("警告: 找不到.env文件: %v", err)
	}

	// 连接数据库
	if err := database.InitDatabase(); err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	log.Println("数据库连接成功")

	// 查询现有的日志记录数
	var count int64
	if err := database.DB.Model(&models.AdminOperationLog{}).Count(&count).Error; err != nil {
		log.Fatalf("查询日志记录数失败: %v", err)
	}
	log.Printf("当前日志记录数: %d", count)

	// 创建一条测试日志记录
	testLog := models.AdminOperationLog{
		UserID:    1,
		Path:      "/api/admin/test",
		Method:    "POST",
		IP:        "127.0.0.1",
		Input:     "{\"test\":\"data\"}",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Duration:  100,
	}

	// 使用事务保存日志
	tx := database.DB.Begin()
	if err := tx.Create(&testLog).Error; err != nil {
		tx.Rollback()
		log.Fatalf("创建测试日志失败: %v", err)
	}
	if err := tx.Commit().Error; err != nil {
		log.Fatalf("提交事务失败: %v", err)
	}

	fmt.Printf("成功创建测试日志，ID: %d\n", testLog.ID)

	// 再次查询日志记录数
	if err := database.DB.Model(&models.AdminOperationLog{}).Count(&count).Error; err != nil {
		log.Fatalf("查询日志记录数失败: %v", err)
	}
	log.Printf("插入后日志记录数: %d", count)

	// 查询并显示最新的日志记录
	var latestLog models.AdminOperationLog
	if err := database.DB.Order("id desc").First(&latestLog).Error; err != nil {
		log.Fatalf("查询最新日志记录失败: %v", err)
	}
	log.Printf("最新日志记录: %+v", latestLog)
} 