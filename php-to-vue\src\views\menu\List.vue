<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>菜单管理</span>
        <el-button
          style="float: right; margin-left: 10px"
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleCreate"
        >
          新建菜单
        </el-button>
      </div>
      
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="queryParams" ref="queryForm" size="small" style="margin-bottom: 20px">
        <el-form-item label="菜单名称" prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="请输入菜单名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 菜单表格 -->
      <el-table
        v-loading="loading"
        :data="menuList"
        row-key="id"
        border
        default-expand-all
        :tree-props="{children: 'children'}"
      >
        <el-table-column prop="title" label="菜单名称" :show-overflow-tooltip="true" />
        <el-table-column prop="icon" label="图标" align="center" width="60">
          <template slot-scope="scope">
            <i :class="scope.row.icon" v-if="scope.row.icon"></i>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="order" label="排序" width="60" align="center" />
        <el-table-column prop="uri" label="路径" :show-overflow-tooltip="true" />
        <el-table-column label="操作" align="center" width="210">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-plus"
              @click="handleAddChild(scope.row)"
              v-if="scope.row.id"
            >添加下级</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getMenuList, deleteMenu } from '@/api/menu'

export default {
  name: 'MenuList',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 菜单表格数据
      menuList: [],
      // 原始树形数据（用于搜索过滤）
      originalMenuTree: [],
      // 查询参数
      queryParams: {
        title: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询菜单列表 */
    getList() {
      this.loading = true
      // 获取菜单列表
      getMenuList({ title: this.queryParams.title }).then(response => {
        this.originalMenuTree = response || []
        this.menuList = this.originalMenuTree
        this.loading = false
      }).catch(error => {
        console.error('获取菜单列表失败:', error)
        this.loading = false
      })
    },
    
    /** 在树形结构中搜索符合条件的节点 */
    filterMenuTree(keyword) {
      if (!keyword) {
        return this.originalMenuTree
      }
      
      // 递归搜索函数
      const searchTree = (nodes, keyword) => {
        const result = []
        
        for (const node of nodes) {
          // 创建节点副本
          const newNode = { ...node }
          
          // 检查当前节点是否匹配
          const isMatch = newNode.title && newNode.title.toLowerCase().includes(keyword.toLowerCase())
          
          // 递归处理子节点
          if (newNode.children && newNode.children.length) {
            newNode.children = searchTree(newNode.children, keyword)
          }
          
          // 当前节点匹配或子节点中有匹配项，则保留
          if (isMatch || (newNode.children && newNode.children.length)) {
            result.push(newNode)
          }
        }
        
        return result
      }
      
      return searchTree(this.originalMenuTree, keyword)
    },
    
    /** 如果搜索无结果，使用列表接口再次搜索 */
    fallbackSearch(keyword) {
      this.loading = true
      getMenuList({ title: keyword }).then(response => {
        // 使用后端的搜索结果
        this.menuList = response || []
        this.loading = false
      }).catch(error => {
        console.error('搜索菜单失败:', error)
        this.loading = false
      })
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.getList()
    },
    
    /** 新增按钮操作 */
    handleCreate() {
      this.$router.push({ path: '/menu/create' })
    },
    
    /** 添加子菜单 */
    handleAddChild(row) {
      this.$router.push({ 
        path: '/menu/create',
        query: { parentId: row.id, parentTitle: row.title }
      })
    },
    
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$router.push({ 
        path: '/menu/edit',
        query: { id: row.id }
      })
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(`确认删除菜单 ${row.title} 吗?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteMenu(row.id).then(() => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(() => {
          this.loading = false
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style> 