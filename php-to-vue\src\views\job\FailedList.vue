<template>
  <div class="failed-job-list">
    <div class="page-header">
      <h1 class="page-title">失败任务管理</h1>
      <div class="page-actions">
        <el-button type="primary" @click="showCreateDialog">创建失败任务</el-button>
        <el-button type="danger" @click="handleClearAll">清空所有失败任务</el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="任务名称">
          <el-input v-model="filterForm.name" placeholder="请输入任务名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchFailedJobs">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 失败任务列表 -->
    <el-card shadow="hover" class="job-table-container">
      <div slot="header">
        <span>失败任务列表</span>
        <el-dropdown style="float: right; cursor: pointer;" @command="handleBatchCommand">
          <span class="el-dropdown-link">
            批量操作 <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="delete">批量删除</el-dropdown-item>
            <el-dropdown-item command="retry">批量重试</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-table
        :data="failedJobList"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="loading">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="任务名称" min-width="150"></el-table-column>
        <el-table-column prop="exception" label="异常信息" min-width="200">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.exception" placement="top">
              <div class="exception-text">{{ scope.row.exception }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="failed_at" label="失败时间" width="180"></el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="viewJobDetail(scope.row)">详情</el-button>
            <el-button size="mini" type="success" @click="retryJob(scope.row)">重试</el-button>
            <el-button size="mini" type="warning" @click="showUpdateDialog(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="deleteJobItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 失败任务详情对话框 -->
    <el-dialog title="失败任务详情" :visible.sync="detailDialogVisible" width="700px">
      <div v-loading="detailLoading">
        <div v-if="currentJob">
          <el-descriptions border :column="1">
            <el-descriptions-item label="ID">{{ currentJob.id }}</el-descriptions-item>
            <el-descriptions-item label="任务名称">{{ currentJob.name }}</el-descriptions-item>
            <el-descriptions-item label="队列">{{ currentJob.queue }}</el-descriptions-item>
            <el-descriptions-item label="失败时间">{{ currentJob.failed_at }}</el-descriptions-item>
            <el-descriptions-item label="异常信息">
              <pre class="error-message">{{ currentJob.exception }}</pre>
            </el-descriptions-item>
            <el-descriptions-item label="任务数据">
              <pre>{{ JSON.stringify(currentJob.payload, null, 2) }}</pre>
            </el-descriptions-item>
          </el-descriptions>
          <div class="dialog-footer" style="margin-top: 20px; text-align: right;">
            <el-button type="success" @click="retryJob(currentJob)">重试此任务</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 创建/编辑失败任务对话框 -->
    <el-dialog :title="dialogType === 'create' ? '创建失败任务' : '编辑失败任务'" :visible.sync="formDialogVisible" width="500px">
      <el-form :model="jobForm" :rules="jobFormRules" ref="jobForm" label-width="100px" v-loading="formLoading">
        <el-form-item label="队列" prop="queue">
          <el-input v-model="jobForm.queue" placeholder="请输入队列名称"></el-input>
        </el-form-item>
        <el-form-item label="连接类型" prop="connection">
          <el-input v-model="jobForm.connection" placeholder="请输入连接类型"></el-input>
        </el-form-item>
        <el-form-item label="异常信息" prop="exception">
          <el-input type="textarea" v-model="jobForm.exception" :rows="4" placeholder="请输入异常信息"></el-input>
        </el-form-item>
        <el-form-item label="任务数据" prop="payload">
          <el-input type="textarea" v-model="jobForm.payload" :rows="6" placeholder="请输入任务数据 (JSON格式)"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitJobForm" :loading="formLoading">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFailedJobs, getFailedJob, createFailedJob, updateFailedJob, deleteFailedJob, retryFailedJob, clearAllFailedJobs } from '@/api/failedJob'

export default {
  name: 'FailedJobList',
  data() {
    return {
      loading: false,
      detailLoading: false,
      formLoading: false,
      // 筛选表单
      filterForm: {
        name: ''
      },
      // 失败任务列表数据
      failedJobList: [],
      // 分页
      pagination: {
        page: 1,
        page_size: 10,
        total: 0
      },
      // 选中的行
      multipleSelection: [],
      // 详情对话框
      detailDialogVisible: false,
      currentJob: null,
      // 表单对话框
      formDialogVisible: false,
      // 对话框类型：create-创建，update-更新
      dialogType: 'create',
      // 任务表单
      jobForm: {
        queue: '',
        connection: 'database',
        exception: '',
        payload: ''
      },
      // 表单验证规则
      jobFormRules: {
        queue: [
          { required: true, message: '请输入队列名称', trigger: 'blur' }
        ],
        connection: [
          { required: true, message: '请输入连接类型', trigger: 'blur' }
        ],
        exception: [
          { required: true, message: '请输入异常信息', trigger: 'blur' }
        ],
        payload: [
          { required: true, message: '请输入任务数据', trigger: 'blur' },
          { validator: this.validateJSON, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchFailedJobList()
  },
  methods: {
    // 获取失败任务列表
    fetchFailedJobList() {
      this.loading = true
      const params = {
        page: this.pagination.page,
        page_size: this.pagination.page_size,
        ...this.filterForm
      }
      
      // 移除值为null或空字符串的参数
      Object.keys(params).forEach(key => {
        if (params[key] === null || params[key] === '') {
          delete params[key]
        }
      })

      getFailedJobs(params)
        .then(response => {
          this.failedJobList = response.items || []
          this.pagination.total = response.total || 0
        })
        .catch(error => {
          this.$message.error(`获取失败任务列表失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 验证JSON格式
    validateJSON(rule, value, callback) {
      if (!value) {
        callback()
        return
      }
      try {
        JSON.parse(value)
        callback()
      } catch (error) {
        callback(new Error('请输入有效的JSON格式'))
      }
    },
    // 显示创建任务对话框
    showCreateDialog() {
      this.dialogType = 'create'
      this.resetJobForm()
      this.formDialogVisible = true
    },
    // 显示更新任务对话框
    showUpdateDialog(row) {
      this.dialogType = 'update'
      this.resetJobForm()
      this.formLoading = true
      
      getFailedJob(row.id)
        .then(data => {
          this.jobForm = {
            id: data.id,
            queue: data.queue || '',
            connection: data.connection || 'database',
            exception: data.exception || '',
            payload: typeof data.payload === 'string' ? data.payload : JSON.stringify(data.payload || {}, null, 2)
          }
        })
        .catch(error => {
          this.$message.error(`获取失败任务详情失败: ${error.message}`)
          this.formDialogVisible = false
        })
        .finally(() => {
          this.formLoading = false
          this.formDialogVisible = true
        })
    },
    // 重置任务表单
    resetJobForm() {
      this.jobForm = {
        queue: '',
        connection: 'database',
        exception: '',
        payload: ''
      }
      // 如果表单已经被创建，则重置验证
      if (this.$refs.jobForm) {
        this.$refs.jobForm.resetFields()
      }
    },
    // 提交任务表单
    submitJobForm() {
      this.$refs.jobForm.validate(valid => {
        if (!valid) {
          return false
        }
        
        this.formLoading = true
        
        // 构建API需要的数据结构
        let formData = {
          connection: this.jobForm.connection,
          queue: this.jobForm.queue,
          exception: this.jobForm.exception,
          payload: this.jobForm.payload // 作为字符串发送
        }
        
        try {
          // 验证payload是否是有效的JSON
          JSON.parse(this.jobForm.payload)
        } catch (error) {
          this.$message.error('任务数据格式错误，请确保是有效的JSON格式')
          this.formLoading = false
          return
        }
        
        // 根据对话框类型决定是创建还是更新
        let apiCall = this.dialogType === 'create' 
          ? createFailedJob(formData)
          : updateFailedJob(this.jobForm.id, formData)
        
        apiCall
          .then(() => {
            this.$message.success(this.dialogType === 'create' ? '创建成功' : '更新成功')
            this.formDialogVisible = false
            this.fetchFailedJobList()
          })
          .catch(error => {
            this.$message.error(`${this.dialogType === 'create' ? '创建' : '更新'}失败: ${error.message}`)
          })
          .finally(() => {
            this.formLoading = false
          })
      })
    },
    // 搜索失败任务
    searchFailedJobs() {
      this.pagination.page = 1
      this.fetchFailedJobList()
    },
    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        name: ''
      }
      this.searchFailedJobs()
    },
    // 批量操作
    handleBatchCommand(command) {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择失败任务')
        return
      }
      
      if (command === 'delete') {
        this.$confirm('确认删除选中的失败任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.loading = true
          const deletePromises = this.multipleSelection.map(item => deleteFailedJob(item.id))
          Promise.all(deletePromises)
            .then(() => {
              this.$message.success('批量删除成功')
              this.fetchFailedJobList()
            })
            .catch(error => {
              this.$message.error(`批量删除失败: ${error.message}`)
            })
            .finally(() => {
              this.loading = false
            })
        }).catch(() => {
          // 取消删除
        })
      } else if (command === 'retry') {
        this.$confirm('确认重试选中的失败任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.loading = true
          const retryPromises = this.multipleSelection.map(item => retryFailedJob(item.id))
          Promise.all(retryPromises)
            .then(() => {
              this.$message.success('批量重试成功')
              this.fetchFailedJobList()
            })
            .catch(error => {
              this.$message.error(`批量重试失败: ${error.message}`)
            })
            .finally(() => {
              this.loading = false
            })
        }).catch(() => {
          // 取消操作
        })
      }
    },
    // 选择行变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.page_size = val
      this.fetchFailedJobList()
    },
    // 页码变化
    handleCurrentChange(val) {
      this.pagination.page = val
      this.fetchFailedJobList()
    },
    // 查看任务详情
    viewJobDetail(row) {
      this.detailDialogVisible = true
      this.detailLoading = true
      this.currentJob = null
      
      getFailedJob(row.id)
        .then(data => {
          this.currentJob = data
        })
        .catch(error => {
          this.$message.error(`获取失败任务详情失败: ${error.message}`)
        })
        .finally(() => {
          this.detailLoading = false
        })
    },
    // 重试失败任务
    retryJob(row) {
      this.loading = true
      retryFailedJob(row.id)
        .then(() => {
          this.$message.success('重试成功')
          if (this.detailDialogVisible) {
            this.detailDialogVisible = false
          }
          this.fetchFailedJobList()
        })
        .catch(error => {
          this.$message.error(`重试失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 删除失败任务
    deleteJobItem(row) {
      this.$confirm(`确认删除失败任务 "${row.name || row.id}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteFailedJob(row.id)
          .then(() => {
            this.$message.success('删除成功')
            this.fetchFailedJobList()
          })
          .catch(error => {
            this.$message.error(`删除失败: ${error.message}`)
          })
          .finally(() => {
            this.loading = false
          })
      }).catch(() => {
        // 取消删除
      })
    },
    // 清空所有失败任务
    handleClearAll() {
      this.$confirm('确认清空所有失败任务吗？此操作不可恢复！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        clearAllFailedJobs()
          .then(() => {
            this.$message.success('清空成功')
            this.fetchFailedJobList()
          })
          .catch(error => {
            this.$message.error(`清空失败: ${error.message}`)
          })
          .finally(() => {
            this.loading = false
          })
      }).catch(() => {
        // 取消操作
      })
    }
  }
}
</script>

<style scoped>
.failed-job-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.job-table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-dropdown-link {
  color: #409EFF;
  cursor: pointer;
}

.exception-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.error-message {
  background-color: #fef0f0;
  color: #f56c6c;
}
</style> 