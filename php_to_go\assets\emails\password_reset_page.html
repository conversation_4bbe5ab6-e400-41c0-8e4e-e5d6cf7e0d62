<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码</title>
    <style>
        :root {
            --primary-color: #4285f4;
            --primary-dark: #3367d6;
            --error-color: #f44336;
            --success-color: #4caf50;
            --border-color: #e0e0e0;
            --text-color: #333;
            --text-secondary: #666;
            --bg-color: #f5f7fa;
            --card-bg: #fff;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            width: 100%;
            max-width: 450px;
            border-radius: 10px;
            padding: 30px;
            background-color: var(--card-bg);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        h2 {
            color: var(--text-color);
            font-weight: 600;
            font-size: 26px;
            margin-bottom: 10px;
        }

        .subtitle {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 14px;
        }

        input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 16px;
            transition: border 0.3s ease, box-shadow 0.3s ease;
        }

        input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.2);
        }

        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            transition: background-color 0.3s ease, transform 0.1s ease;
        }

        button:hover {
            background-color: var(--primary-dark);
        }
        
        button:active {
            transform: translateY(1px);
        }

        .message {
            padding: 12px;
            border-radius: 6px;
            margin-top: 15px;
            font-size: 14px;
            display: none;
        }

        .error {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(244, 67, 54, 0.3);
        }

        .success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .password-requirements {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 6px;
        }

        @media only screen and (max-width: 480px) {
            .container {
                padding: 20px;
            }
            
            h2 {
                font-size: 22px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h2>重置密码</h2>
            <div class="subtitle">请设置您的新密码</div>
        </div>
        
        <div class="form-group">
            <label for="password">新密码</label>
            <input type="password" id="password" placeholder="请输入新密码">
            <div class="password-requirements">密码长度至少为6个字符</div>
        </div>
        <div class="form-group">
            <label for="confirm-password">确认密码</label>
            <input type="password" id="confirm-password" placeholder="请再次输入新密码">
        </div>
        
        <button id="submit-btn">重置密码</button>
        
        <div id="error-message" class="message error"></div>
        <div id="success-message" class="message success"></div>
    </div>

    <script>
        document.getElementById('submit-btn').addEventListener('click', function () {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const errorMessage = document.getElementById('error-message');
            const successMessage = document.getElementById('success-message');

            // 隐藏所有消息
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            // 验证密码
            if (!password) {
                errorMessage.textContent = '请输入新密码';
                errorMessage.style.display = 'block';
                return;
            }

            if (password.length < 6) {
                errorMessage.textContent = '密码长度至少为6个字符';
                errorMessage.style.display = 'block';
                return;
            }

            if (password !== confirmPassword) {
                errorMessage.textContent = '两次输入的密码不一致';
                errorMessage.style.display = 'block';
                return;
            }

            // 发送重置请求
            fetch('/api/admin/auth/reset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token: '{{.Token}}',
                    new_password: password
                }),
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        successMessage.textContent = data.message || '密码重置成功，请返回登录页面';
                        successMessage.style.display = 'block';
                        // 清空表单
                        document.getElementById('password').value = '';
                        document.getElementById('confirm-password').value = '';
                    } else {
                        errorMessage.textContent = data.message || '密码重置失败，请重试';
                        errorMessage.style.display = 'block';
                    }
                })
                .catch(error => {
                    errorMessage.textContent = '网络错误，请重试';
                    errorMessage.style.display = 'block';
                    console.error('Error:', error);
                });
        });
    </script>
</body>

</html>