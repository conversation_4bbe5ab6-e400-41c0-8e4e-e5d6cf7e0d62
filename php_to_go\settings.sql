/*
 Navicat Premium Data Transfer

 Source Server         : 1
 Source Server Type    : MySQL
 Source Server Version : 80034
 Source Host           : localhost:3306
 Source Schema         : go_fiber

 Target Server Type    : MySQL
 Target Server Version : 80034
 File Encoding         : 65001

 Date: 23/06/2025 23:04:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for settings
-- ----------------------------
DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设置键名',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '设置值',
  `created_at` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_settings_key`(`key`) USING BTREE,
  INDEX `idx_settings_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of settings
-- ----------------------------
INSERT INTO `settings` VALUES (1, 'site_name', 'Go Fiber API', '2025-06-07 16:10:04.412', '2025-06-07 16:22:19.100', NULL);
INSERT INTO `settings` VALUES (2, 'site_keywords', 'Go,Fiber,API', '2025-06-07 16:10:04.424', '2025-06-07 16:22:19.127', NULL);
INSERT INTO `settings` VALUES (3, 'site_description', 'Go Fiber API是一个基于Fiber框架的API服务asdfasdfasdfasdf', '2025-06-07 16:10:04.441', '2025-06-07 16:22:19.139', NULL);
INSERT INTO `settings` VALUES (4, 'site_logo', '', '2025-06-07 16:10:04.468', '2025-06-07 16:22:19.157', NULL);
INSERT INTO `settings` VALUES (5, 'site_favicon', '', '2025-06-07 16:10:04.481', '2025-06-07 16:22:19.165', NULL);
INSERT INTO `settings` VALUES (6, 'site_footer', 'Copyright © 2023 Go Fiber API', '2025-06-07 16:10:04.498', '2025-06-07 16:22:19.176', NULL);
INSERT INTO `settings` VALUES (7, 'site_notice', 'sdfasdf', '2025-06-07 16:10:04.516', '2025-06-07 16:22:19.189', NULL);
INSERT INTO `settings` VALUES (8, 'site_closed', 'false', '2025-06-07 16:10:04.523', '2025-06-07 16:22:19.197', NULL);
INSERT INTO `settings` VALUES (9, 'closed_message', '网asdfasdfasdfasdfasdfas站维护中，请稍后再试', '2025-06-07 16:10:04.535', '2025-06-07 16:22:19.213', NULL);
INSERT INTO `settings` VALUES (10, 'register_closed', 'false', '2025-06-07 16:10:04.542', '2025-06-07 16:22:19.233', NULL);
INSERT INTO `settings` VALUES (11, 'mail_host', '', '2025-06-07 16:10:04.561', '2025-06-07 16:22:19.253', NULL);
INSERT INTO `settings` VALUES (12, 'mail_port', '587', '2025-06-07 16:10:04.566', '2025-06-07 16:22:19.281', NULL);
INSERT INTO `settings` VALUES (13, 'mail_username', '', '2025-06-07 16:10:04.576', '2025-06-07 16:22:19.299', NULL);
INSERT INTO `settings` VALUES (14, 'mail_password', '', '2025-06-07 16:10:04.583', '2025-06-07 16:22:19.316', NULL);
INSERT INTO `settings` VALUES (15, 'mail_from_name', 'Go Fiber API', '2025-06-07 16:10:04.591', '2025-06-07 16:22:19.331', NULL);
INSERT INTO `settings` VALUES (16, 'mail_from_email', '', '2025-06-07 16:10:04.600', '2025-06-07 16:22:19.345', NULL);

-- 邀请码功能相关配置
INSERT INTO `settings` VALUES (17, 'invitation_required', 'true', '2025-06-23 23:10:00.000', '2025-06-23 23:10:00.000', NULL);
INSERT INTO `settings` VALUES (18, 'invitation_code_length', '8', '2025-06-23 23:10:00.000', '2025-06-23 23:10:00.000', NULL);
INSERT INTO `settings` VALUES (19, 'invitation_code_expire_days', '30', '2025-06-23 23:10:00.000', '2025-06-23 23:10:00.000', NULL);
INSERT INTO `settings` VALUES (20, 'invitation_code_max_uses', '1', '2025-06-23 23:10:00.000', '2025-06-23 23:10:00.000', NULL);

-- 邮箱验证相关配置
INSERT INTO `settings` VALUES (21, 'email_verification_required', 'false', '2025-06-23 23:10:00.000', '2025-06-23 23:10:00.000', NULL);
INSERT INTO `settings` VALUES (22, 'email_verification_expire_minutes', '30', '2025-06-23 23:10:00.000', '2025-06-23 23:10:00.000', NULL);
INSERT INTO `settings` VALUES (23, 'password_reset_expire_minutes', '60', '2025-06-23 23:10:00.000', '2025-06-23 23:10:00.000', NULL);

SET FOREIGN_KEY_CHECKS = 1;
