<template>
  <div class="project-list">
    <div class="page-header">
      <h1 class="page-title">项目管理</h1>
      <div class="page-actions">
        <el-button type="primary" @click="$router.push('/project/create')">
          <i class="el-icon-plus"></i> 创建项目
        </el-button>
        <el-button type="success" @click="$router.push('/project-content')">
          <i class="el-icon-document-copy"></i> 查看项目内容
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="项目名称">
          <el-input
            v-model="filterForm.title"
            placeholder="请输入项目名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="filterForm.state"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchProjects">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 项目列表 -->
    <el-card shadow="hover" class="project-table-container">
      <div slot="header">
        <span>项目列表</span>
        <el-dropdown
          style="float: right; cursor: pointer"
          @command="handleBatchCommand"
        >
          <span class="el-dropdown-link">
            批量操作 <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="delete">批量删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-table
        :data="projects"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column label="项目" >
          <template slot-scope="scope">
            <div style="display: flex; align-items: center">
              <span>{{ scope.row.title }}</span>
              <el-badge
                v-if="scope.row.is_new_record"
                value="New"
                type="danger"
                class="item"
              ></el-badge>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="项目名称"></el-table-column>
        <el-table-column prop="description" label="项目描述"></el-table-column>
        <el-table-column prop="unique_key" label="唯一标识"></el-table-column>
        <el-table-column prop="created_at" label="创建时间"></el-table-column>
        <el-table-column prop="state" width="150" label="状态">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.state"
              :active-value="1"
              :inactive-value="0"
              @change="toggleProjectState(scope.row)"
              :loading="scope.row.stateLoading"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="content_count" label="内容数" width="100">
          <template slot-scope="scope">
            <el-badge
              :value="scope.row.content_count || 0"
              class="content-badge"
            >
              <el-button
                size="mini"
                type="text"
                @click="goToContents(scope.row)"
              >
                查看内容
              </el-button>
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="viewProject(scope.row)"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="editProject(scope.row)"
              >编辑</el-button
            >
            <el-button
              v-if="!scope.row.deleted"
              size="mini"
              type="danger"
              @click="deleteProject(scope.row)"
              >删除</el-button
            >
            <el-button
              v-else
              size="mini"
              type="success"
              @click="restoreProject(scope.row)"
              >恢复</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  getProjectList,
  deleteProject,
  previewProject,
  getProjectCode,
  toggleProjectState,
  updateProject,
  restoreProject as restoreProjectAPI
} from "@/api/project";

export default {
  name: "ProjectList",
  data() {
    return {
      loading: false,
      // 筛选表单
      filterForm: {
        title: "",
        state: null,
      },
      // 项目列表数据
      projects: [],
      // 分页
      pagination: {
        page: 1,
        page_size: 10,
        total: 0,
      },
      // 选中的行
      multipleSelection: [],
    };
  },
  methods: {
    // 项目相关方法
    searchProjects() {
      this.pagination.page = 1;
      this.fetchProjects();
    },

    resetFilter() {
      this.filterForm = {
        title: "",
        state: null,
      };
      this.searchProjects();
    },

    handleBatchCommand(command) {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请先选择项目");
        return;
      }
      if (command === "delete") {
        this.$confirm("确认删除选中的项目吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            // 批量删除的逻辑实现
            const deletePromises = this.multipleSelection.map((item) =>
              deleteProject(item.id)
            );
            Promise.all(deletePromises)
              .then(() => {
                this.$message.success("批量删除成功");
                this.fetchProjects();
              })
              .catch((error) => {
                this.$message.error(`批量删除失败: ${error.message}`);
              });
          })
          .catch(() => {
            // 取消删除
          });
      } else if (command === "export") {
        this.$message.success("开始导出数据，请稍候...");
        // 导出数据逻辑
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    handleSizeChange(val) {
      this.pagination.page_size = val;
      this.fetchProjects();
    },

    handleCurrentChange(val) {
      this.pagination.page = val;
      this.fetchProjects();
    },

    viewProject(row) {
      this.$router.push(`/project/${row.id}`);
    },

    editProject(row) {
      this.$router.push(`/project/create?id=${row.id}`);
    },

    deleteProject(row) {
      this.$confirm(`确认删除项目 "${row.title}" 吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          deleteProject(row.id)
            .then(() => {
              this.$message.success("删除成功");
              this.fetchProjects();
            })
            .catch((error) => {
              this.$message.error(`删除失败: ${error.message}`);
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          // 取消删除
        });
    },

    previewProject(row) {
      this.loading = true;
      previewProject(row.id)
        .then(() => {
          this.$message.success("预览项目成功，请查看生成的链接");
          this.loading = false;
        })
        .catch((error) => {
          this.$message.error(`预览失败: ${error.message}`);
          this.loading = false;
        });
    },

    fetchProjects() {
      this.loading = true;
      const params = {
        page: this.pagination.page,
        page_size: this.pagination.page_size,
        with_deleted: true,
        ...this.filterForm,
      };

      // 移除值为null或空字符串的参数
      Object.keys(params).forEach((key) => {
        if (params[key] === null || params[key] === "") {
          delete params[key];
        }
      });

      getProjectList(params)
        .then((response) => {
          this.projects = response.items || [];
          this.pagination.total = response.total || 0;
        })
        .catch((error) => {
          this.$message.error(`获取项目列表失败: ${error.message}`);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 切换项目状态
    toggleProjectState(row) {
      // 设置加载状态
      this.$set(row, "stateLoading", true);

      // 调用API切换状态
      toggleProjectState(row.id)
        .then((response) => {
          const newState = response.state;
          this.$message.success(
            `项目状态已${newState === 1 ? "启用" : "禁用"}`
          );
          // 更新本地状态
          row.state = newState;
        })
        .catch((error) => {
          this.$message.error(`状态更新失败: ${error.message}`);
          // 恢复之前的状态
          row.state = row.state === 1 ? 0 : 1;
        })
        .finally(() => {
          this.$set(row, "stateLoading", false);
        });
    },

    // 跳转到项目内容页面
    goToContents(project) {
      // 更新项目状态 is_new_record = false
      updateProject(project.id, { is_new_record: false });
      this.$router.push({
        path: "/project-content",
        query: { project_id: project.id },
      });
    },

    // 恢复项目
    restoreProject(row) {
      this.$confirm(`确认恢复项目 "${row.title}" 吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      })
        .then(() => {
          this.loading = true;
          restoreProjectAPI(row.id)
            .then(() => {
              this.$message.success("恢复成功");
              this.fetchProjects();
            })
            .catch((error) => {
              this.$message.error(`恢复失败: ${error.message}`);
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          // 取消恢复
        });
    },
  },
  created() {
    this.fetchProjects();
  },
};
</script>

<style scoped>
.project-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.project-table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-dropdown-link {
  color: #409eff;
}

.code-preview-dialog .el-dialog__body {
  padding: 10px 20px;
  max-height: 600px;
  overflow: auto;
  font-family: "Courier New", Courier, monospace;
  white-space: pre-wrap;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.content-badge {
  margin-top: 10px;
}

.content-badge .el-badge__content {
  background-color: #409eff;
}
</style>
