package models

import (
	"time"
)

// AdminOperationLog 管理员操作日志模型
type AdminOperationLog struct {
	ID        uint64    `json:"id" gorm:"primaryKey;type:bigint unsigned"`
	UserID    uint64    `json:"user_id" gorm:"index:admin_operation_log_user_id_index;type:bigint unsigned;not null"`
	Path      string    `json:"path" gorm:"not null;size:255"`
	Method    string    `json:"method" gorm:"not null;size:10"`
	IP        string    `json:"ip" gorm:"not null;size:255"`
	Input     string    `json:"input" gorm:"type:text;not null"`
	Duration  int64     `json:"duration" gorm:"type:int;default:0;comment:请求处理时长(毫秒)"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	// 关联用户信息，使用软关联而不是外键约束
	User *AdminUser `json:"user" gorm:"foreignKey:UserID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL;-:migration"`
}

// TableName 指定表名
func (AdminOperationLog) TableName() string {
	return "admin_operation_log"
} 