/*
 Navicat Premium Data Transfer

 Source Server         : 1
 Source Server Type    : MySQL
 Source Server Version : 80034
 Source Host           : localhost:3306
 Source Schema         : go_fiber

 Target Server Type    : MySQL
 Target Server Version : 80034
 File Encoding         : 65001

 Date: 25/06/2025 01:40:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_roles
-- ----------------------------
DROP TABLE IF EXISTS `admin_roles`;
CREATE TABLE `admin_roles`  (
  `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色标识',
  `created_at` datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
  `guard_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'admin' COMMENT 'Guard',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `deleted_at` datetime(3) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `admin_roles_slug_unique`(`slug`) USING BTREE,
  UNIQUE INDEX `idx_role_name_guard`(`name`, `guard_name`) USING BTREE,
  INDEX `idx_admin_roles_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of admin_roles
-- ----------------------------
INSERT INTO `admin_roles` VALUES (1, 'Administrator', 'administrator', '2020-08-03 16:16:14.000', '2025-06-25 01:31:21.228', 'admin', '', NULL);
INSERT INTO `admin_roles` VALUES (2, '用户', 'user', '2020-08-27 16:55:53.000', '2025-06-24 22:18:47.317', 'admin', '', NULL);
INSERT INTO `admin_roles` VALUES (3, '收费会员', 'vipuser', '2021-07-12 09:44:01.000', '2021-07-12 09:44:01.000', 'admin', NULL, NULL);
INSERT INTO `admin_roles` VALUES (4, 'asdfs', '', '2025-06-24 22:02:49.752', '2025-06-24 22:07:52.698', 'admin', 'asdfsdasdfsdfa', '2025-06-24 22:14:07.256');

SET FOREIGN_KEY_CHECKS = 1;
