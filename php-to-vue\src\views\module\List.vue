<template>
  <div class="module-list">
    <div class="page-header">
      <h1 class="page-title">模块管理</h1>
      <el-button type="primary" @click="$router.push('/module/create')">
        <i class="el-icon-plus"></i> 创建模块
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="模块名称">
          <el-input v-model="filterForm.title" placeholder="请输入模块名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="共享状态">
          <el-select v-model="filterForm.is_share" placeholder="全部" clearable>
            <el-option label="非公开" :value="0"></el-option>
            <el-option label="公开" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="安全级别">
          <el-select v-model="filterForm.level" placeholder="全部" clearable>
            <el-option label="默认 (1级)" value="1"></el-option>
            <el-option label="1级" value="1"></el-option>
            <el-option label="2级" value="2"></el-option>
            <el-option label="3级" value="3"></el-option>
            <el-option label="4级" value="4"></el-option>
            <el-option label="5级" value="5"></el-option>
            <el-option label="6级" value="6"></el-option>
            <el-option label="7级" value="7"></el-option>
            <el-option label="8级" value="8"></el-option>
            <el-option label="9级" value="9"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchModules">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 模块列表 -->
    <el-card shadow="hover" class="module-table-container">
      <div slot="header">
        <span>模块列表</span>
      </div>
      <el-table
        :data="modules"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="loading"
        border
        :max-height="600"
        :table-layout="'fixed'"
        :row-key="(row) => row.id">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="title" label="模块名称" width="160">
          <template slot-scope="scope">
            <el-tooltip effect="dark" :content="scope.row.title" placement="top" :disabled="!scope.row.title || scope.row.title.length < 20">
              <div class="truncated-text">{{ scope.row.title }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="模块描述" min-width="200">
          <template slot-scope="scope">
            <el-tooltip effect="dark" :content="scope.row.description" placement="top" :disabled="!scope.row.description || scope.row.description.length < 20">
              <div class="truncated-text">{{ scope.row.description }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="安全级别" width="80">
          <template slot-scope="scope">
            <el-tag :type="getSecurityLevelType(scope.row.level)">
              {{ getLevelText(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_share" label="共享状态" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.is_share"
              :active-value="1"
              :inactive-value="0"
              @change="toggleModuleShareState(scope.row)"
              :loading="scope.row.shareStateLoading">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="state" label="状态" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.state"
              :active-value="1"
              :inactive-value="0"
              @change="toggleModuleState(scope.row)"
              :loading="scope.row.stateLoading">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="viewModule(scope.row)">查看</el-button>
            <el-button size="mini" type="warning" @click="editModule(scope.row)">编辑</el-button>
            <el-button v-if="!scope.row.deleted" size="mini" type="danger" @click="deleteModule(scope.row)">删除</el-button>
            <el-button v-else size="mini" type="success" @click="restoreModule(scope.row)">恢复</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getModuleList, deleteModule, updateModule, restoreModule as restoreModuleAPI } from '@/api/module'

export default {
  name: 'ModuleList',
  data() {
    return {
      loading: false,
      // 筛选表单
      filterForm: {
        title: '',
        is_share: '',
        level: ''
      },
      // 模块列表数据
      modules: [],
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 选中的行
      multipleSelection: []
    }
  },
  methods: {
    // 获取安全级别对应的类型
    getSecurityLevelType(level) {
      if (level === undefined || level === null) return 'info';
      
      // 将0-9的安全级别映射到不同类型
      const levelNumber = Number(level);
      if (levelNumber === 0) return 'info'; // 默认级别
      if (levelNumber >= 1 && levelNumber <= 3) return 'success'; // 低级别
      if (levelNumber >= 4 && levelNumber <= 6) return 'warning'; // 中级别
      if (levelNumber >= 7 && levelNumber <= 9) return 'danger'; // 高级别
      
      return 'info'; // 默认返回info类型
    },
    // 获取安全级别文本
    getLevelText(level) {
      if (level === undefined || level === null) return '默认';
      
      // 直接返回数字级别
      return `${level}级`;
    },
    // 搜索模块
    searchModules() {
      this.pagination.currentPage = 1
      this.fetchModules()
    },
    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        title: '',
        is_share: '',
        level: ''
      }
      this.searchModules()
    },
    // 选择行变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.fetchModules()
    },
    // 页码变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.fetchModules()
    },
    // 查看模块详情
    viewModule(row) {
      this.$router.push(`/module/${row.id}`)
    },
    // 编辑模块
    editModule(row) {
      this.$router.push(`/module/create?id=${row.id}`)
    },
    // 删除模块
    deleteModule(row) {
      this.$confirm(`确认删除模块 "${row.title}" 吗？删除后不可恢复！`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteModule(row.id).then(() => {
          this.$message.success('删除成功')
          this.fetchModules()
        }).catch(error => {
          this.$message.error(`删除失败: ${error.message || '未知错误'}`)
        }).finally(() => {
          this.loading = false
        })
      }).catch(() => {
        // 取消删除
      })
    },
    // 获取模块列表
    fetchModules() {
      this.loading = true
      
      const params = {
        page: this.pagination.currentPage,
        page_size: this.pagination.pageSize,
        with_deleted: true, // 增加参数，包括已删除的记录
      }
      
      // 添加筛选条件
      if (this.filterForm.title) {
        params.title = this.filterForm.title
      }
      
      if (this.filterForm.level !== '') {
        params.level = this.filterForm.level
      }
      
      if (this.filterForm.is_share !== '') {
        params.is_share = this.filterForm.is_share
      }
      
      getModuleList(params).then(response => {
        this.modules = response.items || []
        this.pagination.total = response.total || 0
      }).catch(error => {
        this.$message.error(`获取模块列表失败: ${error.message || '未知错误'}`)
      }).finally(() => {
        this.loading = false
      })
    },
    // 切换模块状态
    toggleModuleState(row) {
      // 设置加载状态
      this.$set(row, 'stateLoading', true)
      
      // 调用API更新状态
      updateModule(row.id, { state: row.state })
        .then(() => {
          this.$message.success(`模块状态已${row.state === 1 ? '启用' : '禁用'}`)
        })
        .catch(error => {
          this.$message.error(`状态更新失败: ${error.message}`)
          // 恢复之前的状态
          row.state = row.state === 1 ? 0 : 1
        })
        .finally(() => {
          this.$set(row, 'stateLoading', false)
        })
    },
    // 切换模块共享状态
    toggleModuleShareState(row) {
      // 设置加载状态
      this.$set(row, 'shareStateLoading', true)
      
      // 调用API更新共享状态
      updateModule(row.id, { is_share: row.is_share })
        .then(() => {
          this.$message.success(`模块共享状态已设为${row.is_share === 20 ? '公开' : '非公开'}`)
        })
        .catch(error => {
          this.$message.error(`共享状态更新失败: ${error.message}`)
          // 恢复之前的状态
          row.is_share = row.is_share === 20 ? 10 : 20
        })
        .finally(() => {
          this.$set(row, 'shareStateLoading', false)
        })
    },
    // 恢复模块
    restoreModule(row) {
      this.$confirm(`确认恢复模块 "${row.title}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.loading = true
        restoreModuleAPI(row.id).then(() => {
          this.$message.success('恢复成功')
          this.fetchModules()
        }).catch(error => {
          this.$message.error(`恢复失败: ${error.message || '未知错误'}`)
        }).finally(() => {
          this.loading = false
        })
      }).catch(() => {
        // 取消恢复
      })
    },
  },
  created() {
    this.fetchModules()
  }
}
</script>

<style scoped>
.module-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.module-table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.truncated-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
</style> 