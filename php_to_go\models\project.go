package models

import (
	"time"

	"gorm.io/gorm"
)

// Project 项目模型
type Project struct {
	ID            uint64         `json:"id" gorm:"primaryKey;type:int unsigned"`
	Title         string         `json:"title" gorm:"not null;size:255;default:'';comment:项目名称"`
	UniqueKey     string         `json:"unique_key" gorm:"not null;size:20;default:'';comment:唯一标记"`
	Description   string         `json:"description" gorm:"size:255;default:'';comment:项目描述"`
	Code          string         `json:"code" gorm:"type:mediumtext;comment:代码"`
	UserID        uint64         `json:"user_id" gorm:"not null;default:0;comment:用户ID"`
	ModuleID      string         `json:"module_id" gorm:"not null;size:255;default:'0';comment:模型ID"`
	ModuleExtParam string        `json:"module_ext_param" gorm:"type:mediumtext;comment:模型扩展字段"`
	IsNewRecord   bool           `json:"is_new_record" gorm:"type:tinyint(1);not null;default:0;comment:是否有新纪录"`
	State         int8           `json:"state" gorm:"type:tinyint(1);not null;default:0;comment:状态:0 无效;1 启用"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Project) TableName() string {
	return "projects"
} 