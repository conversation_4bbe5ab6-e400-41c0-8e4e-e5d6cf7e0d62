package middleware

import (
	"go-fiber-api/database"
	"log"
	"sync"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/websocket/v2"
	"github.com/google/uuid"
)

// 存储所有WebSocket连接
var (
	clients      = make(map[string]*websocket.Conn)
	clientsMutex = sync.RWMutex{}
)

// WebSocketHandler WebSocket处理器
func WebSocketHandler(c *websocket.Conn) {
	// 生成客户端ID
	clientID := uuid.New().String()

	// 将连接添加到客户端集合
	clientsMutex.Lock()
	clients[clientID] = c
	clientsMutex.Unlock()

	log.Printf("WebSocket客户端连接: %s, 当前连接数: %d", clientID, len(clients))

	// 在函数返回时移除客户端
	defer func() {
		clientsMutex.Lock()
		delete(clients, clientID)
		clientsMutex.Unlock()
		log.Printf("WebSocket客户端断开: %s, 当前连接数: %d", clientID, len(clients))
	}()

	// 处理消息
	for {
		messageType, message, err := c.ReadMessage()
		if err != nil {
			if websocket.IsCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				// 正常关闭
				break
			}
			log.Printf("读取消息错误: %v", err)
			break
		}

		// 简单地回显消息（实际应用中可能需要处理特定命令）
		log.Printf("收到消息: %s", message)
		if err := c.WriteMessage(messageType, message); err != nil {
			log.Printf("写入消息错误: %v", err)
			break
		}
	}
}

// WebSocketUpgrade WebSocket升级中间件
func WebSocketUpgrade() fiber.Handler {
	return websocket.New(WebSocketHandler, websocket.Config{
		EnableCompression: true,
	})
}

// BroadcastMessage 向所有连接的客户端广播消息
func BroadcastMessage(message []byte) {
	clientsMutex.RLock()
	defer clientsMutex.RUnlock()

	for clientID, client := range clients {
		if err := client.WriteMessage(websocket.TextMessage, message); err != nil {
			log.Printf("广播到客户端 %s 失败: %v", clientID, err)
		}
	}
}

// StartRedisSubscriber 启动Redis订阅，监听通知并广播到WebSocket客户端
func StartRedisSubscriber() {
	// 订阅Redis通知频道
	pubsub := database.Rdb.Subscribe(database.Rdb.Context(), "notifications")
	defer pubsub.Close()

	// 处理订阅消息
	channel := pubsub.Channel()
	for msg := range channel {
		log.Printf("收到Redis通知: %s", msg.Payload)
		// 将消息广播到所有WebSocket客户端
		BroadcastMessage([]byte(msg.Payload))
	}
}
