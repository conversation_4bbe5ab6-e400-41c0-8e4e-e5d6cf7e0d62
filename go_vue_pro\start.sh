#!/bin/bash

# Go Vue Pro 项目一键启动脚本
# Linux/WSL 版本

echo "=== Go Vue Pro 项目一键启动 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查 Docker 是否安装
echo -e "${YELLOW}检查 Docker 状态...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}✗ Docker 未安装${NC}"
    echo "请先安装 Docker"
    exit 1
fi

# 检查 Docker 是否运行
if ! docker info &> /dev/null; then
    echo -e "${RED}✗ Docker 未运行${NC}"
    echo "请启动 Docker 服务"
    echo "在 WSL 中，您可能需要启动 Windows 上的 Docker Desktop"
    exit 1
fi

echo -e "${GREEN}✓ Docker 正在运行${NC}"

# 检查 Docker Compose 是否可用
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${RED}✗ Docker Compose 不可用${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Docker Compose 可用${NC}"

# 进入 docker 目录
cd docker || {
    echo -e "${RED}错误: docker 目录不存在${NC}"
    exit 1
}

# 检查是否有运行中的容器
echo -e "${YELLOW}检查现有容器...${NC}"
if docker compose ps -q | grep -q .; then
    echo -e "${YELLOW}发现运行中的容器，正在停止...${NC}"
    docker compose down
    sleep 3
fi

# 启动所有服务
echo -e "${YELLOW}启动所有服务...${NC}"
docker compose up -d

if [ $? -ne 0 ]; then
    docker pull alpine:latest
    docker pull golang:1.24-alpine
    docker pull node:18.18.0-alpine
    docker pull nginx:latest
    echo -e "${RED}✗ 服务启动失败${NC}"
    echo "请检查 Docker 配置和日志"
    exit 1
fi

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 15

# 检查容器状态
echo -e "${YELLOW}检查容器状态...${NC}"
docker compose ps

# 等待服务完全就绪
echo -e "${YELLOW}等待服务完全就绪...${NC}"
max_wait=60
waited=0
all_ready=false

while [ $waited -lt $max_wait ] && [ "$all_ready" = false ]; do
    ready=0
    
    # 检查前端
    if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200"; then
        ready=$((ready + 1))
    fi
    
    # 检查API健康检查
    if curl -s -o /dev/null -w "%{http_code}" http://localhost/health | grep -q "200"; then
        ready=$((ready + 1))
    fi
    
    if [ $ready -eq 2 ]; then
        all_ready=true
        break
    fi
    
    echo -e "${YELLOW}等待服务就绪... ($waited/$max_wait 秒)${NC}"
    sleep 5
    waited=$((waited + 5))
done

if [ "$all_ready" = true ]; then
    echo -e "\n${GREEN}🎉 所有服务启动成功！${NC}"
    
    # 显示访问信息
    echo -e "\n${CYAN}=== 服务访问地址 ===${NC}"
    echo "前端应用: http://localhost"
    echo "API 接口: http://localhost/api/"
    echo "API 文档: http://localhost/swagger/"
    echo "队列监控: http://localhost/asynq/"
    
    # 在WSL中，提示用户在Windows浏览器中访问
    echo -e "\n${CYAN}💡 提示: 在 WSL 环境中，请在 Windows 浏览器中访问上述地址${NC}"
    
else
    echo -e "\n${YELLOW}⚠️  服务启动可能有问题，请检查日志${NC}"
    echo "运行以下命令查看详细日志:"
    echo "docker compose logs"
fi

echo -e "\n${CYAN}=== 常用命令 ===${NC}"
echo "查看日志: docker compose logs"
echo "停止服务: docker compose down"
echo "重启服务: docker compose restart"
echo "验证服务: ./verify.sh"

# 返回原目录
cd ..

echo -e "\n${GREEN}启动脚本执行完成${NC}"
