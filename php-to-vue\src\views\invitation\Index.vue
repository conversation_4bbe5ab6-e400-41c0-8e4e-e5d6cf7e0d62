<template>
  <div class="invitation-container">
    <div class="page-header">
      <h1 class="page-title">邀请码管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="$router.push('/invitation/create')"
          >创建邀请码</el-button
        >
        <el-button type="success" @click="showBatchCreateDialog"
          >批量生成</el-button
        >
      </div>
    </div>

    <!-- 搜索栏 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="listQuery" class="search-form">
        <el-form-item label="邀请码">
          <el-input
            v-model="listQuery.code"
            placeholder="请输入邀请码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="listQuery.state" placeholder="全部" clearable>
            <el-option :value="1" label="启用"></el-option>
            <el-option :value="0" label="禁用"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card shadow="hover" class="table-container">
      <div class="table-header-actions">
        <el-button
          size="small"
          type="danger"
          :disabled="selectedIds.length === 0"
          @click="batchDelete"
          >批量删除</el-button
        >
        <el-button
          size="small"
          type="success"
          :disabled="selectedIds.length === 0"
          @click="batchEnable"
          >批量启用</el-button
        >
        <el-button
          size="small"
          type="info"
          :disabled="selectedIds.length === 0"
          @click="batchDisable"
          >批量禁用</el-button
        >
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column
          prop="code"
          label="邀请码"
        ></el-table-column>
        <el-table-column prop="description" label="描述">
          <template slot-scope="{ row }">
            {{ row.description || "无" }}
          </template>
        </el-table-column>
        <el-table-column prop="used_count" label="已使用次数"></el-table-column>
        <el-table-column prop="limit" label="使用限制">
          <template slot-scope="{ row }">
            {{ row.limit ? row.limit : "不限" }}
          </template>
        </el-table-column>
        <el-table-column prop="expired_at" label="过期时间">
          <template slot-scope="{ row }">
            {{ row.expired_at || "永不过期" }}
          </template>
        </el-table-column>
        <el-table-column prop="state" label="状态">
          <template slot-scope="{ row }">
            <el-tag :type="row.state === 1 ? 'success' : 'info'">
              {{ row.state === 1 ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" fixed="right">
          <template slot-scope="{ row }">
            <el-button size="mini" @click="handleEdit(row)">编辑</el-button>
            <el-button
              size="mini"
              :type="row.state === 1 ? 'info' : 'success'"
              @click="toggleState(row)"
            >
              {{ row.state === 1 ? "禁用" : "启用" }}
            </el-button>
            <el-button size="mini" type="danger" @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="listQuery.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="listQuery.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </el-card>

    <!-- 批量生成对话框 -->
    <el-dialog
      title="批量生成邀请码"
      :visible.sync="batchDialogVisible"
      width="500px"
    >
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="生成数量">
          <el-input-number
            v-model="batchForm.count"
            :min="1"
            :max="100"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="使用限制">
          <el-input-number
            v-model="batchForm.limit"
            :min="0"
            placeholder="0表示不限制"
          ></el-input-number>
          <span class="form-tip">0表示不限制使用次数</span>
        </el-form-item>
        <el-form-item label="过期时间">
          <el-date-picker
            v-model="batchForm.expired_at"
            type="datetime"
            placeholder="选择过期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="{
              disabledDate(date) {
                return date.getTime() < Date.now() - 8.64e7;
              },
            }"
          >
          </el-date-picker>
          <span class="form-tip">不设置表示永不过期</span>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleBatchCreate"
          :loading="batchLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getInvitationList,
  deleteInvitation,
  updateInvitation,
  batchInvitation,
  createInvitation,
} from "@/api/invitation";

export default {
  name: "InvitationList",
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      selectedIds: [],

      listQuery: {
        page: 1,
        page_size: 10,
        code: "",
        state: "",
      },

      batchDialogVisible: false,
      batchLoading: false,
      batchForm: {
        count: 10,
        limit: 0,
        expired_at: "",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取邀请码列表
    async getList() {
      this.listLoading = true;
      try {
        const response = await getInvitationList(this.listQuery);
        console.log("获取到的数据:", response);

        // 正确处理返回的数据结构
        if (response && response.items) {
          this.list = response.items;
          this.total = response.total || 0;
        } else {
          this.list = [];
          this.total = 0;
          console.error("返回数据格式不正确:", response);
        }
      } catch (error) {
        console.error("获取邀请码列表失败:", error);
        this.$message.error(`获取邀请码列表失败: ${error.message}`);
        this.list = [];
        this.total = 0;
      } finally {
        this.listLoading = false;
      }
    },

    // 搜索
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },

    // 重置搜索
    resetFilter() {
      this.listQuery = {
        page: 1,
        page_size: 10,
        code: "",
        state: "",
      };
      this.getList();
    },

    // 处理页大小变化
    handleSizeChange(size) {
      this.listQuery.page_size = size;
      this.getList();
    },

    // 处理页码变化
    handleCurrentChange(page) {
      this.listQuery.page = page;
      this.getList();
    },

    // 显示批量创建对话框
    showBatchCreateDialog() {
      this.batchForm = {
        count: 10,
        limit: 0,
        expired_at: "",
      };
      this.batchDialogVisible = true;
    },

    // 批量创建邀请码
    async handleBatchCreate() {
      if (!this.batchForm.count || this.batchForm.count < 1) {
        return this.$message.warning("请输入有效的生成数量");
      }

      this.batchLoading = true;
      try {
        // 创建批量生成邀请码的请求参数
        const data = {
          // 随机生成一个code前缀，让系统自动补充
          code: `AUTO_${this.generateRandomCode()}`,
          count: this.batchForm.count,
          state: 1,
        };

        // 添加可选参数
        if (this.batchForm.limit > 0) {
          data.limit = this.batchForm.limit;
        }

        if (this.batchForm.expired_at) {
          data.expired_at = this.batchForm.expired_at;
        }

        console.log("批量生成邀请码请求:", data);
        await createInvitation(data);

        this.$message.success(`成功生成 ${this.batchForm.count} 个邀请码`);
        this.batchDialogVisible = false;
        this.getList();
      } catch (error) {
        console.error("批量生成失败:", error);
        this.$message.error(`批量生成失败: ${error.message}`);
      } finally {
        this.batchLoading = false;
      }
    },

    // 生成随机邀请码
    generateRandomCode() {
      const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
      let code = "";
      for (let i = 0; i < 6; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return code;
    },

    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedIds = selection.map((item) => item.id);
    },

    // 批量删除
    async batchDelete() {
      if (this.selectedIds.length === 0) return;

      try {
        await this.$confirm("确认删除选中的邀请码？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        try {
          await batchInvitation({
            action: "delete",
            ids: this.selectedIds,
          });
          this.$message.success("批量删除成功");
          this.getList();
        } catch (error) {
          this.$message.error(`批量删除失败: ${error.message}`);
        }
      } catch (error) {
        // 用户取消操作，不做处理
      }
    },

    // 批量启用
    async batchEnable() {
      if (this.selectedIds.length === 0) return;

      try {
        await batchInvitation({
          action: "enable",
          ids: this.selectedIds,
        });
        this.$message.success("批量启用成功");
        this.getList();
      } catch (error) {
        this.$message.error(`批量启用失败: ${error.message}`);
      }
    },

    // 批量禁用
    async batchDisable() {
      if (this.selectedIds.length === 0) return;

      try {
        await batchInvitation({
          action: "disable",
          ids: this.selectedIds,
        });
        this.$message.success("批量禁用成功");
        this.getList();
      } catch (error) {
        this.$message.error(`批量禁用失败: ${error.message}`);
      }
    },

    // 编辑邀请码
    handleEdit(row) {
      this.$router.push(`/invitation/edit?id=${row.id}`);
    },

    // 切换状态
    async toggleState(row) {
      try {
        await updateInvitation(row.id, {
          state: row.state === 1 ? 0 : 1,
        });
        this.$message.success(`${row.state === 1 ? "禁用" : "启用"}成功`);
        row.state = row.state === 1 ? 0 : 1;
      } catch (error) {
        this.$message.error(`操作失败: ${error.message}`);
      }
    },

    // 删除单个邀请码
    async handleDelete(row) {
      try {
        await this.$confirm("确认删除此邀请码？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        try {
          await deleteInvitation(row.id);
          this.$message.success("删除成功");
          this.getList();
        } catch (error) {
          this.$message.error(`删除失败: ${error.message}`);
        }
      } catch (error) {
        // 用户取消操作，不做处理
      }
    },
  },
};
</script>

<style scoped>
.invitation-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.table-container {
  margin-bottom: 20px;
}

.table-header-actions {
  margin-bottom: 15px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.form-tip {
  display: block;
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
</style>
