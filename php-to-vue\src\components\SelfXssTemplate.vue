<template>
  <div class="self-xss-template">
    <!-- 操作按钮区域 -->
    <div class="operation-bar">
      <el-button type="primary" size="small" @click="showCreateDialog">
        <i class="el-icon-plus"></i> 新建模板
      </el-button>
      <el-button type="success" size="small" @click="loadTemplates">
        <i class="el-icon-refresh"></i> 刷新
      </el-button>
    </div>

    <!-- 模板列表 -->
    <div class="template-list" v-loading="loading">
      <div v-if="templates.length === 0" class="empty-state">
        <p>暂无自定义XSS模板</p>
      </div>

      <div v-for="template in templates" :key="template.id" class="code-block">
        <div class="template-header">
          <h4>{{ template.title }}</h4>
          <div class="template-actions">
            <el-button size="mini" type="primary" @click="editTemplate(template)">
              <i class="el-icon-edit"></i> 编辑
            </el-button>
            <el-button size="mini" type="success" @click="copyCode(template.content)">
              <i class="el-icon-document-copy"></i> 复制
            </el-button>
            <el-button size="mini" type="danger" @click="deleteTemplate(template.id)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </div>
        </div>

        <div class="code-textarea-container">
          <el-input
            type="textarea"
            :rows="3"
            :value="template.content"
            class="code-textarea"
            readonly
          />
          <el-button size="small" type="primary" @click="copyCode(template.content)">
            复制代码
          </el-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="60%"
      @close="resetForm">
      <el-form :model="form" :rules="rules" ref="form" label-width="80px">
        <el-form-item label="模板标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入模板标题"></el-input>
        </el-form-item>
        <el-form-item label="模板内容" prop="content">
          <el-input
            type="textarea"
            :rows="8"
            v-model="form.content"
            placeholder="请输入XSS模板内容">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getXssTemplateList,
  createXssTemplate,
  updateXssTemplate,
  deleteXssTemplate
} from '@/api/xssTemplate'

export default {
  name: 'SelfXssTemplate',
  props: {
    projectId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      submitting: false,
      templates: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,

      // 对话框相关
      dialogVisible: false,
      isEdit: false,
      editingId: null,

      // 表单数据
      form: {
        title: '',
        content: ''
      },

      // 表单验证规则
      rules: {
        title: [
          { required: true, message: '请输入模板标题', trigger: 'blur' },
          { max: 255, message: '标题长度不能超过255个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入模板内容', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑XSS模板' : '创建XSS模板'
    }
  },
  mounted() {
    this.loadTemplates()
  },
  watch: {
    projectId: {
      handler() {
        this.currentPage = 1
        this.loadTemplates()
      },
      immediate: false
    }
  },
  methods: {
    // 加载模板列表
    async loadTemplates() {
      this.loading = true
      try {
        const params = {
          project_id: this.projectId,
          page: this.currentPage,
          page_size: this.pageSize
        }

        const response = await getXssTemplateList(params)
        this.templates = response.list || []
        this.total = response.total || 0
      } catch (error) {
        console.error('加载XSS模板失败:', error)
        this.$message.error('加载XSS模板失败: ' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 显示创建对话框
    showCreateDialog() {
      this.isEdit = false
      this.editingId = null
      this.resetForm()
      this.dialogVisible = true
    },

    // 编辑模板
    editTemplate(template) {
      this.isEdit = true
      this.editingId = template.id
      this.form.title = template.title
      this.form.content = template.content
      this.dialogVisible = true
    },

    // 提交表单
    async submitForm() {
      try {
        await this.$refs.form.validate()

        this.submitting = true
        const data = {
          title: this.form.title,
          content: this.form.content
        }

        if (this.isEdit) {
          await updateXssTemplate(this.editingId, data)
          this.$message.success('模板更新成功')
        } else {
          data.project_id = parseInt(this.projectId)
          await createXssTemplate(data)
          this.$message.success('模板创建成功')
        }

        this.dialogVisible = false
        this.loadTemplates()
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('操作失败: ' + error.message)
      } finally {
        this.submitting = false
      }
    },

    // 删除模板
    async deleteTemplate(id) {
      try {
        await this.$confirm('确定要删除这个XSS模板吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await deleteXssTemplate(id)
        this.$message.success('模板删除成功')
        this.loadTemplates()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error('删除失败: ' + error.message)
        }
      }
    },

    // 复制代码
    copyCode(code) {
      navigator.clipboard.writeText(code).then(() => {
        this.$message.success('代码已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    },

    // 重置表单
    resetForm() {
      this.form = {
        title: '',
        content: ''
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    },

    // 分页相关
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadTemplates()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadTemplates()
    }
  }
}
</script>

<style scoped>
.self-xss-template {
  padding: 10px;
}

.operation-bar {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.template-list {
  min-height: 200px;
}

.empty-state {
  text-align: center;
  padding: 50px 0;
  color: #909399;
}

.code-block {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.template-header h4 {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.template-actions {
  display: flex;
  gap: 5px;
}

.code-textarea {
  font-family: 'Courier New', Courier, monospace;
}

.code-textarea-container {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: flex-start;
  margin-bottom: 10px;
}

.template-meta {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #909399;
}

.meta-item {
  display: inline-block;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}
</style>
