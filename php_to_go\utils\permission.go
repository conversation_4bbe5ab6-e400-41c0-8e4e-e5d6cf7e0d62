package utils

import (
	"go-fiber-api/database"
	"go-fiber-api/models"
	"path/filepath"
	"strings"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
	"log"
)

// PermissionMiddleware 创建权限验证中间件
func PermissionMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 获取当前请求路径和方法
		path := c.Path()
		method := c.Method()

		// 跳过不需要验证权限的路径
		if isPublicPath(path) {
			return c.Next()
		}

		// 获取用户ID
		userID := GetUserIDFromContext(c)
		if userID == 0 {
			return Unauthorized(c, "未登录或登录已过期", nil)
		}

		// 检查是否是管理员
		if IsAdmin(c) {
			return c.Next()
		}

		// 检查用户是否有权限
		if hasPermission(userID, path, method) {
			return c.Next()
		}

		// 无权限
		return Forbidden(c, "您没有权限执行此操作", nil)
	}
}

// isPublicPath 检查路径是否为公开路径
func isPublicPath(path string) bool {
	publicPaths := []string{
		"/api/auth/login",
		"/api/auth/register",
		"/api/auth/forgot-password",
		"/api/auth/reset-password",
		"/swagger",
		"/api-docs",
	}

	for _, publicPath := range publicPaths {
		if strings.HasPrefix(path, publicPath) {
			return true
		}
	}
	return false
}

// hasPermission 检查用户是否有访问指定路径和方法的权限
func hasPermission(userID uint64, path string, method string) bool {
	var permissions []models.Permission

	// 获取用户所有角色的权限
	if err := database.DB.Raw(`
		SELECT p.* FROM admin_permissions p
		JOIN admin_role_permissions rp ON p.id = rp.permission_id
		JOIN admin_role_users ru ON rp.role_id = ru.role_id
		WHERE ru.user_id = ? AND (p.http_method LIKE ? OR p.http_method IS NULL)
	`, userID, "%"+method+"%").Scan(&permissions).Error; err != nil {
		return false
	}

	// 检查是否有匹配的权限
	for _, perm := range permissions {
		if perm.HttpPath != "" && matchPath(path, perm.HttpPath) {
			return true
		}
	}

	return false
}

// matchPath 检查请求路径是否匹配权限路径
func matchPath(requestPath, permissionPath string) bool {
	// 如果权限路径为空，默认不能访问
	if permissionPath == "" {
		return false
	}

	// 分割权限路径（可能包含多个路径，以逗号分隔）
	paths := strings.Split(permissionPath, ",")

	for _, p := range paths {
		p = strings.TrimSpace(p)
		
		// 如果是精确匹配
		if p == requestPath {
			return true
		}

		// 如果以*结尾，进行前缀匹配
		if strings.HasSuffix(p, "*") {
			prefix := strings.TrimSuffix(p, "*")
			if strings.HasPrefix(requestPath, prefix) {
				return true
			}
		}

		// 支持路径参数匹配，例如 /admin/users/* 应该匹配 /admin/users/123
		matched, err := filepath.Match(p, requestPath)
		if err == nil && matched {
			return true
		}
	}

	return false
}

// GetUserPermissions 获取用户所有权限
func GetUserPermissions(userID uint64) ([]string, error) {
	var slugs []string

	err := database.DB.Raw(`
		SELECT DISTINCT p.slug FROM admin_permissions p
		JOIN admin_role_permissions rp ON p.id = rp.permission_id
		JOIN admin_role_users ru ON rp.role_id = ru.role_id
		WHERE ru.user_id = ?
	`, userID).Scan(&slugs).Error

	if err != nil {
		return nil, err
	}

	return slugs, nil
}

// HasPermissionBySlug 检查用户是否有指定标识的权限
func HasPermissionBySlug(userID uint64, slug string) bool {
	// 首先检查用户是否有直接分配的权限
	var directCount int64
	database.DB.Raw(`
		SELECT COUNT(*) FROM admin_permissions p
		JOIN admin_user_permissions up ON p.id = up.permission_id
		WHERE up.user_id = ? AND p.slug = ?
	`, userID, slug).Count(&directCount)
	
	// 如果用户有直接权限，则返回true
	if directCount > 0 {
		return true
	}
	
	// 如果用户没有直接权限，检查用户通过角色获得的权限
	var roleCount int64
	database.DB.Raw(`
		SELECT COUNT(*) FROM admin_permissions p
		JOIN admin_role_permissions rp ON p.id = rp.permission_id
		JOIN admin_role_users ru ON rp.role_id = ru.role_id
		WHERE ru.user_id = ? AND p.slug = ?
	`, userID, slug).Count(&roleCount)

	return roleCount > 0
}

// CheckPermission 检查当前用户是否有指定路径的访问权限
func CheckPermission(c *fiber.Ctx, path string, method string) bool {
	userID := GetUserIDFromContext(c)
	if userID == 0 {
		log.Printf("CheckPermission: 用户ID为0，无法验证权限")
		return false
	}

	log.Printf("CheckPermission: 正在检查用户(ID=%d)是否有权限访问: %s [%s]", userID, path, method)

	// 管理员拥有所有权限
	if IsAdmin(c) {
		log.Printf("CheckPermission: 用户(ID=%d)是管理员，自动授权访问", userID)
		return true
	}

	// 尝试1: 从用户的直接权限中查找
	var directPermissions []models.Permission
	directPermQuery := `
		SELECT DISTINCT p.* FROM admin_permissions p
		JOIN admin_role_users up ON p.id = up.permission_id
		WHERE up.user_id = ? AND (p.http_method LIKE ? OR p.http_method = '' OR p.http_method LIKE '%*%')
	`
	err := database.DB.Raw(directPermQuery, userID, "%"+method+"%").Scan(&directPermissions).Error
	
	if err != nil {
		log.Printf("CheckPermission: 查询用户(ID=%d)直接权限时发生错误: %v", userID, err)
	} else {
		log.Printf("CheckPermission: 用户(ID=%d)拥有%d个直接权限记录", userID, len(directPermissions))
		
		// 检查直接权限是否匹配
		for i, perm := range directPermissions {
			log.Printf("CheckPermission: 检查直接权限记录%d: ID=%d, 名称='%s', 路径='%s', 方法='%s'", 
				i+1, perm.ID, perm.Name, perm.HttpPath, perm.HttpMethod)
			
			if perm.HttpPath != "" && matchPath(path, perm.HttpPath) {
				log.Printf("CheckPermission: 直接权限匹配成功! 用户(ID=%d)有权限访问: %s [%s]", userID, path, method)
				return true
			}
		}
		
		log.Printf("CheckPermission: 用户(ID=%d)没有直接权限匹配，继续检查角色权限", userID)
	}

	// 尝试2: 如果没有直接权限匹配，从用户的角色权限中查找
	var rolePermissions []models.Permission
	rolePermQuery := `
		SELECT DISTINCT p.* FROM admin_permissions p
		JOIN admin_role_permissions rp ON p.id = rp.permission_id
		JOIN admin_role_users ru ON rp.role_id = ru.role_id
		WHERE ru.user_id = ? AND (p.http_method LIKE ? OR p.http_method = '' OR p.http_method LIKE '%*%')
	`
	err = database.DB.Raw(rolePermQuery, userID, "%"+method+"%").Scan(&rolePermissions).Error
	
	if err != nil {
		log.Printf("CheckPermission: 查询用户(ID=%d)角色权限时发生错误: %v", userID, err)
		return false
	}
	
	log.Printf("CheckPermission: 用户(ID=%d)通过角色拥有%d个权限记录", userID, len(rolePermissions))

	// 获取用户的角色信息用于日志记录
	var roles []struct {
		ID   uint64
		Name string
	}
	database.DB.Raw(`
		SELECT r.id, r.name FROM admin_roles r
		JOIN admin_role_users ru ON r.id = ru.role_id
		WHERE ru.user_id = ?
	`, userID).Scan(&roles)
	
	if len(roles) > 0 {
		log.Printf("CheckPermission: 用户(ID=%d)拥有以下角色:", userID)
		for i, role := range roles {
			log.Printf("  - 角色%d: ID=%d, 名称='%s'", i+1, role.ID, role.Name)
		}
	} else {
		log.Printf("CheckPermission: 警告! 用户(ID=%d)没有任何角色，无法获取角色权限", userID)
		return false
	}

	// 检查角色权限是否匹配
	for i, perm := range rolePermissions {
		log.Printf("CheckPermission: 检查角色权限记录%d: ID=%d, 名称='%s', 路径='%s', 方法='%s'", 
			i+1, perm.ID, perm.Name, perm.HttpPath, perm.HttpMethod)
		
		if perm.HttpPath != "" && matchPath(path, perm.HttpPath) {
			log.Printf("CheckPermission: 角色权限匹配成功! 用户(ID=%d)通过角色获得权限访问: %s [%s]", userID, path, method)
			return true
		}
	}

	log.Printf("CheckPermission: 匹配失败! 用户(ID=%d)没有权限访问: %s [%s]", userID, path, method)
	return false
}

// GetUserRoles 获取用户所有角色
func GetUserRoles(userID uint64) ([]models.Role, error) {
	var roles []models.Role

	err := database.DB.Raw(`
		SELECT r.* FROM admin_roles r
		JOIN admin_role_users ru ON r.id = ru.role_id
		WHERE ru.user_id = ? AND r.deleted_at IS NULL
	`, userID).Scan(&roles).Error

	if err != nil {
		return nil, err
	}

	return roles, nil
}

// GetUserRoleSlugs 获取用户角色标识列表
func GetUserRoleSlugs(userID uint64) ([]string, error) {
	var slugs []string

	err := database.DB.Raw(`
		SELECT r.slug FROM admin_roles r
		JOIN admin_role_users ru ON r.id = ru.role_id
		WHERE ru.user_id = ? AND r.deleted_at IS NULL
	`, userID).Scan(&slugs).Error

	if err != nil {
		return nil, err
	}

	return slugs, nil
}

// CheckPermissionBySlug 检查用户是否有特定权限（包含继承）
func CheckPermissionBySlug(c *fiber.Ctx, permissionSlug string) bool {
	// 获取用户ID
	userID := GetUserIDFromContext(c)
	if userID == 0 {
		return false
	}

	// 如果是超级管理员，拥有所有权限
	if IsAdmin(c) {
		return true
	}

	// 使用权限继承检查
	return HasPermissionWithInheritance(userID, permissionSlug)
}

// RequirePermission 中间件，要求用户具有特定权限
func RequirePermission(permissionSlug string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		if !CheckPermissionBySlug(c, permissionSlug) {
			return Forbidden(c, "您没有权限执行此操作", nil)
		}
		return c.Next()
	}
}

// HasUserRole 检查用户是否具有特定角色
func HasUserRole(c *fiber.Ctx, roleName string) bool {
	// 获取用户ID
	userID := GetUserIDFromContext(c)
	if userID == 0 {
		return false
	}

	// 从数据库查询用户角色
	var count int64
	err := database.DB.Raw(`
		SELECT COUNT(*) FROM admin_roles r
		JOIN admin_role_users ru ON r.id = ru.role_id
		WHERE ru.user_id = ? AND r.name = ?
	`, userID, roleName).Count(&count).Error

	if err != nil || count == 0 {
		return false
	}

	return true
}

// RequireRole 中间件，要求用户具有特定角色
func RequireRole(roleName string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		if !HasUserRole(c, roleName) {
			return Forbidden(c, "您没有所需的角色权限", nil)
		}
		return c.Next()
	}
}

// CanAccessRoute 检查用户是否能访问指定路由
func CanAccessRoute(c *fiber.Ctx, route string) bool {
	// 获取用户ID
	userID := GetUserIDFromContext(c)
	if userID == 0 {
		return false
	}

	// 超级管理员可以访问所有路由
	if IsAdmin(c) {
		return true
	}

	// 从数据库查询用户可访问的菜单
	var menus []models.Menu
	err := database.DB.Raw(`
		SELECT m.* FROM admin_menu m
		JOIN admin_role_menu rm ON m.id = rm.menu_id
		JOIN admin_role_users ru ON rm.role_id = ru.role_id
		WHERE ru.user_id = ?
	`, userID).Find(&menus).Error

	if err != nil {
		return false
	}

	// 检查每个菜单的URI是否匹配
	for _, menu := range menus {
		if menu.URI == route || strings.HasPrefix(route, menu.URI+"/") {
			return true
		}
	}

	return false
}

// RequireRouteAccess 中间件，要求用户能访问指定路由
func RequireRouteAccess(route string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		if !CanAccessRoute(c, route) {
			return Forbidden(c, "您没有权限访问此路由", nil)
		}
		return c.Next()
	}
}

// CheckMenuPermission 检查用户是否有权限访问指定菜单
func CheckMenuPermission(userID uint64, menuID uint64) bool {
	// 首先检查用户是否是管理员
	var adminCount int64
	database.DB.Raw(`
		SELECT COUNT(*) FROM admin_role_users ru
		JOIN admin_roles r ON ru.role_id = r.id
		WHERE ru.user_id = ? AND (r.name = 'administrator' OR r.name = 'admin')
	`, userID).Count(&adminCount)
	
	if adminCount > 0 {
		return true
	}
	
	// 检查用户角色是否有菜单权限
	var count int64
	err := database.DB.Raw(`
		SELECT COUNT(*) FROM admin_role_menu rm
		JOIN admin_role_users ru ON rm.role_id = ru.role_id
		WHERE ru.user_id = ? AND rm.menu_id = ?
	`, userID, menuID).Count(&count).Error
	
	if err != nil || count == 0 {
		return false
	}
	
	return true
}

// GetAllChildPermissions 递归获取指定权限的所有子权限ID
func GetAllChildPermissions(parentID uint64) ([]uint64, error) {
	log.Printf("GetAllChildPermissions - 查找权限ID %d 的所有子权限", parentID)
	
	var childIDs []uint64

	// 直接查询子权限
	var directChildren []uint64
	if err := database.DB.Model(&models.Permission{}).
		Where("parent_id = ?", parentID).
		Pluck("id", &directChildren).Error; err != nil {
		log.Printf("GetAllChildPermissions - 查询子权限失败: %v", err)
		return nil, err
	}

	log.Printf("GetAllChildPermissions - 权限ID %d 的直接子权限: %v", parentID, directChildren)

	// 添加直接子权限
	childIDs = append(childIDs, directChildren...)

	// 递归获取每个子权限的子权限
	for _, childID := range directChildren {
		grandChildren, err := GetAllChildPermissions(childID)
		if err != nil {
			return nil, err
		}
		childIDs = append(childIDs, grandChildren...)
	}

	log.Printf("GetAllChildPermissions - 权限ID %d 的所有子权限(包括孙权限): %v", parentID, childIDs)
	return childIDs, nil
}

// GetPermissionsWithChildren 获取权限列表及其所有子权限
func GetPermissionsWithChildren(permissionIDs []uint64) ([]uint64, error) {
	if len(permissionIDs) == 0 {
		return []uint64{}, nil
	}

	// 添加日志输出
	log.Printf("GetPermissionsWithChildren - 原始权限IDs: %v", permissionIDs)

	allPermissionIDs := make(map[uint64]bool)

	// 添加原始权限ID
	for _, id := range permissionIDs {
		allPermissionIDs[id] = true
	}

	// 为每个权限获取其子权限
	for _, permID := range permissionIDs {
		log.Printf("GetPermissionsWithChildren - 处理权限ID: %d", permID)
		childIDs, err := GetAllChildPermissions(permID)
		if err != nil {
			log.Printf("GetPermissionsWithChildren - 获取子权限失败: %v", err)
			return nil, err
		}

		// 添加子权限ID
		log.Printf("GetPermissionsWithChildren - 权限ID %d 的子权限: %v", permID, childIDs)
		for _, childID := range childIDs {
			allPermissionIDs[childID] = true
		}
	}

	// 转换为切片
	result := make([]uint64, 0, len(allPermissionIDs))
	for id := range allPermissionIDs {
		result = append(result, id)
	}
	
	log.Printf("GetPermissionsWithChildren - 最终权限列表 (包含子权限): %v", result)

	return result, nil
}

// HasPermissionWithInheritance 检查用户是否有权限（包含继承）
func HasPermissionWithInheritance(userID uint64, permissionSlug string) bool {
	log.Printf("HasPermissionWithInheritance: 检查用户(ID=%d)是否有权限: %s", userID, permissionSlug)
	
	// 首先检查用户是否有直接权限或通过角色获得的权限
	if HasPermissionBySlug(userID, permissionSlug) {
		log.Printf("HasPermissionWithInheritance: 用户(ID=%d)具有权限: %s", userID, permissionSlug)
		return true
	}

	// 获取目标权限信息
	var targetPermission models.Permission
	if err := database.DB.Where("slug = ?", permissionSlug).First(&targetPermission).Error; err != nil {
		log.Printf("HasPermissionWithInheritance: 未找到权限: %s, 错误: %v", permissionSlug, err)
		return false
	}

	// 如果目标权限有父权限，检查用户是否有父权限
	if targetPermission.ParentID > 0 {
		var parentPermission models.Permission
		if err := database.DB.First(&parentPermission, targetPermission.ParentID).Error; err != nil {
			log.Printf("HasPermissionWithInheritance: 未找到父权限(ID=%d), 错误: %v", targetPermission.ParentID, err)
			return false
		}

		log.Printf("HasPermissionWithInheritance: 递归检查父权限: %s", parentPermission.Slug)
		// 递归检查父权限
		return HasPermissionWithInheritance(userID, parentPermission.Slug)
	}

	log.Printf("HasPermissionWithInheritance: 用户(ID=%d)没有权限: %s", userID, permissionSlug)
	return false
}

// SyncRolePermissions 同步角色权限，确保父权限包含子权限
func SyncRolePermissions(roleID uint64) error {
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 获取角色当前的权限
		var currentPermissions []models.Permission
		if err := tx.Model(&models.Role{ID: roleID}).Association("Permissions").Find(&currentPermissions); err != nil {
			return err
		}

		// 获取当前权限ID列表
		var currentPermissionIDs []uint64
		for _, perm := range currentPermissions {
			currentPermissionIDs = append(currentPermissionIDs, perm.ID)
		}

		// 获取包含子权限的完整权限列表
		allPermissionIDs, err := GetPermissionsWithChildren(currentPermissionIDs)
		if err != nil {
			return err
		}

		// 查找所有需要的权限
		var allPermissions []models.Permission
		if err := tx.Where("id IN ?", allPermissionIDs).Find(&allPermissions).Error; err != nil {
			return err
		}

		// 更新角色权限关联
		role := models.Role{ID: roleID}
		if err := tx.Model(&role).Association("Permissions").Replace(allPermissions); err != nil {
			return err
		}

		return nil
	})
}