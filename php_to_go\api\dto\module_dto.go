package dto

// ModuleListItem 模块列表项
type ModuleListItem struct {
	ID          uint64 `json:"id"`          // 模块ID
	Title       string `json:"title"`       // 模块名称
	Description string `json:"description"` // 模块描述
	Level       int8   `json:"level"`       // 安全等级
	IsShare     int8   `json:"is_share"`    // 共享状态
	State       int8   `json:"state"`       // 状态
	Keys        string `json:"keys"`
	UserID      uint64 `json:"user_id"`
	Deleted     bool   `json:"deleted"`
	CreatedAt   string `json:"created_at"`  // 创建时间
}

// ModuleResponse 模块详情
type ModuleResponse struct {
	ID          	uint64    `json:"id"`           				// 模块ID
	Title       	string    `json:"title"`        				// 模块名称
	Description 	string    `json:"description"`  				// 模块描述
	Keys        	string    `json:"keys"`         				// 关键字
	Setkeys     	string    `json:"setkeys"`      				// 配置参数
	Code        	string    `json:"code"`         				// HTML代码
	OriginalCode    string    `json:"original_code"`        		// 原始HTML代码
	Level       	int8      `json:"level"`        				// 安全等级
	UserID      	uint64    `json:"user_id"`      				// 创建人ID
	IsShare     	int8      `json:"is_share"`     				// 共享状态
	State       	int8      `json:"state"`        				// 状态
	CreatedAt   	string    `json:"created_at"`   				// 创建时间
	UpdatedAt   	string    `json:"updated_at"`   				// 更新时间
}

// ModuleRequest 模块创建请求
type ModuleRequest struct {
	Title       string `json:"title" validate:"required"`       // 模块名称
	Description string `json:"description"`                     // 模块描述
	Keys        string `json:"keys"`                           // 关键字
	Setkeys     string `json:"setkeys"`                        // 配置参数
	Code        string `json:"code" validate:"required"`       // HTML代码
	Level       int8   `json:"level" validate:"min=0,max=9"`   // 安全等级
	IsShare     int8   `json:"is_share" validate:"min=0,max=1"` // 共享状态
	State       int8   `json:"state" validate:"min=0,max=1"`   // 状态
}

// ModuleUpdateRequest 模块更新请求
type ModuleUpdateRequest struct {
	Title       *string `json:"title"`                       // 模块名称
	Description *string `json:"description"`                 // 模块描述
	Keys        *string `json:"keys"`                        // 关键字
	Setkeys     *string `json:"setkeys"`                     // 配置参数
	Code        *string `json:"code"`                        // HTML代码
	Level       *int8   `json:"level" validate:"omitempty,min=0,max=9"` // 安全等级
	IsShare     *int8   `json:"is_share" validate:"omitempty,min=0,max=1"` // 共享状态
	State       *int8   `json:"state" validate:"omitempty,min=0,max=1"` // 状态
} 