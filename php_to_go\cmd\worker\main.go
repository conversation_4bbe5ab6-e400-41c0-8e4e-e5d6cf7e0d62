package main

import (
	"flag"
	"go-fiber-api/database"
	"go-fiber-api/utils/queue"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/hibiken/asynq"
	"github.com/joho/godotenv"
)

func main() {
	// 解析命令行参数
	concurrency := flag.Int("concurrency", 10, "工作进程并发数")
	enableMonitor := flag.Bool("monitor", false, "是否启用监控服务")
	monitorAddr := flag.String("monitor-addr", ":8080", "监控服务监听地址")
	flag.Parse()

	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Printf("警告: 无法加载.env文件: %v\n", err)
	}

	// 初始化数据库连接
	if err := database.ConnDatabase(); err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 初始化Redis连接
	if err := database.InitRedis(); err != nil {
		log.Fatalf("Redis初始化失败: %v", err)
	}

	// 创建服务器
	server := queue.NewServer(*concurrency)

	// 创建多路复用器并注册处理器
	mux := asynq.NewServeMux()
	queue.RegisterHandlers(mux)

	// 如果启用监控，则在单独的goroutine中启动监控服务
	if *enableMonitor {
		go func() {
			opts := queue.MonitorOptions{
				ListenAddr: *monitorAddr,
				RootPath:   "/",
				// 可以在这里设置基本认证
				// Username: "admin",
				// Password: "password",
			}
			queue.StartMonitor(opts)
		}()
	}
	// 捕获退出信号
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	// 在单独的goroutine中启动服务器
	go func() {
		log.Println("Asynq工作进程已启动，并发数:", *concurrency)
		if err := server.Run(mux); err != nil {
			log.Fatalf("无法启动队列工作进程: %v", err)
		}
	}()

	// 等待退出信号
	<-c
	log.Println("正在关闭工作进程...")
	server.Shutdown()
	log.Println("工作进程已关闭")
} 