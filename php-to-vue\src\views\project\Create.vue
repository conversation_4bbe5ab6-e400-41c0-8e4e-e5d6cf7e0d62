<template>
  <div class="project-create">
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? '编辑项目' : '创建项目' }}</h1>
      <el-button @click="$router.push(isEdit ? `/project/${projectId}` : '/project')">
        <i class="el-icon-back"></i> {{ isEdit ? '返回项目详情' : '返回列表' }}
      </el-button>
    </div>

    <el-card shadow="hover" v-loading="loading">
      <el-form ref="projectForm" :model="formData" :rules="rules" label-width="120px" label-position="right"
        size="medium">

        <el-form-item label="项目名称" prop="title">
          <el-input v-model="formData.title" placeholder="请输入项目名称" maxlength="255" show-word-limit></el-input>
        </el-form-item>

        <el-form-item v-if="!isEdit" label="唯一标识" prop="unique_key">
          <el-input v-model="formData.unique_key" placeholder="请输入唯一标识（2-20个字符）" maxlength="20" show-word-limit>
            <template slot="append">
              <el-button @click="generateUniqueKey">生成</el-button>
            </template>
          </el-input>
          <small class="form-tip">唯一标识用于区分不同项目，创建后不可修改</small>
        </el-form-item>

        <el-form-item label="项目描述" prop="description">
          <el-input v-model="formData.description" type="textarea" placeholder="请输入项目描述" maxlength="255" show-word-limit
            :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
        </el-form-item>

        <el-form-item label="模块" prop="module_id">
          <el-select v-model="selectedModules" placeholder="请选择模块" style="width: 100%;" multiple filterable
            :loading="modulesLoading" popper-class="module-select-dropdown" :popper-append-to-body="false"
            @change="handleModulesChange">
            <el-option v-for="item in moduleOptions" :key="item.id" :label="item.title" :value="item.id"
              :disabled="item.state === 0">
              <div class="module-option">
                <span class="module-title">{{ item.title }}</span>
                <el-tag v-if="item.level" size="mini" :type="getModuleLevelType(item.level)" class="module-tag">
                  {{ getModuleLevelText(item.level) }}
                </el-tag>
              </div>
              <div v-if="item.description" class="module-description">{{ item.description }}</div>
            </el-option>
            <div slot="empty" class="empty-text">
              <span v-if="modulesLoading">加载中...</span>
              <span v-else>暂无可用模块</span>
            </div>
          </el-select>
          <div class="selected-modules-tags" v-if="selectedModules.length > 0">
            <el-tag v-for="moduleId in selectedModules" :key="moduleId" closable @close="removeModule(moduleId)"
              style="margin-right: 5px; margin-top: 5px;">
              {{ getModuleTitle(moduleId) }}
            </el-tag>
          </div>
        </el-form-item>

        <!-- <el-form-item label="模块参数" prop="module_ext_param">
          <el-input v-model="formData.module_ext_param" placeholder="请输入模块扩展参数（JSON格式）" type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"></el-input>
        </el-form-item> -->

        <el-form-item label="项目代码" prop="code">
          <el-input v-model="formData.code" type="textarea" placeholder="请输入项目代码"
            :autosize="{ minRows: 6, maxRows: 15 }"></el-input>
        </el-form-item>

        <el-form-item label="状态" prop="state">
          <el-switch v-model="formData.state" :active-value="1" :inactive-value="0">
          </el-switch>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            {{ isEdit ? '保存修改' : '创建项目' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getProject, createProject, updateProject } from '@/api/project'
import { getModuleList } from '@/api/module'

export default {
  name: 'ProjectCreate',
  data() {
    return {
      isEdit: false,
      projectId: null,
      loading: false,
      submitLoading: false,
      modulesLoading: false,
      selectedModules: [], // 选中的模块ID数组
      formData: {
        title: '',
        unique_key: '',
        description: '',
        module_id: '',
        module_ext_param: '',
        code: '',
        state: 1
      },
      rules: {
        title: [
          { required: true, message: '请输入项目名称', trigger: 'blur' },
          { min: 2, max: 255, message: '长度在 2 到 255 个字符', trigger: 'blur' }
        ],
        unique_key: [
          { required: true, message: '请输入唯一标识', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和连字符', trigger: 'blur' }
        ],
        module_id: [
          { required: true, message: '请选择模块', trigger: 'change' }
        ],
      },
      moduleOptions: []
    }
  },
  methods: {
    // 生成随机唯一标识
    generateUniqueKey() {
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
      let result = ''
      // 生成4-5位随机字符
      const length = Math.floor(Math.random() * 2) + 4; // 4或5位

      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }

      this.formData.unique_key = result
    },

    // 处理模块选择变化
    handleModulesChange(selectedValues) {
      // 将选中的模块ID数组转换为逗号分隔的字符串
      this.formData.module_id = selectedValues.join(',')
    },

    // 移除已选模块
    removeModule(moduleId) {
      const index = this.selectedModules.indexOf(moduleId)
      if (index !== -1) {
        this.selectedModules.splice(index, 1)
        this.handleModulesChange(this.selectedModules)
      }
    },

    // 获取模块标题
    getModuleTitle(moduleId) {
      const module = this.moduleOptions.find(item => item.id === moduleId)
      return module ? module.title : moduleId
    },

    // 提交表单
    submitForm() {
      this.$refs.projectForm.validate(valid => {
        if (!valid) {
          this.$message.error('请检查表单填写是否正确')
          return
        }

        this.submitLoading = true

        // 编辑模式
        if (this.isEdit) {
          updateProject(this.projectId, this.formData)
            .then(() => {
              this.$message.success('项目更新成功')
              this.$router.push(`/project/${this.projectId}`)
            })
            .catch(error => {
              this.$message.error(`更新失败: ${error.message}`)
            })
            .finally(() => {
              this.submitLoading = false
            })
        }
        // 创建模式
        else {
          createProject(this.formData)
            .then(response => {
              this.$message.success('项目创建成功')
              this.$router.push(`/project/${response.id}`)
            })
            .catch(error => {
              this.$message.error(`创建失败: ${error.message}`)
            })
            .finally(() => {
              this.submitLoading = false
            })
        }
      })
    },

    // 重置表单
    resetForm() {
      if (this.isEdit) {
        // 编辑模式下，重置为原始数据
        this.fetchProjectDetail()
      } else {
        // 创建模式下，清空表单
        this.$refs.projectForm.resetFields()
        this.selectedModules = []
      }
    },

    // 获取项目详情
    fetchProjectDetail() {
      if (!this.projectId) return

      this.loading = true
      getProject(this.projectId)
        .then(data => {
          // 填充表单数据
          this.formData = {
            title: data.title,
            description: data.description || '',
            module_id: data.module_id || '',
            module_ext_param: data.module_ext_param || '',
            code: data.code || '',
            state: data.state
          }

          // 如果module_id是字符串，则分割成数组
          if (data.module_id && typeof data.module_id === 'string') {
            this.selectedModules = data.module_id.split(',').map(id => id.trim())
          } else if (data.module_id) {
            this.selectedModules = [data.module_id.toString()]
          }

          // 展示项目唯一标识（只读）
          this.uniqueKey = data.unique_key
        })
        .catch(error => {
          this.$message.error(`获取项目详情失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 获取模块列表
    fetchModuleOptions() {
      this.modulesLoading = true
      // 只获取启用状态的模块
      const params = {
        state: 1,
        page_size: 100 // 获取更多模块
      }

      getModuleList(params)
        .then(response => {
          // response中应该有items数组
          if (response && response.items) {
            this.moduleOptions = response.items.map(item => ({
              ...item,
              // 确保id是字符串，因为v-model绑定的是字符串
              id: item.id.toString()
            }))
          } else {
            this.moduleOptions = []
          }
        })
        .catch(error => {
          this.$message.error(`获取模块列表失败: ${error.message}`)
          this.moduleOptions = []
        })
        .finally(() => {
          this.modulesLoading = false
        })
    },
    getModuleLevelType(level) {
      const map = {
        0: 'info',    // 默认
        1: 'success', // 低
        2: 'warning', // 中
        3: 'danger'   // 高
      }
      return map[level] || 'info'
    },
    getModuleLevelText(level) {
      const map = {
        0: '默认',
        1: '低',
        2: '中',
        3: '高'
      }
      return map[level] || '默认'
    }
  },
  created() {
    // 获取路由参数，判断是创建还是编辑
    const id = this.$route.query.id
    if (id) {
      this.isEdit = true
      this.projectId = id
      this.fetchProjectDetail()
    } else {
      // 默认生成一个唯一标识
      this.generateUniqueKey()
    }
    this.fetchModuleOptions()
  }
}
</script>

<style scoped>
.project-create {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
  display: block;
  margin-top: 5px;
}

.module-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
}

.module-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 85%;
  flex: 1;
}

.module-tag {
  flex-shrink: 0;
}

.module-description {
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 3px;
  white-space: normal;
  word-break: break-all;
  padding-right: 10px;
  width: 100%;
}

.empty-text {
  padding: 10px;
  text-align: center;
  color: #909399;
}

.selected-modules-tags {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
}
</style>

<style>
/* 全局样式，用于优化模块选择下拉框 */
.module-select-dropdown {
  max-width: 100% !important;
  width: auto !important;
}

.module-select-dropdown .el-select-dropdown__wrap {
  max-width: 100%;
}

.module-select-dropdown .el-select-dropdown__item {
  height: auto !important;
  padding: 8px 20px 8px 10px !important;
  line-height: 1.5 !important;
  white-space: normal;
  word-break: break-word;
}

.module-select-dropdown .el-select-dropdown__item.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
  background-color: #f5f7fa;
}

.module-select-dropdown .el-select-dropdown__item.is-disabled:hover {
  background-color: #f5f7fa;
}

.module-select-dropdown .el-select-dropdown__item.hover,
.module-select-dropdown .el-select-dropdown__item:hover {
  background-color: #f5f7fa;
}

.module-select-dropdown .el-tag {
  margin-left: 8px;
}
</style>