package utils

import (
	"go-fiber-api/models"
	"time"
)

// IsInvitationValid 检查邀请码是否有效
// 考虑邀请码的状态、过期时间和使用次数限制
func IsInvitationValid(invitation *models.Invitation) (bool, string) {
	// 检查邀请码状态
	if invitation.State != 1 {
		return false, "邀请码已被禁用"
	}
	
	// 检查是否过期
	if invitation.ExpiredAt != nil && time.Now().After(*invitation.ExpiredAt) {
		return false, "邀请码已过期"
	}
	
	// 检查使用次数限制
	if invitation.Limit > 0 && invitation.UsedCount >= invitation.Limit {
		return false, "邀请码使用次数已达上限"
	}
	
	return true, ""
} 