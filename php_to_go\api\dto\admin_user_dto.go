package dto

// AdminUserCreateDTO 创建管理员用户的请求数据结构
type AdminUserCreateDTO struct {
	Username      string   `json:"username" validate:"required,min=3,max=120"`
	Password      string   `json:"password" validate:"required,min=6,max=80"`
	Name          string   `json:"name" validate:"required"`
	Email         string   `json:"email" validate:"email"`
	Avatar        string   `json:"avatar"`
	Referee       string   `json:"referee"`
	Notify        int8     `json:"notify"`
	State         int8     `json:"state" validate:"oneof=0 1"`
	RoleIDs       []uint64 `json:"role_ids"`
	PermissionIDs []uint64 `json:"permission_ids"`
}

// AdminUserUpdateDTO 更新管理员用户的请求数据结构
type AdminUserUpdateDTO struct {
	Username      string   `json:"username" validate:"omitempty,min=3,max=120"`
	Password      string   `json:"password" validate:"omitempty,min=6,max=80"`
	Name          string   `json:"name"`
	Email         string   `json:"email" validate:"omitempty,email"`
	Avatar        string   `json:"avatar"`
	Referee       string   `json:"referee"`
	Notify        int8     `json:"notify"`
	State         int8     `json:"state" validate:"omitempty,oneof=0 1"`
	RoleIDs       []uint64 `json:"role_ids"`
	PermissionIDs []uint64 `json:"permission_ids"`
}

// AdminUserQueryDTO 查询管理员用户的请求参数
type AdminUserQueryDTO struct {
	Username string `query:"username"`
	Name     string `query:"name"`
	Email    string `query:"email"`
	State    int8   `query:"state"`
	Page     int    `query:"page" validate:"omitempty,min=1"`
	PageSize int    `query:"page_size" validate:"omitempty,min=1,max=100"`
}

// AdminUserResponse 管理员用户的响应数据结构
type AdminUserResponse struct {
	ID              uint64 `json:"id"`
	Username        string `json:"username"`
	Name            string `json:"name"`
	Email           string `json:"email"`
	Avatar          string `json:"avatar"`
	Referee         string `json:"referee"`
	Notify          int8   `json:"notify"`
	LastedIPAddress string `json:"lasted_ipaddress"`
	LastedAt        string `json:"lasted_at"`
	State           int8   `json:"state"`
	CreatedAt       string `json:"created_at"`
	UpdatedAt       string `json:"updated_at"`
}