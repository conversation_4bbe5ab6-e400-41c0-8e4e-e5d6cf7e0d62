package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupFriendlyRoutes 设置友情链接管理相关路由
func SetupFriendlyRoutes(router fiber.Router) {
	// 友情链接路由组
	friendlies := router.Group("/friendly")
	
	// 添加认证中间件
	friendlies.Use(utils.RequireAuthentication)

	// 友情链接CRUD路由
	friendlies.Get("/index", middleware.RequirePermission("friendly.view"), handlers.GetFriendlies)           // 获取友情链接列表
	friendlies.Get("/index/:id", middleware.RequirePermission("friendly.view"), handlers.GetFriendly)        // 获取单个友情链接
	friendlies.Post("/index", middleware.RequirePermission("friendly.create"), handlers.CreateFriendly)        // 创建友情链接
	friendlies.Put("/index/:id", middleware.RequirePermission("friendly.edit"), handlers.UpdateFriendly)     // 更新友情链接
	friendlies.Delete("/index/:id", middleware.RequirePermission("friendly.delete"), handlers.DeleteFriendly)  // 删除友情链接
} 