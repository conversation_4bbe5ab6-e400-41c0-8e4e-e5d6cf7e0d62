#!/bin/bash

# Go Vue Pro CentOS 7 生产环境部署脚本
# 使用88端口，外部Redis

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Go Vue Pro CentOS 7 生产环境部署脚本 ===${NC}"
echo -e "${YELLOW}配置: 88端口访问，使用服务器现有Redis${NC}"

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo -e "${YELLOW}警告: 建议不要使用root用户运行此脚本${NC}"
fi

# 检查Docker环境
echo -e "${YELLOW}检查Docker环境...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker 未安装${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${RED}错误: Docker Compose 未安装${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Docker 环境检查通过${NC}"

# 检查Redis连接
echo -e "${YELLOW}检查服务器Redis连接...${NC}"
if redis-cli -h 127.0.0.1 -p 6379 ping &> /dev/null; then
    echo -e "${GREEN}✓ Redis 连接正常${NC}"
else
    echo -e "${YELLOW}⚠ 无法连接到Redis，请确保Redis服务正在运行${NC}"
    echo -e "${YELLOW}如果Redis需要密码，请在.env.prod中配置REDIS_PASSWORD${NC}"
fi

# 进入项目目录
cd "$(dirname "$0")"
PROJECT_ROOT=$(pwd)
echo -e "${YELLOW}项目根目录: $PROJECT_ROOT${NC}"

# 检查必要文件
REQUIRED_FILES=(
    "docker/docker-compose.prod.yml"
    "docker/Dockerfile.server.prod"
    "docker/Dockerfile.nginx.prod"
    "docker/nginx.prod.conf"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo -e "${RED}错误: 缺少必要文件 $file${NC}"
        exit 1
    fi
done

echo -e "${GREEN}✓ 必要文件检查通过${NC}"
echo -e "${GREEN}✓ 使用Git克隆方式，无需检查本地源码文件${NC}"

# 检查环境变量文件
if [ ! -f "docker/.env.prod" ]; then
    echo -e "${YELLOW}创建生产环境配置文件...${NC}"
    if [ -f "docker/.env.prod.example" ]; then
        cp docker/.env.prod.example docker/.env.prod
        echo -e "${YELLOW}请编辑 docker/.env.prod 文件，设置正确的生产环境变量${NC}"
        echo -e "${CYAN}重要配置项:${NC}"
        echo -e "  DB_PASSWORD=强密码"
        echo -e "  JWT_SECRET=复杂密钥"
        echo -e "  REDIS_ADDR=127.0.0.1:6379"
        echo -e "  REDIS_PASSWORD=Redis密码(如果有)"
        echo -e "${YELLOW}按任意键继续...${NC}"
        read -n 1
    else
        echo -e "${RED}错误: 缺少 .env.prod.example 文件${NC}"
        exit 1
    fi
fi

# 进入docker目录
cd docker

# 停止现有容器
echo -e "${YELLOW}停止现有容器...${NC}"
docker-compose -f docker-compose.prod.yml down || true

# 清理旧镜像（可选）
read -p "是否清理旧的Docker镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}清理旧镜像...${NC}"
    docker system prune -f
fi

# 构建并启动服务
echo -e "${YELLOW}构建并启动生产环境服务...${NC}"
docker-compose -f docker-compose.prod.yml up -d --build

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 30

# 检查服务状态
echo -e "${YELLOW}检查服务状态...${NC}"
docker-compose -f docker-compose.prod.yml ps

# 健康检查
echo -e "${YELLOW}执行健康检查...${NC}"
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    if curl -f http://localhost:88/health &> /dev/null; then
        echo -e "${GREEN}✓ 应用健康检查通过${NC}"
        break
    fi
    
    attempt=$((attempt + 1))
    echo -e "${YELLOW}等待应用启动... ($attempt/$max_attempts)${NC}"
    sleep 10
done

if [ $attempt -eq $max_attempts ]; then
    echo -e "${RED}❌ 应用启动失败或健康检查超时${NC}"
    echo -e "${YELLOW}查看日志:${NC}"
    docker-compose -f docker-compose.prod.yml logs --tail=20
    exit 1
fi

# 检查防火墙设置
echo -e "${YELLOW}检查防火墙设置...${NC}"
if systemctl is-active --quiet firewalld; then
    if ! firewall-cmd --list-ports | grep -q "88/tcp"; then
        echo -e "${YELLOW}需要开放88端口，请运行:${NC}"
        echo -e "${CYAN}sudo firewall-cmd --permanent --add-port=88/tcp${NC}"
        echo -e "${CYAN}sudo firewall-cmd --reload${NC}"
        
        read -p "是否现在开放88端口? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo firewall-cmd --permanent --add-port=88/tcp
            sudo firewall-cmd --reload
            echo -e "${GREEN}✓ 88端口已开放${NC}"
        fi
    else
        echo -e "${GREEN}✓ 88端口已开放${NC}"
    fi
fi

# 获取服务器IP
SERVER_IP=$(hostname -I | awk '{print $1}')

# 显示部署结果
echo -e "\n${GREEN}🎉 生产环境部署成功！${NC}"
echo -e "\n${CYAN}=== 服务信息 ===${NC}"
echo -e "应用地址: http://$SERVER_IP:88"
echo -e "健康检查: http://$SERVER_IP:88/health"
echo -e "API接口: http://$SERVER_IP:88/api/"
echo -e "项目访问: http://$SERVER_IP:88/p/{项目ID}/"

echo -e "\n${CYAN}=== 管理命令 ===${NC}"
echo -e "查看日志: docker-compose -f docker-compose.prod.yml logs"
echo -e "停止服务: docker-compose -f docker-compose.prod.yml down"
echo -e "重启服务: docker-compose -f docker-compose.prod.yml restart"
echo -e "查看状态: docker-compose -f docker-compose.prod.yml ps"

echo -e "\n${CYAN}=== 安全提醒 ===${NC}"
echo -e "1. 防火墙已配置开放88端口"
echo -e "2. 使用服务器现有Redis服务"
echo -e "3. 定期备份MySQL数据"
echo -e "4. 监控系统资源使用情况"

echo -e "\n${GREEN}部署完成！请访问 http://$SERVER_IP:88 测试应用${NC}"
