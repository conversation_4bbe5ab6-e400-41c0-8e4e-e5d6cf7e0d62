package models

import (
	"time"

	"gorm.io/gorm"
)

// Permission 权限模型
type Permission struct {
	ID         uint64         `json:"id" gorm:"primaryKey;type:bigint unsigned"`
	Name       string         `json:"name" gorm:"type:varchar(50);not null;comment:权限名称"`
	Slug       string         `json:"slug" gorm:"type:varchar(50);not null;uniqueIndex:admin_permissions_slug_unique;comment:权限标识"`
	HttpMethod string         `json:"http_method" gorm:"column:http_method;type:varchar(255);comment:HTTP方法"`
	HttpPath   string         `json:"http_path" gorm:"column:http_path;type:text;comment:HTTP路径"`
	Order      int            `json:"order" gorm:"type:int;not null;default:0;comment:排序"`
	ParentID   uint64         `json:"parent_id" gorm:"default:0;comment:父ID"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	Roles    []Role        `gorm:"many2many:admin_role_permissions;foreignKey:ID;joinForeignKey:PermissionID;References:ID;joinReferences:RoleID" json:"roles,omitempty"`
	Menus    []Menu        `gorm:"many2many:admin_permission_menu;foreignKey:ID;joinForeignKey:PermissionID;References:ID;joinReferences:MenuID" json:"menus,omitempty"`
	Children []Permission  `gorm:"-" json:"children,omitempty"` 
}

// TableName 指定表名
func (Permission) TableName() string {
	return "admin_permissions"
}

// AfterFind 在查询后加载子权限
func (p *Permission) AfterFind(tx *gorm.DB) error {
    if p.ID > 0 {
        var children []Permission
        tx.Where("parent_id = ?", p.ID).Find(&children)
        p.Children = children
    }
    return nil
}