package dto

// RoleResponse 角色响应
type RoleResponse struct {
	ID              uint64 `json:"id"`
	Name            string `json:"name"`
	GuardName       string `json:"guard_name"`
	Remark          string `json:"remark"`
	PermissionCount int64  `json:"permission_count"`
}

// RoleCreateDTO 创建角色请求
type RoleCreateDTO struct {
	Name        string   `json:"name" validate:"required"`
	GuardName   string   `json:"guard_name" validate:"required"`
	Remark      string   `json:"remark"`
	PermissionIDs []uint64 `json:"permission_ids,omitempty"`
	MenuIDs     []uint64   `json:"menu_ids,omitempty"`
}

// RoleUpdateDTO 更新角色请求
type RoleUpdateDTO struct {
	Name        *string  `json:"name,omitempty"`
	GuardName   *string  `json:"guard_name,omitempty"`
	Remark      *string  `json:"remark,omitempty"`
	PermissionIDs []uint64 `json:"permission_ids,omitempty"`
	MenuIDs     []uint64   `json:"menu_ids,omitempty"`
}

// RoleQueryDTO 角色查询参数
type RoleQueryDTO struct {
	Name      string `query:"name"`
	GuardName string `query:"guard_name"`
	Page      int    `query:"page"`
	PageSize  int    `query:"page_size"`
}

// RolePermissionResponse 角色权限响应
type RolePermissionResponse struct {
	ID         uint64                   `json:"id"`
	Name       string                   `json:"name"`
	Slug       string                   `json:"slug"`
	HttpMethod string                   `json:"http_method"`
	HttpPath   string                   `json:"http_path"`
	Order      int                      `json:"order"`
	ParentID   uint64                   `json:"parent_id"`
	Children   []RolePermissionResponse `json:"children,omitempty"`
}

// RoleMenuResponse 角色菜单响应
type RoleMenuResponse struct {
	ID       uint64 `json:"id"`
	ParentID uint64 `json:"parent_id"`
	Order    uint64 `json:"order"`
	Title    string `json:"title"`
	Icon     string `json:"icon,omitempty"`
	URI      string `json:"uri,omitempty"`
}

// RoleDetailResponse 角色详情响应
type RoleDetailResponse struct {
	ID          uint64                  `json:"id"`
	Name        string                  `json:"name"`
	GuardName   string                  `json:"guard_name"`
	Remark      string                  `json:"remark"`
	Permissions []RolePermissionResponse `json:"permissions"`
	Menus       []RoleMenuResponse       `json:"menus"`
	IsAdmin     bool                    `json:"is_admin"` // 是否为超级管理员
} 