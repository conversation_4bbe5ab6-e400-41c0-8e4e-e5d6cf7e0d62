import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'

Vue.config.productionTip = false

// 使用ElementUI
Vue.use(ElementUI)

// 配置axios
Vue.prototype.$http = axios
// 配置请求基础URL
// axios.defaults.baseURL = 'http://api.example.com'

// 创建Vue实例前检查token有效性
async function initApp() {
  try {
    // 验证令牌有效性
    await store.dispatch('checkToken')
  } catch (error) {
    console.error('验证令牌时出错:', error)
    // 出错时清除认证状态
    store.commit('CLEAR_AUTH')
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  } finally {
    // 验证完成后再创建Vue实例
    new Vue({
      router,
      store,
      render: h => h(App)
    }).$mount('#app')
  }
}

// 启动应用
initApp() 