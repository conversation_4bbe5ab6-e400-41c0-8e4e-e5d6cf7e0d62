<template>
  <div class="module-create">
    <div v-loading="loading" class="create-container">
      <!-- 页头 -->
      <div class="page-header">
        <h1 class="page-title">{{ isEdit ? "编辑模块" : "创建模块" }}</h1>
        <el-button icon="el-icon-back" @click="$router.push('/module')"
          >返回列表</el-button
        >
      </div>

      <!-- 表单 -->
      <el-card shadow="hover" class="form-card">
        <el-form
          ref="moduleForm"
          :model="moduleForm"
          :rules="rules"
          label-width="100px"
          label-position="right"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="模块名称" prop="title">
                <el-input
                  v-model="moduleForm.title"
                  placeholder="请输入模块名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="安全级别" prop="level">
                <el-select
                  v-model="moduleForm.level"
                  placeholder="请选择安全级别"
                  style="width: 100%"
                >
                  <el-option label="默认 (0级)" :value="0"></el-option>
                  <el-option label="1级" :value="1"></el-option>
                  <el-option label="2级" :value="2"></el-option>
                  <el-option label="3级" :value="3"></el-option>
                  <el-option label="4级" :value="4"></el-option>
                  <el-option label="5级" :value="5"></el-option>
                  <el-option label="6级" :value="6"></el-option>
                  <el-option label="7级" :value="7"></el-option>
                  <el-option label="8级" :value="8"></el-option>
                  <el-option label="9级" :value="9"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="模块描述" prop="description">
            <el-input
              v-model="moduleForm.description"
              type="textarea"
              :rows="6"
              placeholder="请输入模块描述"
            ></el-input>
          </el-form-item>

          <el-form-item label="关键字" prop="keys">
            <div class="keys-container">
              <el-row :gutter="24" style="text-align: center">
                <el-col :span="9">
                  <div>参数名称</div>
                </el-col>
                <el-col :span="9">
                  <div>参数值</div>
                </el-col>
                <el-col :span="2">
                  <div>状态</div>
                </el-col>
                <el-col :span="2">
                  <div>操作</div>
                </el-col>
              </el-row>
              <el-row
                :gutter="24"
                v-for="(keyItem, index) in keysList"
                :key="index"
                style="text-align: center; margin-bottom: 10px"
              >
                <el-col :span="9">
                  <div class="key-item">
                    <el-input
                      v-model="keyItem.key"
                      placeholder="参数名称"
                      class="key-input"
                    ></el-input>
                  </div>
                </el-col>
                <el-col :span="9">
                  <div class="key-item">
                    <el-input
                      v-model="keyItem.value"
                      placeholder="参数值"
                      class="key-input"
                    ></el-input>
                  </div>
                </el-col>
                <el-col :span="2">
                  <div class="key-item">
                    <el-switch
                      v-model="keyItem.state"
                      :active-value="1"
                      :inactive-value="0"
                      class="key-switch"
                    ></el-switch>
                  </div>
                </el-col>
                <el-col :span="2">
                  <div class="key-item">
                    <el-button
                      type="danger"
                      icon="el-icon-delete"
                      circle
                      size="mini"
                      @click="removeKeyItem(index)"
                    ></el-button>
                  </div>
                </el-col>
              </el-row>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="addKeyItem"
                >添加关键字</el-button
              >
              <div class="help-text">需要服务器接收的参数名</div>
            </div>
          </el-form-item>

          <el-form-item label="配置参数" prop="setkeys">
            <div class="setkeys-container">
              <div
                v-for="(setKeyItem, index) in setKeysList"
                :key="index"
                class="setkey-item"
              >
                <el-input
                  v-model="setKeyItem.key"
                  placeholder="参数名称"
                  class="key-input"
                ></el-input>
                <el-input
                  v-model="setKeyItem.value"
                  placeholder="参数值"
                  class="key-input"
                ></el-input>
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  size="mini"
                  @click="removeSetKeyItem(index)"
                ></el-button>
              </div>
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="addSetKeyItem"
                >添加配置参数</el-button
              >
              <div class="help-text">配置参数用于设置模块的配置选项</div>
              <div class="help-text">
                使用方法：在模块代码中使用 {set.参数名} 语法，保存时会自动替换为对应的参数值
              </div>
              <div class="help-text">
                例如：参数名为"User"，值为"admin"，则代码中的 {set.User} 会被替换为 "admin"
              </div>
            </div>
          </el-form-item>

          <el-form-item label="模块代码" prop="code">
            <el-input
              v-model="moduleForm.code"
              type="textarea"
              :rows="10"
              placeholder="请输入JavaScript代码"
              class="code-editor"
            ></el-input>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="moduleForm.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入备注信息（可选）"
            ></el-input>
          </el-form-item>

          <el-form-item label="状态">
            <el-switch
              v-model="moduleForm.state"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>

          <el-form-item label="共享状态" prop="is_share">
            <el-switch
              v-model="moduleForm.is_share"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="submitForm">{{
              isEdit ? "保存更新" : "创建模块"
            }}</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getModule, createModule, updateModule } from "@/api/module";

export default {
  name: "ModuleCreate",
  data() {
    return {
      loading: false,
      isEdit: false,
      moduleId: null,
      keysList: [{ key: "", value: "", state: 1 }],
      setKeysList: [{ key: "", value: "" }],
      // 表单数据
      moduleForm: {
        title: "",
        description: "",
        code: "",
        level: 1,
        state: 1,
        is_share: 0, // 默认为非公开
        keys: "",
        setkeys: "",
        remark: "",
      },
      // 表单验证规则
      rules: {
        title: [
          { required: true, message: "请输入模块名称", trigger: "blur" },
          {
            min: 3,
            max: 50,
            message: "长度在 3 到 50 个字符",
            trigger: "blur",
          },
        ],
        description: [
          { required: true, message: "请输入模块描述", trigger: "blur" },
          { max: 200, message: "最多200个字符", trigger: "blur" },
        ],
        keys: [{ max: 200, message: "最多200个字符", trigger: "blur" }],
        setkeys: [{ max: 500, message: "最多500个字符", trigger: "blur" }],
        code: [{ required: true, message: "请输入模块代码", trigger: "blur" }],
        level: [
          { required: true, message: "请选择安全级别", trigger: "change" },
        ],
        is_share: [
          { required: true, message: "请选择共享状态", trigger: "change" },
        ],
      },
    };
  },
  created() {
    // 检查是否是编辑模式
    const id = this.$route.query.id;
    if (id) {
      this.isEdit = true;
      this.moduleId = id;
      this.fetchModuleDetail();
    }
  },
  methods: {
    // 添加关键字项
    addKeyItem() {
      this.keysList.push({ key: "", value: "", state: 1 });
    },
    // 删除关键字项
    removeKeyItem(index) {
      this.keysList.splice(index, 1);
      if (this.keysList.length === 0) {
        this.addKeyItem();
      }
    },
    // 添加配置参数项
    addSetKeyItem() {
      this.setKeysList.push({ key: "", value: "" });
    },
    // 删除配置参数项
    removeSetKeyItem(index) {
      this.setKeysList.splice(index, 1);
      if (this.setKeysList.length === 0) {
        this.addSetKeyItem();
      }
    },
    // 处理表单提交前的数据转换
    processFormBeforeSubmit() {
      // 处理关键字
      const keysObj = {};
      this.keysList.forEach((item) => {
        if (item.key && item.value) {
          keysObj[item.key] = {
            value: item.value,
            state: item.state,
          };
        }
      });
      this.moduleForm.keys = JSON.stringify(keysObj);

      // 处理配置参数
      const setKeysObj = {};
      this.setKeysList.forEach((item) => {
        if (item.key && item.value) {
          setKeysObj[item.key] = item.value;
        }
      });
      this.moduleForm.setkeys = JSON.stringify(setKeysObj);
      if (!this.moduleForm.code) return;

      let code = this.moduleForm.code;

      // 遍历所有配置参数，替换代码中的 {set.参数名}
      for (const key in setKeysObj) {
        if (Object.prototype.hasOwnProperty.call(setKeysObj, key)) {
          const pattern = new RegExp(`\\{set\\.${key}\\}`, 'g');
          code = code.replace(pattern, setKeysObj[key]);
        }
      }

      // 注意：这里我们创建一个副本用于提交，而不是直接修改原始代码
      // 这样用户在界面上看到的仍然是带有 {set.参数名} 的模板代码
      return code;
    },

    // 解析已有数据为表单项
    parseExistingData() {
      // 解析关键字
      try {
        if (this.moduleForm.keys) {
          const keysObj = JSON.parse(this.moduleForm.keys);

          this.keysList = [];

          // keys的格式应该是 {"参数名": {"value": "参数值", "state": 状态}} 的形式
          for (const key in keysObj) {
            if (Object.prototype.hasOwnProperty.call(keysObj, key)) {
              const item = keysObj[key];
              this.keysList.push({
                key: key,
                value: item.value || "",
                state: item.state || 0,
              });
            }
          }

          // 如果没有数据，添加一个空项
          if (this.keysList.length === 0) {
            this.addKeyItem();
          }
        }
      } catch (e) {
        console.error("解析关键字失败", e);
        this.keysList = [{ key: "", value: "", state: 1 }];
      }

      // 解析配置参数
      try {
        if (this.moduleForm.setkeys) {
          const setKeysObj = JSON.parse(this.moduleForm.setkeys);
          this.setKeysList = [];

          // setkeys的格式应该是 {"参数名": "参数值"} 的形式
          for (const key in setKeysObj) {
            if (Object.prototype.hasOwnProperty.call(setKeysObj, key)) {
              this.setKeysList.push({
                key: key,
                value: setKeysObj[key] || "",
              });
            }
          }

          // 如果没有数据，添加一个空项
          if (this.setKeysList.length === 0) {
            this.addSetKeyItem();
          }
        }

      
      } catch (e) {
        console.error("解析配置参数失败", e);
        this.setKeysList = [{ key: "", value: "" }];
      }
    },

    // 获取模块详情
    fetchModuleDetail() {
      if (!this.moduleId) return;

      this.loading = true;
      getModule(this.moduleId)
        .then((response) => {
          if (response) {
            

            // 更新表单数据
            this.moduleForm = {
              title: response.title || "",
              description: response.description || "",
              code: response.original_code || "",
              level: response.level || 0,
              state: response.state || 1,
              is_share: response.is_share || 0,
              keys: response.keys || "",
              setkeys: response.setkeys || "",
              remark: response.remark || "",
            };
            this.parseExistingData();
            // 解析keys字段
            console.log(this.moduleForm);
          }
        })
        .catch((error) => {
          this.$message.error(
            `获取模块详情失败: ${error.message || "未知错误"}`
          );
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 提交表单
    submitForm() {
      this.$refs.moduleForm.validate((valid) => {
        if (!valid) {
          this.$message.warning("请正确填写所有必填字段");
          return false;
        }

        this.loading = true;

        // 处理数据转换，保存原始代码
        const originalCode = this.processFormBeforeSubmit();

        // 根据是否编辑模式调用不同API
        const apiRequest = this.isEdit
          ? updateModule(this.moduleId, this.moduleForm)
          : createModule(this.moduleForm);

        apiRequest
          .then(() => {
            this.$message.success(
              this.isEdit ? "模块更新成功" : "模块创建成功"
            );

            if (!this.isEdit) {
              // 创建成功后清空表单
              this.resetForm();
            } else {
              // 编辑成功后返回列表或详情页
              this.$router.push(`/module/${this.moduleId}`);
            }
          })
          .catch((error) => {
            this.$message.error(
              `${this.isEdit ? "更新" : "创建"}失败: ${
                error.message || "未知错误"
              }`
            );
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
    // 重置表单
    resetForm() {
      if (this.isEdit && this.moduleId) {
        // 编辑模式下，重置为原始数据
        this.fetchModuleDetail();
      } else {
        // 创建模式下，清空表单
        this.$refs.moduleForm.resetFields();
        this.moduleForm = {
          title: "",
          description: "",
          code: "",
          original_code: "",
          level: 0,
          state: 1,
          is_share: 0,
          keys: "",
          setkeys: "",
          remark: "",
        };
        this.keysList = [{ key: "", value: "", state: 1 }];
        this.setKeysList = [{ key: "", value: "" }];
      }
    },
  },
};
</script>

<style scoped>
.module-create {
  padding: 20px;
}

.create-container {
  width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
}

.form-card {
  margin-bottom: 20px;
}

.code-editor {
  font-family: "Courier New", Courier, monospace;
}

.keys-container {
  width: 100%;
}

.key-item {
  display: flex;
  justify-content: center;
}

.key-input {
  flex: 1;
}

.key-switch {
  width: 100px;
  margin-right: 10px;
}

.help-text {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}

.setkeys-container {
  width: 100%;
}

.setkey-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}
</style>
