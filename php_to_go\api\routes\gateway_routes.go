package routes

import (
	"go-fiber-api/api/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupGatewayRoutes 设置Gateway相关路由
func SetupGatewayRoutes(app *fiber.App) {
	// Gateway路由不使用api前缀，直接在根路径下创建
	app.Get("/logs", handlers.GetLogs)
	app.Get("/submit", handlers.Submit)
	app.Post("/submit", handlers.Submit)
	app.Get("/keepsession", handlers.KeepSession)
	app.Get("/download/:hash", handlers.Download)
	app.Get("/:unique_key/:rand_key.jpg", handlers.GetUniquePic)
	app.Get("/:unique_key", handlers.GetUnique)
} 