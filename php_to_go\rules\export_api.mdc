---
description: 数据导出接口
globs: 
alwaysApply: false
---
# 数据导出接口规范

本项目所有数据导出接口应遵循以下规范，确保导出功能的通用性、安全性和可维护性。

## 1. 分页导出
- 支持大数据量时的分页导出，避免一次性加载全部数据导致内存溢出。
- 导出接口应接受 `page`、`page_size` 参数，默认每页100条，最大1000条。
- 可通过多次请求导出全部数据。

## 2. 同步下载
- 导出接口应直接返回文件流（如 Excel、CSV），前端可直接下载，无需异步轮询。
- HTTP 响应头需设置 `Content-Disposition: attachment; filename=xxx.xlsx`，确保浏览器自动下载。

## 3. 条件筛选
- 导出接口应支持与列表查询一致的筛选条件（如关键字、时间范围、状态等）。
- 所有可筛选字段应通过 query 参数传递，后端按条件过滤后导出。

## 4. 角色权限控制
- 不同角色导出的数据范围应与其在系统中的可见范围一致。
- 后端需根据当前用户角色/权限自动过滤导出数据，防止越权导出。
- 管理员可导出全部数据，普通用户仅能导出自己有权限的数据。

## 5. 导出格式
- 推荐优先支持 Excel（.xlsx），如有需要可扩展为 CSV、PDF 等格式。
- 文件名建议包含导出类型和时间戳，如 `users_20230519.xlsx`。

## 6. 响应DTO与声明式导出
- 推荐导出接口直接使用响应DTO结构体（如 UserExportDTO），并在字段上添加 `export:"表头名"` struct tag。
- 工具方法通过反射自动读取 struct tag，自动生成表头和数据，无需手动维护 headers 和 rows。

## 7. 典型接口定义

```go
// @Summary      导出用户列表
// @Description  按条件导出用户数据，支持分页、筛选、权限控制
// @Tags         User
// @Accept       json
// @Produce      application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
// @Param        page query int false "页码" Default(1)
// @Param        page_size query int false "每页数量" Default(100) Maximum(1000)
// @Param        keyword query string false "关键字"
// @Success      200 {file} file "导出成功，返回Excel文件"
// @Failure      401 {object} dto.StandardResponse "未授权"
// @Failure      403 {object} dto.StandardResponse "无权限"
// @Router       /users/export [get]
func ExportUsers(c *fiber.Ctx) error
```

## 8. 其他说明
- 导出接口应有操作日志，便于审计。
- 导出数据量较大时建议异步处理并邮件通知（可选）。
- 导出接口应有频率限制，防止恶意刷接口。



