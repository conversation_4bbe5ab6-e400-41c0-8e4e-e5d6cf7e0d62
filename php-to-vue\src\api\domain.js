import request from './index'

/**
 * 获取域名列表
 * @param {Object} params - 查询参数
 * @param {string} [params.domain] - 域名(模糊查询)
 * @param {number} [params.type] - 类型：10 过滤域名/20 切换使用的域名
 * @param {number} [params.state] - 状态(1:启用, 0:禁用, 不传:所有)
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getDomainList(params) {
  return request({
    url: '/admin/domain/index',
    method: 'get',
    params
  })
}

/**
 * 添加域名
 * @param {Object} data - 域名数据
 * @param {string} data.domain - 域名
 * @param {string} [data.description] - 描述
 * @param {number} [data.state=1] - 状态(1:启用, 0:禁用)
 * @param {number} [data.type=10] - 类型：10 过滤域名/20 切换使用的域名
 * @returns {Promise}
 */
export function createDomain(data) {
  return request({
    url: '/admin/domain/index',
    method: 'post',
    data
  })
}

/**
 * 获取单个域名
 * @param {number} id - 域名ID
 * @returns {Promise}
 */
export function getDomain(id) {
  return request({
    url: `/admin/domain/index/${id}`,
    method: 'get'
  })
}

/**
 * 更新域名
 * @param {number} id - 域名ID
 * @param {Object} data - 需要更新的域名数据
 * @param {string} [data.domain] - 域名
 * @param {string} [data.description] - 描述
 * @param {number} [data.state] - 状态(1:启用, 0:禁用)
 * @param {number} [data.type] - 类型：10 过滤域名/20 切换使用的域名
 * @returns {Promise}
 */
export function updateDomain(id, data) {
  return request({
    url: `/admin/domain/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除域名
 * @param {number} id - 域名ID
 * @returns {Promise}
 */
export function deleteDomain(id) {
  return request({
    url: `/admin/domain/index/${id}`,
    method: 'delete'
  })
}

/**
 * 批量导入域名
 * @param {Object} data - 批量导入数据
 * @param {string} data.domains - 域名列表，以换行符分隔
 * @param {number} [data.type=10] - 类型：10 过滤域名/20 切换使用的域名
 * @param {number} [data.state=1] - 状态(1:启用, 0:禁用)
 * @returns {Promise}
 */
export function batchImportDomains(data) {
  // 由于后端没有提供批量导入接口，我们在前端将域名列表拆分并使用单个创建接口
  const domains = data.domains.split('\n').filter(domain => domain.trim() !== '');
  const type = data.type || 10;
  const state = data.state || 1;
  
  // 创建一个Promise数组，每个域名创建一个请求
  const promises = domains.map(domain => {
    return request({
      url: '/admin/domain/index',
      method: 'post',
      data: {
        domain: domain.trim(),
        type,
        state,
        description: `批量导入: ${domain.trim()}`
      }
    });
  });
  
  // 返回Promise.all，等待所有请求完成
  return Promise.all(promises);
}

/**
 * 检查域名是否在过滤列表中
 * @param {string} domain - 需要检查的域名
 * @returns {Promise}
 */
export function checkDomain(domain) {
  return request({
    url: '/admin/domain/check',
    method: 'get',
    params: { domain }
  })
} 