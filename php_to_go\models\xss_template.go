package models

import (
	"time"

	"gorm.io/gorm"
)

// XssTemplate XSS模板模型
type XssTemplate struct {
	ID        uint64         `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	ProjectID uint64         `json:"project_id" gorm:"not null;index:idx_xss_templates_project_id;comment:项目ID"`
	Title     string         `json:"title" gorm:"type:varchar(255);not null;comment:模板标题"`
	Content   string         `json:"content" gorm:"type:text;comment:模板内容"`
	CreatedAt time.Time      `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"comment:更新时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index;comment:删除时间"`

	// 关联关系
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

// TableName 指定表名
func (XssTemplate) TableName() string {
	return "xss_templates"
}
