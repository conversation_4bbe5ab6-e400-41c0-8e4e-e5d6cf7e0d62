import request from './index'

/**
 * 获取菜单列表
 * @param {Object} params - 查询参数
 * @param {string} [params.title] - 菜单标题(模糊搜索)
 * @returns {Promise}
 */
export function getMenuList(params) {
  return request({
    url: '/admin/menu/index',
    method: 'get',
    params
  })
}

/**
 * 获取当前用户有权限访问的菜单树
 * 该接口会根据用户角色自动过滤菜单项
 * @returns {Promise}
 */
export function getUserMenuTree() {
  return request({
    url: '/admin/menu/tree',
    method: 'get'
  })
}

/**
 * 创建菜单
 * @param {Object} data - 菜单数据
 * @param {number} [data.parent_id] - 父菜单ID
 * @param {number} [data.order] - 排序
 * @param {string} data.title - 菜单标题
 * @param {string} [data.icon] - 菜单图标
 * @param {string} [data.uri] - 菜单URI
 * @param {Array<number>} [data.role_ids] - 角色ID列表
 * @param {Array<number>} [data.permission_ids] - 权限ID列表
 * @returns {Promise}
 */
export function createMenu(data) {
  return request({
    url: '/admin/menu/index',
    method: 'post',
    data
  })
}

/**
 * 获取菜单详情
 * @param {number} id - 菜单ID
 * @returns {Promise}
 */
export function getMenuDetail(id) {
  return request({
    url: `/admin/menu/index/${id}`,
    method: 'get'
  })
}

/**
 * 更新菜单
 * @param {number} id - 菜单ID
 * @param {Object} data - 更新数据
 * @param {number} [data.parent_id] - 父菜单ID
 * @param {number} [data.order] - 排序
 * @param {string} [data.title] - 菜单标题
 * @param {string} [data.icon] - 菜单图标
 * @param {string} [data.uri] - 菜单URI
 * @param {Array<number>} [data.role_ids] - 角色ID列表
 * @param {Array<number>} [data.permission_ids] - 权限ID列表
 * @returns {Promise}
 */
export function updateMenu(id, data) {
  return request({
    url: `/admin/menu/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除菜单
 * @param {number} id - 菜单ID
 * @returns {Promise}
 */
export function deleteMenu(id) {
  return request({
    url: `/admin/menu/index/${id}`,
    method: 'delete'
  })
} 