package dto

// JobResponse 任务响应
type JobResponse struct {
	ID          uint64 `json:"id"`
	Queue       string `json:"queue"`
	Payload     string `json:"payload"`
	Attempts    uint8  `json:"attempts"`
	ReservedAt  *uint  `json:"reserved_at"`
	AvailableAt uint   `json:"available_at"`
	CreatedAt   uint   `json:"created_at"`
}

// JobListItem 任务列表项
type JobListItem struct {
	ID          uint64 `json:"id"`
	Queue       string `json:"queue"`
	Attempts    uint8  `json:"attempts"`
	ReservedAt  *uint  `json:"reserved_at"`
	AvailableAt uint   `json:"available_at"`
	CreatedAt   uint   `json:"created_at"`
}

// JobCreateRequest 创建任务请求
type JobCreateRequest struct {
	Queue       string `json:"queue" validate:"required"`
	Payload     string `json:"payload" validate:"required"`
	Attempts    uint8  `json:"attempts" validate:"required"`
	AvailableAt uint   `json:"available_at" validate:"required"`
}

// JobUpdateRequest 更新任务请求
type JobUpdateRequest struct {
	Queue       *string `json:"queue,omitempty"`
	Payload     *string `json:"payload,omitempty"`
	Attempts    *uint8  `json:"attempts,omitempty"`
	ReservedAt  *uint   `json:"reserved_at,omitempty"`
	AvailableAt *uint   `json:"available_at,omitempty"`
}

// JobQuery 任务查询参数
type JobQuery struct {
	Queue      *string  `query:"queue,omitempty"`
	Attempts   *int     `query:"attempts,omitempty"`
	IsReserved *bool    `query:"is_reserved,omitempty"`
	Page       *int     `query:"page,omitempty"`
	PageSize   *int     `query:"page_size,omitempty"`
} 