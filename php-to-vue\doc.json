{
    "schemes": [
        "http",
        "https"
    ],
    "swagger": "2.0",
    "info": {
        "description": "这是使用Go Fiber构建的API服务.",
        "title": "Go Fiber API",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "1.0"
    },
    "host": "localhost:3000",
    "basePath": "/api",
    "paths": {
        "/admin/permissions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统中的所有权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "获取权限列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "权限名称(模糊搜索)",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "父权限ID",
                        "name": "parent_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.PermissionResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建一个新的权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "创建权限",
                "parameters": [
                    {
                        "description": "权限信息",
                        "name": "permission",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.PermissionCreateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.PermissionResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/permissions/batch-delete": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "批量删除指定ID的权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "批量删除权限",
                "parameters": [
                    {
                        "description": "权限ID列表",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/permissions/check/{slug}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "检查当前用户是否有特定的权限标识",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "检查用户权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "权限标识",
                        "name": "slug",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "boolean"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/permissions/tree": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统中的所有权限，以树形结构返回",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "获取权限树",
                "parameters": [
                    {
                        "type": "string",
                        "description": "排除的权限ID，多个ID用逗号分隔",
                        "name": "exclude_ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.PermissionResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/permissions/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定ID的权限详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "获取权限详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "权限ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.PermissionDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "权限不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新已存在的权限信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "更新权限",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "权限ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "权限更新信息",
                        "name": "permission",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.PermissionUpdateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.PermissionResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "权限不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除指定的权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "删除权限",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "权限ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "权限不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/all": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统中的所有角色，不分页",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取所有角色",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.RoleResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/role/batch-delete": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "批量删除指定ID的角色(管理员角色除外)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "批量删除角色",
                "parameters": [
                    {
                        "description": "角色ID列表",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取角色列表，支持分页和筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取角色列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "角色名称(模糊搜索)",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Guard名称",
                        "name": "guard_name",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.RoleResponse"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "创建角色",
                "parameters": [
                    {
                        "description": "角色信息",
                        "name": "role",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.RoleCreateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.RoleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取角色详情，包含权限树形结构",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取角色详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.RoleDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "更新角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "角色更新信息",
                        "name": "role",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.RoleUpdateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.RoleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除现有角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "删除角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/{id}/menus": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "批量分配菜单给指定角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "分配菜单给角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "菜单ID列表",
                        "name": "menu_ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分配成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/{id}/permissions": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "批量分配权限给指定角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "分配权限给角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "权限ID列表",
                        "name": "permission_ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分配成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/{id}/users": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取拥有指定角色的用户列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取角色用户列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.UserResponse"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "将指定角色分配给多个用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "分配角色给用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户ID列表",
                        "name": "user_ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分配成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/{id}/users/remove": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "从多个用户移除指定角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "从用户移除角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户ID列表",
                        "name": "user_ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "移除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/upload/avatar": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "上传用户头像并返回URL",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "上传头像",
                "parameters": [
                    {
                        "type": "file",
                        "description": "头像图片文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "上传成功，返回头像URL",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.AvatarResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误或文件类型/大小不符合要求",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/users": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取管理员用户列表，支持分页和按用户名、姓名、邮箱、状态过滤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "获取管理员用户列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名 (模糊查询)",
                        "name": "username",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "姓名 (模糊查询)",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "邮箱 (模糊查询)",
                        "name": "email",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态 (1:启用, 其他或不传:所有)",
                        "name": "state",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功响应",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.AdminUserResponse"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效的查询参数",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建一个新的管理员用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "创建管理员用户",
                "parameters": [
                    {
                        "description": "创建用户请求体",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AdminUserCreateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功，返回用户ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "properties": {
                                                "id": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效的请求数据或用户名已存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/users/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取管理员用户的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "获取单个管理员用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功响应",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.AdminUserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "用户未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID更新管理员用户的信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "更新管理员用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新用户请求体 (只需提供要更新的字段)",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AdminUserUpdateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "无效的用户ID、请求数据或用户名已被使用",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "用户未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID删除管理员用户 (软删除)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "删除管理员用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "用户未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
    },
    "definitions": {
       
        "dto.AvatarResponse": {
            "type": "object",
            "properties": {
                "url": {
                    "description": "上传成功后的头像URL",
                    "type": "string"
                }
            }
        },
        "dto.BatchIDsDTO": {
            "type": "object",
            "required": [
                "ids"
            ],
            "properties": {
                "ids": {
                    "description": "ID列表",
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
       
        
        "dto.OperationLogResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "id": {
                    "description": "日志ID",
                    "type": "integer"
                },
                "input": {
                    "description": "输入数据",
                    "type": "string"
                },
                "ip": {
                    "description": "IP地址",
                    "type": "string"
                },
                "method": {
                    "description": "HTTP方法",
                    "type": "string"
                },
                "path": {
                    "description": "操作路径",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "integer"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "dto.PaginatedResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "description": "当前页数据项"
                },
                "page": {
                    "description": "当前页码",
                    "type": "integer"
                },
                "page_size": {
                    "description": "每页大小",
                    "type": "integer"
                },
                "total": {
                    "description": "总记录数",
                    "type": "integer"
                },
                "total_pages": {
                    "description": "总页数",
                    "type": "integer"
                }
            }
        },
        "dto.PermissionCreateDTO": {
            "type": "object",
            "required": [
                "name",
                "slug"
            ],
            "properties": {
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string",
                    "maxLength": 50
                },
                "order": {
                    "type": "integer",
                    "default": 0
                },
                "parent_id": {
                    "type": "integer",
                    "default": 0
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "slug": {
                    "type": "string",
                    "maxLength": 50
                }
            }
        },
        "dto.PermissionDetailResponse": {
            "type": "object",
            "properties": {
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "slug": {
                    "type": "string"
                }
            }
        },
        "dto.PermissionResponse": {
            "type": "object",
            "properties": {
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dto.PermissionResponse"
                    }
                },
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "slug": {
                    "type": "string"
                }
            }
        },
        "dto.PermissionUpdateDTO": {
            "type": "object",
            "required": [
                "name",
                "slug"
            ],
            "properties": {
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string",
                    "maxLength": 50
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "slug": {
                    "type": "string",
                    "maxLength": 50
                }
            }
        },
        "dto.ProfileResponse": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "permissions": {
                    "description": "用户权限列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "role_slugs": {
                    "description": "用户角色标识列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dto.RoleItem"
                    }
                },
                "username": {
                    "type": "string"
                }
            }
        },
      
        "dto.RoleCreateDTO": {
            "type": "object",
            "required": [
                "guard_name",
                "name"
            ],
            "properties": {
                "guard_name": {
                    "type": "string"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string"
                },
                "permission_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "remark": {
                    "type": "string"
                }
            }
        },
        "dto.RoleDetailResponse": {
            "type": "object",
            "properties": {
                "guard_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_admin": {
                    "description": "是否为超级管理员",
                    "type": "boolean"
                },
                "menus": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dto.RoleMenuResponse"
                    }
                },
                "name": {
                    "type": "string"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dto.RolePermissionResponse"
                    }
                },
                "remark": {
                    "type": "string"
                }
            }
        },
        "dto.RoleItem": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "slug": {
                    "type": "string"
                }
            }
        },
        "dto.RoleMenuResponse": {
            "type": "object",
            "properties": {
                "icon": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "uri": {
                    "type": "string"
                }
            }
        },
        "dto.RolePermissionResponse": {
            "type": "object",
            "properties": {
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "slug": {
                    "type": "string"
                }
            }
        },
        "dto.RoleResponse": {
            "type": "object",
            "properties": {
                "guard_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "permission_count": {
                    "type": "integer"
                },
                "remark": {
                    "type": "string"
                }
            }
        },
        "dto.RoleUpdateDTO": {
            "type": "object",
            "properties": {
                "guard_name": {
                    "type": "string"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string"
                },
                "permission_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "remark": {
                    "type": "string"
                }
            }
        },
       
        "dto.StandardResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "业务状态码，0表示成功",
                    "type": "integer"
                },
                "data": {
                    "description": "响应数据"
                },
                "error": {
                    "description": "错误信息，仅在开发环境显示",
                    "type": "string"
                },
                "message": {
                    "description": "响应消息",
                    "type": "string"
                }
            }
        },
        "dto.SystemInfoResponse": {
            "type": "object",
            "properties": {
                "database_version": {
                    "description": "数据库版本",
                    "type": "string"
                },
                "go_version": {
                    "description": "Go版本",
                    "type": "string"
                },
                "server_time": {
                    "description": "服务器时间",
                    "type": "string"
                },
                "uptime": {
                    "description": "运行时间",
                    "type": "string"
                },
                "version": {
                    "description": "系统版本",
                    "type": "string"
                }
            }
        },
        "dto.UpdateProfileDTO": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "email": {
                    "type": "string",
                    "maxLength": 100
                },
                "name": {
                    "type": "string",
                    "maxLength": 50
                }
            }
        },
        "dto.UserResponse": {
            "type": "object",
            "properties": {
                "avatar": {
                    "description": "头像",
                    "type": "string"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string"
                },
                "id": {
                    "description": "用户ID",
                    "type": "integer"
                },
                "name": {
                    "description": "姓名",
                    "type": "string"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        }
    },
}