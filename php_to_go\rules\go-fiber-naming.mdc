---
description: 
globs: *.go
alwaysApply: false
---
# Go Fiber 命名规范

本文档规定了Go Fiber项目的命名规范和编码风格。

## 文件命名

- 包含多个单词的文件名应使用下划线命名法，如：`user_handler.go`, `auth_middleware.go`
- 测试文件应以`_test.go`结尾，如：`user_service_test.go`

## 包命名

- 包名应该简短、清晰且全小写
- 避免使用下划线或混合大小写
- 包名通常是单数形式，如`handler`、`model`、`service`
- 包名应与目录名匹配

## 变量命名

- 局部变量：使用驼峰式命名法，如`userID`, `emailAddress`
- 全局变量：使用驼峰式命名法，如首字母大写的`UserCount`
- 常量：使用全大写下划线分隔命名法，如`MAX_RETRY_COUNT`

## 接口命名

- 接口名应以er结尾，如`Reader`, `Writer`, `Logger`
- 单方法接口的名称应该是方法名加上-er后缀，如`Reader`接口中的`Read`方法

## 结构体命名

- 结构体名称应使用大驼峰命名法，如`UserService`, `AuthMiddleware`
- 字段名也应使用大驼峰命名法
- 数据库模型结构体应与表名相关，通常是单数形式，如`User`对应`users`表

## 函数命名

- 函数和方法应使用大驼峰命名法，如`GetAllUsers`, `CreateUser`
- 函数名应表明其功能和用途
- Get/Set方法通常应以属性名开头，如`User.GetName()`

## 错误处理

- 错误变量应以`Err`或`err`作为前缀，如`ErrNotFound`
- 自定义错误类型应以`Error`结尾，如`ValidationError`

## 路由命名

- API路由应遵循RESTful原则
- 使用复数表示资源集合，如`/api/users`而不是`/api/user`
- 使用HTTP方法表示操作，如GET获取，POST创建，PUT更新，DELETE删除
- 使用嵌套路径表示从属关系，如`/api/users/:id/posts`

## 注释规范

- 包注释：每个包应有一个包注释，放在package语句前
- 函数/方法注释：每个导出的函数和方法应有注释
- 复杂逻辑注释：对于复杂的逻辑段应添加注释说明
- 使用标准的Go注释格式，以函数/方法名开头

## 导入规则

导入语句应分组排序为：
1. 标准库导入
2. 第三方库导入
3. 内部包导入


