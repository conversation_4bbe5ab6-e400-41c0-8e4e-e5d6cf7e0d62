package dto

// InvitationListItem 邀请码列表项
type InvitationListItem struct {
	ID          uint64 `json:"id"`          // 邀请码ID
	Code        string `json:"code"`        // 邀请码
	Description string `json:"description"` // 邀请码描述
	UserID      uint64 `json:"user_id"`     // 创建用户ID
	UsedAt      string `json:"used_at"`     // 使用时间
	ExpiredAt   string `json:"expired_at"`  // 过期时间
	State       int8   `json:"state"`       // 状态
	UsedCount   uint   `json:"used_count"`  // 使用次数
	Limit       uint   `json:"limit"`       // 使用次数限制，0表示不限制
	CreatedAt   string `json:"created_at"`  // 创建时间
}

// InvitationResponse 邀请码详情
type InvitationResponse struct {
	ID          uint64 `json:"id"`          // 邀请码ID
	Code        string `json:"code"`        // 邀请码
	Description string `json:"description"` // 邀请码描述
	UserID      uint64 `json:"user_id"`     // 创建用户ID
	UsedAt      string `json:"used_at"`     // 使用时间
	ExpiredAt   string `json:"expired_at"`  // 过期时间
	State       int8   `json:"state"`       // 状态
	UsedCount   uint   `json:"used_count"`  // 使用次数
	Limit       uint   `json:"limit"`       // 使用次数限制，0表示不限制
	CreatedAt   string `json:"created_at"`  // 创建时间
	UpdatedAt   string `json:"updated_at"`  // 更新时间
}

// InvitationRequest 邀请码创建请求
type InvitationRequest struct {
	Code        string `json:"code" validate:"required"` // 邀请码
	Description string `json:"description"`              // 邀请码描述
	State       int8   `json:"state" validate:"min=0,max=1"` // 状态
	Count       int    `json:"count" validate:"min=1,max=100"` // 批量生成数量
	Limit       uint   `json:"limit"`                    // 使用次数限制，0表示不限制
	ExpiredAt   string `json:"expired_at"`               // 过期时间，不传表示不设置
}

// InvitationUpdateRequest 邀请码更新请求
type InvitationUpdateRequest struct {
	Code        *string `json:"code"`                            // 邀请码
	Description *string `json:"description"`                     // 邀请码描述
	State       *int8   `json:"state" validate:"omitempty,min=0,max=1"` // 状态
	Limit       *uint   `json:"limit"`                           // 使用次数限制，0表示不限制
	ExpiredAt   *string `json:"expired_at"`                      // 过期时间，不传表示不设置
}

// InvitationBatchRequest 邀请码批量操作请求
type InvitationBatchRequest struct {
	IDs    []uint64 `json:"ids" validate:"required"`    // 邀请码ID列表
	Action string   `json:"action" validate:"required,oneof=enable disable delete"` // 操作类型
} 