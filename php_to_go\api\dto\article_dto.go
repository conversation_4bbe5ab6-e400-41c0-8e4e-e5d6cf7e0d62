package dto

// ArticleRequest 公告请求DTO
type ArticleRequest struct {
	Title       string `json:"title" validate:"required,min=2,max=255"`
	Description string `json:"description" validate:"required"`
	Status      *int8  `json:"status" validate:"omitempty,oneof=0 1"`
	Author      string `json:"author" validate:"max=100"`
}

// ArticleResponse 公告响应DTO
type ArticleResponse struct {
	ID          uint64 `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Status      int8   `json:"status"`
	Author      string `json:"author"`
	CreatedBy   uint64 `json:"created_by"`
	UpdatedBy   uint64 `json:"updated_by"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// ArticleListItem 公告列表项DTO
type ArticleListItem struct {
	ID        uint64 `json:"id"`
	Title     string `json:"title"`
	Status    int8   `json:"status"`
	Author    string `json:"author"`
	CreatedAt string `json:"created_at"`
}

// ArticleUpdateRequest 公告更新请求DTO
type ArticleUpdateRequest struct {
	Title       *string `json:"title" validate:"omitempty,min=2,max=255"`
	Description *string `json:"description"`
	Status      *int8   `json:"status" validate:"omitempty,oneof=0 1"`
	Author      *string `json:"author" validate:"omitempty,max=100"`
}
