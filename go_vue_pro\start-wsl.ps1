# Go Vue Pro 项目启动脚本 (Windows PowerShell + WSL)
# 适用于 Windows 环境中使用 WSL Docker

Write-Host "=== Go Vue Pro 项目启动 (WSL Docker) ===" -ForegroundColor Green

# 检查 WSL 是否可用
Write-Host "检查 WSL 环境..." -ForegroundColor Yellow
try {
    $wslVersion = wsl --version
    Write-Host "✓ WSL 可用" -ForegroundColor Green
} catch {
    Write-Host "✗ WSL 不可用，请安装 WSL" -ForegroundColor Red
    exit 1
}

# 检查 Docker 是否在 WSL 中可用
Write-Host "检查 WSL 中的 Docker..." -ForegroundColor Yellow
$dockerCheck = wsl docker --version 2>$null
if ($dockerCheck) {
    Write-Host "✓ WSL 中 Docker 可用" -ForegroundColor Green
} else {
    Write-Host "✗ WSL 中 Docker 不可用" -ForegroundColor Red
    Write-Host "请确保 Docker Desktop 已启动并启用 WSL 集成" -ForegroundColor Yellow
    exit 1
}

# 设置脚本权限
Write-Host "设置脚本权限..." -ForegroundColor Yellow
wsl chmod +x start.sh verify.sh stop.sh

# 检查项目目录
Write-Host "检查项目目录..." -ForegroundColor Yellow
$projectPath = "/mnt/d/project/go_vue_pro"
$dirCheck = wsl test -d $projectPath
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 项目目录存在" -ForegroundColor Green
} else {
    Write-Host "✗ 项目目录不存在: $projectPath" -ForegroundColor Red
    exit 1
}

# 进入项目目录并启动服务
Write-Host "启动 Docker 服务..." -ForegroundColor Yellow
Write-Host "正在执行: wsl cd $projectPath && ./start.sh" -ForegroundColor Gray

# 在 WSL 中运行启动脚本
wsl bash -c "cd $projectPath && ./start.sh"

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n🎉 服务启动完成！" -ForegroundColor Green
    
    Write-Host "`n=== 访问地址 ===" -ForegroundColor Cyan
    Write-Host "前端应用: http://localhost" -ForegroundColor White
    Write-Host "API 接口: http://localhost/api/" -ForegroundColor White
    Write-Host "API 文档: http://localhost/swagger/" -ForegroundColor White
    Write-Host "队列监控: http://localhost/asynq/" -ForegroundColor White
    
    # 询问是否打开浏览器
    $openBrowser = Read-Host "`n是否打开浏览器? (Y/n)"
    if ($openBrowser -ne "n" -and $openBrowser -ne "N") {
        Start-Process "http://localhost"
    }
    
    Write-Host "`n=== 管理命令 ===" -ForegroundColor Cyan
    Write-Host "验证服务: .\verify-wsl.ps1" -ForegroundColor White
    Write-Host "停止服务: .\stop-wsl.ps1" -ForegroundColor White
    Write-Host "查看日志: wsl cd $projectPath/docker && docker compose logs" -ForegroundColor White
    
} else {
    Write-Host "`n✗ 服务启动失败" -ForegroundColor Red
    Write-Host "请检查错误信息并重试" -ForegroundColor Yellow
}
