<template>
  <div class="role-list">
    <div class="page-header">
      <h1 class="page-title">角色管理</h1>
      <div class="page-actions">
        <el-button type="primary" @click="showCreateDialog">创建角色</el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="角色名称">
          <el-input
            v-model="filterForm.name"
            placeholder="请输入角色名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="Guard名称">
          <el-input
            v-model="filterForm.guard_name"
            placeholder="请输入Guard名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="searchRoles"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetFilter"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 角色列表 -->
    <el-card shadow="hover" class="role-table-container">
      <div slot="header">
        <span>角色列表</span>
      </div>
      <el-table
        :data="roleList"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column
          prop="name"
          label="角色名称"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="guard_name"
          label="Guard名称"
          min-width="120"
        ></el-table-column>
        <el-table-column label="权限数量" min-width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="success">{{ getPermissionCount(scope.row) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="viewRoleDetail(scope.row)"
              >详情</el-button
            >
            <el-button
              size="mini"
              type="success"
              @click="showUpdateDialog(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="deleteRoleItem(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 角色详情对话框 -->
    <el-dialog
      title="角色详情"
      :visible.sync="detailDialogVisible"
      width="700px"
    >
      <div v-loading="detailLoading">
        <div v-if="currentRole">
          <el-descriptions border>
            <el-descriptions-item label="ID">{{
              currentRole.id
            }}</el-descriptions-item>
            <el-descriptions-item label="角色名称">{{
              currentRole.name
            }}</el-descriptions-item>
            <el-descriptions-item label="Guard名称">{{
              currentRole.guard_name
            }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="3">{{
              currentRole.remark || "-"
            }}</el-descriptions-item>
          </el-descriptions>

          <div
            class="detail-section"
            v-if="currentRole.permissions && currentRole.permissions.length"
          >
            <h4>角色权限</h4>

            <!-- 权限树形图 -->
            <el-tree
              :data="detailPermissionTree"
              :default-expanded-keys="expandedKeys"
              node-key="id"
              :props="{
                label: 'name',
                children: 'children',
              }"
              :default-checked-keys="permissionCheckedKeys"
              show-checkbox
              disabled
            >
              <span class="custom-tree-node" slot-scope="{ data }">
                <span>{{ data.name }}</span>
                <el-tag
                  v-if="data.http_method"
                  size="mini"
                  type="info"
                  class="method-tag"
                >
                  {{ data.http_method }}
                </el-tag>
                <span v-if="data.http_path" class="permission-http">
                  {{ data.http_path }}
                </span>
              </span>
            </el-tree>
          </div>

          <div
            class="detail-section"
            v-if="currentRole.menus && currentRole.menus.length"
          >
            <h4>关联菜单</h4>
            <el-tag
              v-for="menu in currentRole.menus"
              :key="menu.id"
              type="primary"
              class="menu-tag"
            >
              {{ menu.title }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 创建/编辑角色对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '创建角色' : '编辑角色'"
      :visible.sync="formDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="roleForm"
        :rules="roleFormRules"
        ref="roleForm"
        label-width="120px"
        v-loading="formLoading"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="roleForm.name"
            placeholder="请输入角色名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="Guard名称" prop="guard_name">
          <el-input
            v-model="roleForm.guard_name"
            placeholder="请输入Guard名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            v-model="roleForm.remark"
            :rows="3"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
        <el-form-item label="关联权限" prop="permission_ids">
          <el-button type="text" @click="showPermissionDialog">设置权限</el-button>
          <div v-if="roleForm.permission_ids.length" class="selected-permissions-count">
            已选择 {{ roleForm.permission_ids.length }} 个权限
          </div>
          
  
          <!-- 已选权限预览 -->
          <div class="selected-permissions-preview" v-if="roleForm.permission_ids.length > 0">
            <h4>已选权限 ({{ roleForm.permission_ids.length }})</h4>
            <div class="permission-summary">
              <el-tag
                v-for="permission in selectedPermissionsSummary"
                :key="permission.id"
                closable
                @close="removePermission(permission.id)"
                :type="getMethodTagType(permission.http_method)"
              >
                {{ permission.name }}
                <span v-if="permission.http_method">({{ permission.http_method }})</span>
              </el-tag>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="关联菜单" prop="menu_ids">
          <div class="menu-selection">
            <div class="menu-filter">
              <el-input
                placeholder="搜索菜单"
                v-model="menuFilterText"
                prefix-icon="el-icon-search"
                clearable
                style="margin-bottom: 10px;"
              ></el-input>
            </div>

            <el-tree
              ref="menuTree"
              :data="menuTreeData"
              show-checkbox
              node-key="id"
              :props="{
                label: 'title',
                children: 'children',
              }"
              :default-checked-keys="roleForm.menu_ids"
              :filter-node-method="filterMenuNodes"
              check-strictly
              @check="handleMenuCheck"
            >
              <span class="custom-tree-node" slot-scope="{ data }">
                <i :class="data.icon || 'el-icon-menu'" class="menu-icon"></i>
                <span>{{ data.title }}</span>
                <el-tag
                  v-if="data.uri"
                  size="mini"
                  type="info"
                  class="uri-tag"
                >
                  {{ data.uri }}
                </el-tag>
              </span>
            </el-tree>

            <!-- 已选菜单预览 -->
            <div class="selected-menus-preview" v-if="roleForm.menu_ids.length > 0">
              <h4>已选菜单 ({{ roleForm.menu_ids.length }})</h4>
              <div class="menu-summary">
                <el-tag
                  v-for="menu in selectedMenusSummary"
                  :key="menu.id"
                  closable
                  @close="removeMenu(menu.id)"
                  type="primary"
                >
                  {{ menu.title }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitRoleForm" :loading="formLoading"
          >确认</el-button
        >
      </div>
    </el-dialog>

    <!-- 权限选择对话框 -->
    <el-dialog
      title="选择权限"
      :visible.sync="permissionDialogVisible"
      width="60%"
      append-to-body
    >
      <div v-loading="permissionLoading">
        <el-input
          v-model="permissionFilterText"
          placeholder="搜索权限"
          clearable
          style="margin-bottom: 15px"
        />
        <el-tree
          ref="permissionDialogTree"
          :data="permissionTreeData"
          :props="{
            children: 'children',
            label: 'name'
          }"
          node-key="id"
          show-checkbox
          default-expand-all
          :filter-node-method="filterPermissionNode"
        >
          <span slot-scope="{ data }" class="custom-tree-node">
            <span>{{ data.name }}</span>
            <span class="permission-slug">{{ data.slug }}</span>
            <span v-if="data.http_path" class="permission-http">
              {{ data.http_method || '*' }} {{ data.http_path }}
            </span>
          </span>
        </el-tree>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmPermissions">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRoles,
  getRole,
  createRole,
  updateRole,
  deleteRole,
  assignPermissionsToRole,
  assignMenusToRole
} from "@/api/role";
import { getPermissionTree } from "@/api/permission";
import { getMenuList } from "@/api/menu";

export default {
  name: "RoleList",
  data() {
    return {
      loading: false,
      detailLoading: false,
      formLoading: false,
      // 筛选表单
      filterForm: {
        name: "",
        guard_name: "",
      },
      // 角色列表数据
      roleList: [],
      // 分页
      pagination: {
        page: 1,
        page_size: 10,
        total: 0,
      },
      // 选中的行
      multipleSelection: [],
      // 详情对话框
      detailDialogVisible: false,
      currentRole: null,
      detailPermissionTree: [], // 详情页面的权限树
      // 表单对话框
      formDialogVisible: false,
      // 对话框类型：create-创建，update-更新
      dialogType: "create",
      // 角色表单
      roleForm: {
        name: "",
        guard_name: "admin",
        remark: "",
        permission_ids: [],
        menu_ids: [],
      },
      // 表单验证规则
      roleFormRules: {
        name: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
        guard_name: [
          { required: true, message: "请输入Guard名称", trigger: "blur" },
        ],
      },
      // 权限选项
      permissionOptions: [],
      // 权限树数据
      permissionTree: [],
      // 展开的节点
      expandedKeys: [],
      // 权限选中的keys
      permissionCheckedKeys: [],
      // 菜单选项
      menuOptions: [],
      // 菜单树数据
      menuTreeData: [],
      // 菜单搜索关键词
      menuFilterText: '',
      // 权限标签页
      activePermissionTab: 'module',
      // 权限分类
      permissionCategories: [
        { label: '系统管理', value: 'system' },
        { label: '内容管理', value: 'content' },
        { label: '用户管理', value: 'user' },
        { label: '模块管理', value: 'module' },
      ],
      // 选择的权限分类
      selectedCategory: '',
      // 选择的HTTP方法
      selectedHttpMethod: '',
      // 权限搜索关键词
      permissionFilterText: '',
      // 权限模块
      permissionModules: [
        {
          key: 'system',
          name: '系统管理',
          icon: 'el-icon-setting',
          actions: [
            { label: '查看', value: 'view' },
            { label: '创建', value: 'create' },
            { label: '编辑', value: 'edit' },
            { label: '删除', value: 'delete' }
          ]
        },
        {
          key: 'user',
          name: '用户管理',
          icon: 'el-icon-user',
          actions: [
            { label: '查看', value: 'view' },
            { label: '创建', value: 'create' },
            { label: '编辑', value: 'edit' },
            { label: '删除', value: 'delete' },
            { label: '分配角色', value: 'assign_role' }
          ]
        },
        {
          key: 'content',
          name: '内容管理',
          icon: 'el-icon-document',
          actions: [
            { label: '查看', value: 'view' },
            { label: '创建', value: 'create' },
            { label: '编辑', value: 'edit' },
            { label: '删除', value: 'delete' },
            { label: '发布', value: 'publish' }
          ]
        },
        {
          key: 'project',
          name: '项目管理',
          icon: 'el-icon-folder',
          actions: [
            { label: '查看', value: 'view' },
            { label: '创建', value: 'create' },
            { label: '编辑', value: 'edit' },
            { label: '删除', value: 'delete' }
          ]
        }
      ],
      // 选中的模块
      selectedModules: [],
      // 模块权限映射
      modulePermissions: {},
      // 权限模板
      permissionTemplates: [
        {
          key: 'admin',
          name: '超级管理员',
          icon: 'el-icon-crown',
          description: '拥有所有权限',
          permissions: ['系统管理', '用户管理', '内容管理', '项目管理']
        },
        {
          key: 'editor',
          name: '编辑员',
          icon: 'el-icon-edit',
          description: '内容编辑和管理权限',
          permissions: ['内容查看', '内容创建', '内容编辑']
        },
        {
          key: 'viewer',
          name: '查看员',
          icon: 'el-icon-view',
          description: '只读权限',
          permissions: ['系统查看', '用户查看', '内容查看']
        },
        {
          key: 'project_manager',
          name: '项目经理',
          icon: 'el-icon-s-management',
          description: '项目管理权限',
          permissions: ['项目查看', '项目创建', '项目编辑', '项目删除']
        }
      ],
      // 权限相关数据
      permissionLoading: false,
      permissionTreeData: [],
      // 权限选择对话框
      permissionDialogVisible: false,
    };
  },
  created() {
    this.fetchRoleList();
    this.fetchPermissionOptions();
    this.fetchMenuOptions();
    this.initializeModulePermissions();
  },
  watch: {
    // 监听权限搜索关键词变化
    permissionFilterText(val) {
      this.$refs.permissionTree && this.$refs.permissionTree.filter(val);
      this.$refs.permissionDialogTree && this.$refs.permissionDialogTree.filter(val);
    },
    // 监听菜单搜索关键词变化
    menuFilterText(val) {
      this.$refs.menuTree && this.$refs.menuTree.filter(val);
    }
  },
  computed: {
    // 根据分类和HTTP方法过滤权限树
    filteredPermissionTree() {
      let filtered = this.permissionTree;

      // 按分类过滤
      if (this.selectedCategory) {
        filtered = filtered.filter(node => {
          if (this.selectedCategory === 'system') {
            return ['admin', 'settings', 'menu'].some(prefix =>
              node.slug && node.slug.startsWith(prefix)
            );
          } else if (this.selectedCategory === 'content') {
            return ['article', 'friendly'].some(prefix =>
              node.slug && node.slug.startsWith(prefix)
            );
          } else if (this.selectedCategory === 'user') {
            return ['users', 'roles', 'permissions'].some(prefix =>
              node.slug && node.slug.startsWith(prefix)
            );
          } else if (this.selectedCategory === 'module') {
            return ['module', 'project', 'domain'].some(prefix =>
              node.slug && node.slug.startsWith(prefix)
            );
          }
          return true;
        });
      }

      // 按HTTP方法过滤
      if (this.selectedHttpMethod) {
        filtered = this.filterTreeByHttpMethod(filtered, this.selectedHttpMethod);
      }

      return filtered;
    },

    // 已选权限摘要
    selectedPermissionsSummary() {
      const selected = [];
      const findSelected = (nodes) => {
        nodes.forEach(node => {
          if (this.roleForm.permission_ids.includes(node.id)) {
            selected.push(node);
          }
          if (node.children && node.children.length) {
            findSelected(node.children);
          }
        });
      };
      findSelected(this.permissionTree);
      return selected;
    },

    // 已选菜单摘要
    selectedMenusSummary() {
      const selected = [];
      const findSelected = (nodes) => {
        nodes.forEach(node => {
          if (this.roleForm.menu_ids.includes(node.id)) {
            selected.push(node);
          }
          if (node.children && node.children.length) {
            findSelected(node.children);
          }
        });
      };
      findSelected(this.menuTreeData);
      return selected;
    }
  },
  methods: {
    // 获取角色列表
    fetchRoleList() {
      this.loading = true;
      const params = {
        page: this.pagination.page,
        page_size: this.pagination.page_size,
        ...this.filterForm,
      };

      // 移除值为null或空字符串的参数
      Object.keys(params).forEach((key) => {
        if (params[key] === null || params[key] === "") {
          delete params[key];
        }
      });

      getRoles(params)
        .then((response) => {
          this.roleList = response.items || [];
          this.pagination.total = response.total || 0;
        })
        .catch((error) => {
          this.$message.error(`获取角色列表失败: ${error.message}`);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取角色权限数量
    getPermissionCount(role) {
      return role.permission_count || 0;
    },

    // 获取权限选项并构建树形结构
    fetchPermissionOptions() {
      return new Promise((resolve, reject) => {
        getPermissionTree({ page_size: 1000 })
          .then((response) => {
            this.permissionOptions = response || [];
            this.buildPermissionTree();
            resolve(this.permissionTree);
          })
          .catch((error) => {
            console.error('获取权限列表失败:', error);
            this.$message.error(`获取权限列表失败: ${error.message}`);
            reject(error);
          });
      });
    },

    // 构建权限树
    buildPermissionTree() {
      // 如果没有权限数据，直接返回
      if (!this.permissionOptions.length) {
        return [];
      }

      // 将列表转换为ID映射
      const permissionMap = {};
      this.permissionOptions.forEach((permission) => {
        permission.children = [];
        permissionMap[permission.id] = permission;
      });

      // 构建树形结构
      const tree = [];
      this.permissionOptions.forEach((permission) => {
        if (permission.parent_id === 0 || !permission.parent_id) {
          // 根节点
          tree.push(permission);
        } else {
          // 子节点，添加到父节点的children中
          const parent = permissionMap[permission.parent_id];
          if (parent) {
            parent.children.push(permission);
          } else {
            // 如果找不到父节点，放在根节点
            tree.push(permission);
          }
        }
      });

      // 递归排序
      const sortTree = (nodes) => {
        nodes.sort((a, b) => a.order - b.order);
        nodes.forEach((node) => {
          if (node.children && node.children.length) {
            sortTree(node.children);
          }
        });
      };
      sortTree(tree);

      this.permissionTree = tree;

      // 设置默认展开的节点为所有根节点
      this.expandedKeys = tree.map((node) => node.id);
    },

    // 获取菜单选项并构建菜单树
    fetchMenuOptions() {
      return new Promise((resolve, reject) => {
        getMenuList()
          .then((response) => {
            this.menuOptions = response || [];
            this.buildMenuTree();
            resolve(this.menuTreeData);
          })
          .catch((error) => {
            console.error('获取菜单列表失败:', error);
            this.$message.error(`获取菜单列表失败: ${error.message}`);
            reject(error);
          });
      });
    },

    // 构建菜单树
    buildMenuTree() {
      if (!this.menuOptions.length) {
        return [];
      }

      // 将列表转换为ID映射
      const menuMap = {};
      this.menuOptions.forEach((menu) => {
        menu.children = [];
        menuMap[menu.id] = menu;
      });

      // 构建树形结构
      const tree = [];
      this.menuOptions.forEach((menu) => {
        if (menu.parent_id === 0 || !menu.parent_id) {
          // 根节点
          tree.push(menu);
        } else {
          // 子节点，添加到父节点的children中
          const parent = menuMap[menu.parent_id];
          if (parent) {
            parent.children.push(menu);
          } else {
            // 如果找不到父节点，放在根节点
            tree.push(menu);
          }
        }
      });

      // 递归排序
      const sortTree = (nodes) => {
        nodes.sort((a, b) => (a.order || 0) - (b.order || 0));
        nodes.forEach((node) => {
          if (node.children && node.children.length) {
            sortTree(node.children);
          }
        });
      };
      sortTree(tree);

      this.menuTreeData = tree;
    },

    // 初始化模块权限 - 从权限数据中动态生成模块
    initializeModulePermissions() {
      // 先清空现有模块
      this.permissionModules = [];
      this.modulePermissions = {};

      // 从权限树中提取模块信息
      this.extractModulesFromPermissions();
    },

    // 从权限数据中提取模块信息
    extractModulesFromPermissions() {
      const moduleMap = new Map();

      // 遍历权限树，提取模块信息
      const extractModules = (nodes, parentPath = '') => {
        nodes.forEach(node => {
          if (node.parent_id === 0 || !node.parent_id) {
            // 根节点作为模块
            const moduleKey = this.getModuleKeyFromSlug(node.slug);
            const moduleName = node.name;
            const moduleIcon = this.getModuleIcon(moduleKey);

            if (!moduleMap.has(moduleKey)) {
              moduleMap.set(moduleKey, {
                key: moduleKey,
                name: moduleName,
                icon: moduleIcon,
                permissions: [],
                actions: new Set()
              });
            }

            // 收集该模块下的所有权限
            this.collectModulePermissions(node, moduleMap.get(moduleKey));
          }

          if (node.children && node.children.length) {
            extractModules(node.children, parentPath + '/' + node.name);
          }
        });
      };

      extractModules(this.permissionTree);

      // 转换为数组格式
      this.permissionModules = Array.from(moduleMap.values()).map(module => ({
        ...module,
        actions: Array.from(module.actions).map(action => ({
          label: this.getActionLabel(action),
          value: action
        }))
      }));

      // 初始化模块权限映射
      this.permissionModules.forEach(module => {
        this.$set(this.modulePermissions, module.key, []);
      });
    },

    // 收集模块权限
    collectModulePermissions(node, moduleInfo) {
      // 添加当前节点
      moduleInfo.permissions.push(node);

      // 从权限标识中提取操作类型
      if (node.slug) {
        const action = this.extractActionFromSlug(node.slug);
        if (action) {
          moduleInfo.actions.add(action);
        }
      }

      // 递归处理子节点
      if (node.children && node.children.length) {
        node.children.forEach(child => {
          this.collectModulePermissions(child, moduleInfo);
        });
      }
    },

    // 从权限标识获取模块键
    getModuleKeyFromSlug(slug) {
      if (!slug) return 'other';

      const slugLower = slug.toLowerCase();
      if (slugLower.includes('admin') || slugLower.includes('system') || slugLower.includes('setting')) {
        return 'system';
      } else if (slugLower.includes('user') || slugLower.includes('role') || slugLower.includes('permission')) {
        return 'user';
      } else if (slugLower.includes('article') || slugLower.includes('content') || slugLower.includes('friendly')) {
        return 'content';
      } else if (slugLower.includes('project') || slugLower.includes('module') || slugLower.includes('domain')) {
        return 'project';
      } else if (slugLower.includes('menu')) {
        return 'menu';
      }
      return 'other';
    },

    // 获取模块图标
    getModuleIcon(moduleKey) {
      const iconMap = {
        'system': 'el-icon-setting',
        'user': 'el-icon-user',
        'content': 'el-icon-document',
        'project': 'el-icon-folder',
        'menu': 'el-icon-menu',
        'other': 'el-icon-more'
      };
      return iconMap[moduleKey] || 'el-icon-more';
    },

    // 从权限标识提取操作类型
    extractActionFromSlug(slug) {
      if (!slug) return null;

      const slugLower = slug.toLowerCase();
      if (slugLower.includes('index') || slugLower.includes('list') || slugLower.includes('show')) {
        return 'view';
      } else if (slugLower.includes('create') || slugLower.includes('store')) {
        return 'create';
      } else if (slugLower.includes('edit') || slugLower.includes('update')) {
        return 'edit';
      } else if (slugLower.includes('delete') || slugLower.includes('destroy')) {
        return 'delete';
      } else if (slugLower.includes('assign')) {
        return 'assign';
      } else if (slugLower.includes('publish')) {
        return 'publish';
      }
      return 'other';
    },

    // 获取操作标签
    getActionLabel(action) {
      const labelMap = {
        'view': '查看',
        'create': '创建',
        'edit': '编辑',
        'delete': '删除',
        'assign': '分配',
        'publish': '发布',
        'other': '其他'
      };
      return labelMap[action] || action;
    },

    // 权限树选择处理
    handlePermissionCheck(data, checked) {
      console.log('权限节点选择变化:', data.id, checked);
      // 获取所有选中的节点
      const checkedKeys = this.$refs.permissionTree.getCheckedKeys();
      console.log('权限树当前所有选中ID:', checkedKeys);
      
      // 同步更新roleForm中的权限IDs
      this.roleForm.permission_ids = checkedKeys;
    },

    // 菜单树选择处理
    handleMenuCheck() {
      // 获取所有选中的节点
      this.roleForm.menu_ids = this.$refs.menuTree.getCheckedKeys();
    },

    // 切换模块选择
    toggleModule(moduleKey) {
      const index = this.selectedModules.indexOf(moduleKey);
      if (index > -1) {
        this.selectedModules.splice(index, 1);
        this.modulePermissions[moduleKey] = [];
      } else {
        this.selectedModules.push(moduleKey);
      }
      this.updatePermissionsByModules();
    },

    // 处理模块权限变化
    handleModulePermissionChange(moduleKey) {
      this.updatePermissionsByModules();
    },

    // 根据模块更新权限
    updatePermissionsByModules() {
      const selectedPermissions = [];

      // 收集所有选中模块的权限
      this.selectedModules.forEach(moduleKey => {
        const actions = this.modulePermissions[moduleKey] || [];
        actions.forEach(action => {
          // 根据模块和操作找到对应的权限ID
          const permissions = this.findPermissionsByModuleAndAction(moduleKey, action);
          selectedPermissions.push(...permissions);
        });
      });

      // 使用集合去重后更新roleForm.permission_ids
      this.roleForm.permission_ids = [...new Set(selectedPermissions)];
      console.log('模块选择更新权限IDs:', this.roleForm.permission_ids);

      // 如果权限树已加载，同步更新权限树的选中状态
      if (this.$refs.permissionTree) {
        this.$nextTick(() => {
          this.$refs.permissionTree.setCheckedKeys(this.roleForm.permission_ids);
        });
      }
    },

    // 切换标签页
    handlePermissionTabChange(tab) {
      this.activePermissionTab = tab.name;
      
      // 当从模块选择切换到精细权限选择时，确保权限树反映当前选择
      if (tab.name === 'detailed' && this.$refs.permissionTree) {
        this.$nextTick(() => {
          this.$refs.permissionTree.setCheckedKeys(this.roleForm.permission_ids);
        });
      }
    },

    // 根据模块和操作查找权限
    findPermissionsByModuleAndAction(moduleKey, action) {
      const permissions = [];

      const searchPermissions = (nodes) => {
        nodes.forEach(node => {
          // 根据权限标识匹配模块和操作
          if (node.slug) {
            const slug = node.slug.toLowerCase();
            if (this.matchModuleAndAction(slug, moduleKey, action)) {
              permissions.push(node.id);
            }
          }
          if (node.children && node.children.length) {
            searchPermissions(node.children);
          }
        });
      };

      searchPermissions(this.permissionTree);
      return permissions;
    },

    // 匹配模块和操作
    matchModuleAndAction(slug, moduleKey, action) {
      const moduleMap = {
        'system': ['admin', 'settings', 'menu'],
        'user': ['users', 'roles', 'permissions'],
        'content': ['article', 'friendly'],
        'project': ['module', 'project', 'domain']
      };

      const actionMap = {
        'view': ['index', 'show', 'list'],
        'create': ['create', 'store'],
        'edit': ['edit', 'update'],
        'delete': ['delete', 'destroy'],
        'assign_role': ['assign'],
        'publish': ['publish']
      };

      const modulePrefixes = moduleMap[moduleKey] || [];
      const actionSuffixes = actionMap[action] || [];

      return modulePrefixes.some(prefix => slug.includes(prefix)) &&
             actionSuffixes.some(suffix => slug.includes(suffix));
    },

    // 应用权限模板
    applyPermissionTemplate(templateKey) {
      const template = this.permissionTemplates.find(t => t.key === templateKey);
      if (!template) return;

      let permissionIds = [];

      if (templateKey === 'admin') {
        // 超级管理员：所有权限
        const getAllPermissionIds = (nodes) => {
          const ids = [];
          nodes.forEach(node => {
            ids.push(node.id);
            if (node.children && node.children.length) {
              ids.push(...getAllPermissionIds(node.children));
            }
          });
          return ids;
        };
        permissionIds = getAllPermissionIds(this.permissionTree);
      } else if (templateKey === 'editor') {
        // 编辑员：内容相关权限
        permissionIds = this.findPermissionsByCategory('content');
      } else if (templateKey === 'viewer') {
        // 查看员：只读权限
        permissionIds = this.findPermissionsByAction('view');
      } else if (templateKey === 'project_manager') {
        // 项目经理：项目管理权限
        permissionIds = this.findPermissionsByCategory('project');
      }

      this.roleForm.permission_ids = permissionIds;

      // 更新权限树的选中状态
      if (this.$refs.permissionTree) {
        this.$refs.permissionTree.setCheckedKeys(permissionIds);
      }

      this.$message.success(`已应用${template.name}权限模板`);
    },

    // 根据分类查找权限
    findPermissionsByCategory(category) {
      const permissions = [];
      const searchPermissions = (nodes) => {
        nodes.forEach(node => {
          if (node.slug) {
            const slug = node.slug.toLowerCase();
            if (category === 'content' && ['article', 'friendly'].some(prefix => slug.includes(prefix))) {
              permissions.push(node.id);
            } else if (category === 'project' && ['module', 'project', 'domain'].some(prefix => slug.includes(prefix))) {
              permissions.push(node.id);
            }
          }
          if (node.children && node.children.length) {
            searchPermissions(node.children);
          }
        });
      };
      searchPermissions(this.permissionTree);
      return permissions;
    },

    // 根据操作查找权限
    findPermissionsByAction(action) {
      const permissions = [];
      const actionSuffixes = {
        'view': ['index', 'show', 'list']
      };

      const suffixes = actionSuffixes[action] || [];

      const searchPermissions = (nodes) => {
        nodes.forEach(node => {
          if (node.slug && suffixes.some(suffix => node.slug.includes(suffix))) {
            permissions.push(node.id);
          }
          if (node.children && node.children.length) {
            searchPermissions(node.children);
          }
        });
      };

      searchPermissions(this.permissionTree);
      return permissions;
    },

    // 按HTTP方法过滤树
    filterTreeByHttpMethod(nodes, method) {
      const filtered = [];
      nodes.forEach(node => {
        if (node.http_method === method || !node.http_method) {
          const newNode = { ...node };
          if (node.children && node.children.length) {
            newNode.children = this.filterTreeByHttpMethod(node.children, method);
          }
          filtered.push(newNode);
        }
      });
      return filtered;
    },

    // 获取HTTP方法标签类型
    getMethodTagType(method) {
      const typeMap = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
      };
      return typeMap[method] || 'info';
    },

    // 移除权限
    removePermission(permissionId) {
      const index = this.roleForm.permission_ids.indexOf(permissionId);
      if (index > -1) {
        this.roleForm.permission_ids.splice(index, 1);
        if (this.$refs.permissionTree) {
          this.$refs.permissionTree.setCheckedKeys(this.roleForm.permission_ids);
        }
      }
    },

    // 移除菜单
    removeMenu(menuId) {
      const index = this.roleForm.menu_ids.indexOf(menuId);
      if (index > -1) {
        this.roleForm.menu_ids.splice(index, 1);
        if (this.$refs.menuTree) {
          this.$refs.menuTree.setCheckedKeys(this.roleForm.menu_ids);
        }
      }
    },

    // 搜索权限
    filterPermissionNodes(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1 ||
             (data.slug && data.slug.indexOf(value) !== -1);
    },

    // 搜索菜单
    filterMenuNodes(value, data) {
      if (!value) return true;
      return data.title.indexOf(value) !== -1 ||
             (data.uri && data.uri.indexOf(value) !== -1);
    },

    // 显示创建角色对话框
    showCreateDialog() {
      this.dialogType = "create";
      this.resetRoleForm();
      this.formDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.roleForm.clearValidate();
      });
    },

    // 显示更新角色对话框
    showUpdateDialog(row) {
      this.dialogType = "update";
      this.resetRoleForm();
      this.formLoading = true;

      getRole(row.id)
        .then((data) => {
          console.log('获取角色详情成功:', data);
          this.roleForm = {
            id: data.id,
            name: data.name || "",
            guard_name: data.guard_name || "admin",
            remark: data.remark || "",
            permission_ids: (data.permissions || []).map((p) => p.id),
            menu_ids: (data.menus || []).map((m) => m.id),
          };
          console.log('设置角色表单数据:', this.roleForm);

          // 确保权限树已加载
          if (this.permissionTree.length === 0) {
            this.fetchPermissionOptions().then(() => {
              this.$nextTick(() => {
                // 确保权限树已经渲染
                if (this.$refs.permissionTree) {
                  this.$refs.permissionTree.setCheckedKeys(this.roleForm.permission_ids);
                }
                
                // 同时更新权限选择对话框的树
                if (this.permissionTreeData.length === 0) {
                  this.permissionTreeData = this.permissionTree;
                }
              });
            });
          } else if (this.$refs.permissionTree) {
            // 权限树已加载
            this.$nextTick(() => {
              this.$refs.permissionTree.setCheckedKeys(this.roleForm.permission_ids);
              
              // 同时更新权限选择对话框的树
              if (this.permissionTreeData.length === 0) {
                this.permissionTreeData = this.permissionTree;
              }
            });
          }

          // 确保菜单树已加载
          if (this.menuTreeData.length === 0) {
            this.fetchMenuOptions().then(() => {
              this.$nextTick(() => {
                // 确保菜单树已经渲染
                if (this.$refs.menuTree) {
                  this.$refs.menuTree.setCheckedKeys(this.roleForm.menu_ids);
                }
              });
            });
          } else if (this.$refs.menuTree) {
            // 菜单树已加载
            this.$nextTick(() => {
              this.$refs.menuTree.setCheckedKeys(this.roleForm.menu_ids);
            });
          }
        })
        .catch((error) => {
          console.error('获取角色详情失败:', error);
          this.$message.error(`获取角色详情失败: ${error.message}`);
          this.formDialogVisible = false;
        })
        .finally(() => {
          this.formLoading = false;
          this.formDialogVisible = true;
        });
    },

    // 重置角色表单
    resetRoleForm() {
      this.roleForm = {
        name: "",
        guard_name: "admin",
        remark: "",
        permission_ids: [],
        menu_ids: [],
      };

      // 重置筛选条件
      this.selectedCategory = '';
      this.selectedHttpMethod = '';
      this.permissionFilterText = '';
      this.menuFilterText = '';
      this.activePermissionTab = 'module';

      // 重置模块选择
      this.selectedModules = [];
      // 遍历modulePermissions对象的键
      Object.keys(this.modulePermissions).forEach(key => {
        this.$set(this.modulePermissions, key, []);
      });

      // 如果表单已经被创建，则重置验证
      if (this.$refs.roleForm) {
        this.$refs.roleForm.resetFields();
      }

      // 如果权限树已加载，清除选中状态
      if (this.$refs.permissionTree) {
        this.$refs.permissionTree.setCheckedKeys([]);
      }

      // 如果菜单树已加载，清除选中状态
      if (this.$refs.menuTree) {
        this.$refs.menuTree.setCheckedKeys([]);
      }
    },

    // 提交角色表单
    submitRoleForm() {
      this.$refs.roleForm.validate((valid) => {
        if (!valid) {
          return false;
        }

        this.formLoading = true;

        // 确保获取最新的权限选择
        if (this.$refs.permissionTree) {
          // 获取树组件中当前选中的节点
          this.roleForm.permission_ids = this.$refs.permissionTree.getCheckedKeys();
          console.log('提交的权限IDs:', this.roleForm.permission_ids);
        }
        
        // 确保获取最新的菜单选择
        if (this.$refs.menuTree) {
          this.roleForm.menu_ids = this.$refs.menuTree.getCheckedKeys();
          console.log('提交的菜单IDs:', this.roleForm.menu_ids);
        }

        // 确保permission_ids和menu_ids是数组类型
        if (!Array.isArray(this.roleForm.permission_ids)) {
          this.roleForm.permission_ids = [];
        }
        
        if (!Array.isArray(this.roleForm.menu_ids)) {
          this.roleForm.menu_ids = [];
        }

        // 构建API需要的数据结构
        let formData = {
          name: this.roleForm.name,
          guard_name: this.roleForm.guard_name,
          remark: this.roleForm.remark,
          permission_ids: this.roleForm.permission_ids,
          menu_ids: this.roleForm.menu_ids
        };
        
        console.log('发送到API的表单数据:', formData);

        // 根据对话框类型决定是创建还是更新
        let apiCall =
          this.dialogType === "create"
            ? createRole(formData)
            : updateRole(this.roleForm.id, formData);

        apiCall
          .then((response) => {
            console.log('API响应成功:', response);
            this.$message.success(
              this.dialogType === "create" ? "创建成功" : "更新成功"
            );
            this.formDialogVisible = false;
            this.fetchRoleList();
          })
          .catch((error) => {
            console.error('API响应错误:', error);
            // 显示详细错误信息
            let errorMsg = error.message;
            if (error.response && error.response.data) {
              errorMsg += `: ${JSON.stringify(error.response.data)}`;
            }
            this.$message.error(
              `${this.dialogType === "create" ? "创建" : "更新"}失败: ${errorMsg}`
            );
          })
          .finally(() => {
            this.formLoading = false;
          });
      });
    },

    // 查看角色详情
    viewRoleDetail(row) {
      this.detailDialogVisible = true;
      this.detailLoading = true;
      this.currentRole = null;
      this.permissionCheckedKeys = [];
      this.detailPermissionTree = [];

      getRole(row.id)
        .then((data) => {
          this.currentRole = data;
          console.log("获取到的角色详情数据:", data);
          
          // 设置权限选中状态
          this.permissionCheckedKeys = (data.permissions || []).map(
            (p) => p.id
          );
          
          // 直接使用API返回的树形结构权限数据
          this.detailPermissionTree = data.permissions || [];
          
          // 设置默认展开的节点
          this.expandedKeys = this.getExpandedKeys(this.detailPermissionTree);
        })
        .catch((error) => {
          this.$message.error(`获取角色详情失败: ${error.message}`);
        })
        .finally(() => {
          this.detailLoading = false;
        });
    },

    // 获取需要展开的节点
    getExpandedKeys(nodes) {
      const keys = [];
      
      // 添加所有有子节点的节点ID到展开列表
      const addExpandedKeys = (nodeList) => {
        nodeList.forEach(node => {
          if (node.children && node.children.length > 0) {
            keys.push(node.id);
            addExpandedKeys(node.children);
          }
        });
      };
      
      addExpandedKeys(nodes);
      return keys;
    },

    // 显示权限选择对话框
    showPermissionDialog() {
      if (this.permissionTreeData.length === 0) {
        this.loadPermissionsForDialog();
      } else {
        this.permissionDialogVisible = true;
        this.$nextTick(() => {
          // 设置已选择的权限
          if (this.$refs.permissionDialogTree && this.roleForm.permission_ids && this.roleForm.permission_ids.length > 0) {
            console.log("设置权限树选中状态，权限IDs:", this.roleForm.permission_ids);
            this.$refs.permissionDialogTree.setCheckedKeys(this.roleForm.permission_ids);
          }
        });
      }
    },
    
    // 加载权限数据
    loadPermissionsForDialog() {
      this.permissionLoading = true;
      getPermissionTree()
        .then((response) => {
          console.log("获取到的权限树数据:", response);
          this.permissionTreeData = response || [];
          this.permissionDialogVisible = true;
          this.permissionLoading = false;
          
          this.$nextTick(() => {
            // 设置已选择的权限
            if (this.$refs.permissionDialogTree && this.roleForm.permission_ids && this.roleForm.permission_ids.length > 0) {
              console.log("加载权限后设置选中状态，权限IDs:", this.roleForm.permission_ids);
              this.$refs.permissionDialogTree.setCheckedKeys(this.roleForm.permission_ids);
            }
          });
        })
        .catch((error) => {
          console.error("获取权限数据失败:", error);
          this.permissionLoading = false;
          this.$message.error("获取权限数据失败");
        });
    },
    
    // 确认选择的权限
    confirmPermissions() {
      if (this.$refs.permissionDialogTree) {
        const checkedNodes = this.$refs.permissionDialogTree.getCheckedNodes();
        const halfCheckedNodes = this.$refs.permissionDialogTree.getHalfCheckedNodes();
        
        // 组合所有选中和半选中的节点
        const allSelectedNodes = [...checkedNodes, ...halfCheckedNodes];
        const permissionIds = allSelectedNodes.map((node) => node.id);
        
        this.roleForm.permission_ids = permissionIds;
        
        // 同步更新权限树的选中状态
        if (this.$refs.permissionTree) {
          this.$nextTick(() => {
            this.$refs.permissionTree.setCheckedKeys(this.roleForm.permission_ids);
          });
        }
      }
      
      this.permissionDialogVisible = false;
    },
    
    // 过滤权限节点
    filterPermissionNode(value, data) {
      if (!value) return true;
      
      // 搜索权限名称或标识
      return data.name.indexOf(value) !== -1 || 
             (data.slug && data.slug.indexOf(value) !== -1);
    },

    // 批量选择权限
    selectPermissionsByCategory(category) {
      this.selectedCategory = category;
      
      if (!this.$refs.permissionTree) return;
      
      // 根据分类选择对应的权限
      const keysToCheck = [];
      
      const findPermissionsByPrefix = (nodes, prefixes) => {
        nodes.forEach(node => {
          if (prefixes.some(prefix => node.slug && node.slug.startsWith(prefix))) {
            keysToCheck.push(node.id);
          }
          if (node.children && node.children.length) {
            findPermissionsByPrefix(node.children, prefixes);
          }
        });
      };
      
      if (category === 'system') {
        findPermissionsByPrefix(this.permissionTree, ['admin', 'settings', 'menu']);
      } else if (category === 'content') {
        findPermissionsByPrefix(this.permissionTree, ['article', 'friendly']);
      } else if (category === 'user') {
        findPermissionsByPrefix(this.permissionTree, ['users', 'roles', 'permissions']);
      } else if (category === 'module') {
        findPermissionsByPrefix(this.permissionTree, ['module', 'project', 'domain']);
      }
      
      // 将新选中的权限添加到现有选中的权限中
      const currentCheckedKeys = this.$refs.permissionTree.getCheckedKeys();
      const newCheckedKeys = [...new Set([...currentCheckedKeys, ...keysToCheck])];
      
      this.$refs.permissionTree.setCheckedKeys(newCheckedKeys);
      this.roleForm.permission_ids = newCheckedKeys;
    },
    
    // 切换高级选项显示
    toggleAdvancedOptions() {
      this.showAdvancedOptions = !this.showAdvancedOptions;
    },
    
    // 其他方法保持不变...
    searchRoles() {
      this.pagination.page = 1;
      this.fetchRoleList();
    },
    resetFilter() {
      this.filterForm = {
        name: "",
        guard_name: "",
      };
      this.searchRoles();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSizeChange(val) {
      this.pagination.page_size = val;
      this.fetchRoleList();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.fetchRoleList();
    },
    deleteRoleItem(row) {
      this.$confirm(`确认删除角色 "${row.name}" 吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          deleteRole(row.id)
            .then(() => {
              this.$message.success("删除成功");
              this.fetchRoleList();
            })
            .catch((error) => {
              this.$message.error(`删除失败: ${error.message}`);
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          // 取消删除
        });
    },
  },
};
</script>

<style scoped>
.role-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.role-table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.detail-section {
  margin-top: 20px;
}

.permission-tag,
.menu-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.method-tag {
  margin-left: 5px;
}

.path-tag {
  margin-left: 5px;
}

.uri-tag {
  margin-left: 5px;
}

.menu-icon {
  margin-right: 5px;
  color: #409EFF;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.permission-tags {
  display: flex;
  gap: 5px;
}

/* 权限模块选择样式 */
.permission-module-selection {
  margin-bottom: 20px;
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.module-card {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.module-card:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.module-card.selected {
  border-color: #409EFF;
  background: #f0f9ff;
}

.module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.module-header i {
  font-size: 20px;
  color: #409EFF;
  margin-right: 8px;
}

.module-name {
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.module-permissions {
  margin-top: 12px;
}

.module-permissions .el-checkbox {
  margin-right: 16px;
  margin-bottom: 8px;
}

/* 权限模板样式 */
.permission-templates {
  margin-bottom: 20px;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 180px;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.template-header {
  text-align: center;
  margin-bottom: 12px;
}

.template-header i {
  font-size: 32px;
  color: #409EFF;
  margin-bottom: 8px;
  display: block;
}

.template-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.template-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 12px;
  text-align: center;
}

.template-permissions {
  text-align: center;
}

.template-permissions .el-tag {
  margin: 2px;
}

/* 已选权限预览样式 */
.selected-permissions-preview,
.selected-menus-preview {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.selected-permissions-preview h4,
.selected-menus-preview h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.permission-summary,
.menu-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.permission-summary .el-tag,
.menu-summary .el-tag {
  margin: 0;
}

/* 权限筛选样式 */
.permission-filter {
  margin-bottom: 16px;
}

.menu-selection {
  margin-bottom: 16px;
}

.menu-filter {
  margin-bottom: 12px;
}

/* 标签页样式优化 */
.el-tabs--border-card {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
}

.el-tabs--border-card > .el-tabs__content {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .module-grid {
    grid-template-columns: 1fr;
  }

  .permission-templates .el-col {
    margin-bottom: 16px;
  }
}

.selected-permissions-count {
  margin-top: 5px;
  font-size: 12px;
  color: #409EFF;
}

.permission-slug {
  color: #909399;
  font-size: 12px;
  margin-left: 10px;
}

.permission-http {
  color: #409EFF;
  font-size: 12px;
  margin-left: 10px;
}
</style>
