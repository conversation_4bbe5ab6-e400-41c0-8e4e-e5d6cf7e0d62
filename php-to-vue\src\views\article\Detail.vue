<template>
  <div class="article-detail-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>公告详情</span>
        <el-button type="text" icon="el-icon-back" @click="goBack">返回列表</el-button>
      </div>
      
      <div v-loading="loading" class="detail-container">
        <template v-if="article.id">
          <h1 class="detail-title">{{ article.title }}</h1>
          
          <div class="detail-meta">
            <span><i class="el-icon-user"></i> {{ article.author || '管理员' }}</span>
            <span><i class="el-icon-time"></i> {{ article.time || article.created_at }}</span>
            <span>
              <el-tag :type="article.status === 1 ? 'success' : 'info'" size="small">
                {{ article.status === 1 ? '已发布' : '草稿' }}
              </el-tag>
            </span>
          </div>
          
          <el-divider></el-divider>
          
          <div class="detail-content" v-html="article.content || article.description"></div>
        </template>
        
        <div v-else class="empty-data">
          <i class="el-icon-document"></i>
          <p>没有找到该公告或公告已被删除</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getArticleDetail } from '@/api/article'

export default {
  name: 'ArticleDetail',
  data() {
    return {
      loading: false,
      article: {}
    }
  },
  created() {
    this.fetchArticle()
  },
  methods: {
    fetchArticle() {
      const id = this.$route.params.id
      if (!id) {
        this.$message.error('公告ID不存在')
        return
      }
      
      this.loading = true
      getArticleDetail(id)
        .then(response => {
          this.article = response
          this.loading = false
          // 更新页面标题
          document.title = `${this.article.title} - 公告详情`
        })
        .catch(error => {
          console.error('获取公告详情失败:', error)
          this.$message.error('获取公告详情失败')
          this.loading = false
        })
    },
    
    goBack() {
      this.$router.push('/article/list')
    }
  }
}
</script>

<style scoped>
.article-detail-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.clearfix {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-container {
  padding: 20px;
}

.detail-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}

.detail-meta {
  display: flex;
  justify-content: center;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.detail-meta span {
  margin: 0 15px;
}

.detail-content {
  line-height: 1.8;
  font-size: 16px;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  color: #909399;
}

.empty-data i {
  font-size: 60px;
  margin-bottom: 20px;
}
</style>