<template>
  <div class="project-content-list">
    <div class="page-header">
      <h1 class="page-title">项目内容管理</h1>
      <el-button type="primary" @click="$router.push('/project-content/create')">
        <i class="el-icon-plus"></i> 创建项目内容
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="项目ID">
          <el-input v-model="filterForm.project_id" placeholder="请输入项目ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="域名">
          <el-input v-model="filterForm.domain" placeholder="请输入域名" clearable></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="searchContents">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 项目内容列表 -->
    <el-card shadow="hover" class="content-table-container">
      <div slot="header">
        <span>项目内容列表</span>
        <el-dropdown style="float: right; cursor: pointer;" @command="handleBatchCommand">
          <span class="el-dropdown-link">
            批量操作 <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="delete">批量删除</el-dropdown-item>
            <el-dropdown-item command="export">导出数据</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-table :data="contents" stripe style="width: 100%" @selection-change="handleSelectionChange"
        v-loading="loading">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="cid" label="ID" width="80"></el-table-column>
        <el-table-column prop="project_id" label="项目ID" width="100"></el-table-column>
        <el-table-column prop="domain" label="域名" min-width="120"></el-table-column>
        <el-table-column prop="content" label="接收内容" min-width="150">
          <template slot-scope="scope">
            <el-tooltip effect="dark" :content="scope.row.content" placement="top">
              <div class="content-preview">{{ truncateText(scope.row.content, 30) }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="server" label="服务器内容" min-width="150">
          <template slot-scope="scope">
            <el-tooltip effect="dark" :content="scope.row.server" placement="top">
              <div class="content-preview">{{ truncateText(scope.row.server, 30) }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="source_file" label="源码文件" min-width="120">
          <template slot-scope="scope">
            <el-button v-if="scope.row.source_file" size="mini" type="text" @click="openSourceFile(scope.row.source_file)">
              查看源码
            </el-button>
            <span v-else>暂无源码</span>
          </template>
        </el-table-column>
        <el-table-column prop="screenshot" label="屏幕截图" width="100">
          <template slot-scope="scope">
            <el-image 
              v-if="scope.row.screenshot" 
              style="width: 50px; height: 30px; cursor: pointer;" 
              :src="getImageUrl(scope.row.screenshot)"
              :preview-src-list="[getImageUrl(scope.row.screenshot)]">
            </el-image>
            <span v-else>暂无截图</span>
          </template>
        </el-table-column>
        <el-table-column prop="hash" label="哈希值" width="120"></el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>

        <el-table-column label="操作" width="240" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="viewContent(scope.row)">查看</el-button>
            <el-button size="mini" type="warning" @click="editContent(scope.row)">编辑</el-button>
            <el-button v-if="!scope.row.deleted" size="mini" type="danger" @click="deleteContent(scope.row)">删除</el-button>
            <el-button v-else size="mini" type="success" @click="restoreContent(scope.row)">恢复</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="pagination.page" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.page_size"
          layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getProjectContentList, deleteProjectContentById, restoreProjectContent } from '@/api/projectContent'
import { getImageUrl, truncateText } from '@/utils/helpers'

export default {
  name: 'ProjectContentList',
  data() {
    return {
      loading: false,
      // 筛选表单
      filterForm: {
        project_id: '',
        domain: '',
      },
      // 内容列表数据
      contents: [],
      // 分页
      pagination: {
        page: 1,
        page_size: 10,
        total: 0
      },
      // 选中的行
      multipleSelection: []
    }
  },
  created() {
    // 检查URL是否有项目ID参数
    const projectId = this.$route.query.project_id
    if (projectId) {
      this.filterForm.project_id = projectId
    }
    this.fetchContents()
  },
  methods: {
    // 处理图片URL
    getImageUrl,
    
    // 截断文本
    truncateText,

    // 查看源码文件
    viewSourceFile(row) {
      this.$alert(row.source_file, '源码文件', {
        dangerouslyUseHTMLString: false,
        customClass: 'source-file-dialog'
      });
    },

    // 获取项目内容列表
    fetchContents() {
      this.loading = true
      const params = {
        page: this.pagination.page,
        page_size: this.pagination.page_size,
        with_deleted: true,
        ...this.filterForm
      }

      // 移除值为null或空字符串的参数
      Object.keys(params).forEach(key => {
        if (params[key] === null || params[key] === '') {
          delete params[key]
        }
      })
      
      getProjectContentList(params)
        .then(response => {
          this.contents = response.items || []
          this.pagination.total = response.total || 0
          this.loading = false
        })
        .catch(error => {
          this.$message.error(`获取项目内容列表失败: ${error.message}`)
          this.loading = false
        })
    },

    // 搜索内容
    searchContents() {
      this.pagination.page = 1
      this.fetchContents()
    },

    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        project_id: '',
        domain: '',
        state: null
      }
      this.searchContents()
    },

    // 批量操作
    handleBatchCommand(command) {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择内容')
        return
      }
      if (command === 'delete') {
        this.$confirm('确认删除选中的项目内容吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 批量删除的逻辑实现
          const deletePromises = this.multipleSelection.map(item => deleteProjectContentById(item.cid))
          Promise.all(deletePromises)
            .then(() => {
              this.$message.success('批量删除成功')
              this.fetchContents()
            })
            .catch(error => {
              this.$message.error(`批量删除失败: ${error.message}`)
            })
        }).catch(() => {
          // 取消删除
        })
      } else if (command === 'export') {
        this.$message.success('开始导出数据，请稍候...')
        // 导出数据逻辑
      }
    },

    // 选择行变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.page_size = val
      this.fetchContents()
    },

    // 页码变化
    handleCurrentChange(val) {
      this.pagination.page = val
      this.fetchContents()
    },

    // 查看内容详情
    viewContent(row) {
      this.$router.push(`/project-content/${row.cid}`)
    },

    // 编辑内容
    editContent(row) {
      this.$router.push(`/project-content/edit/${row.cid}`)
    },

    // 删除内容
    deleteContent(row) {
      this.$confirm(`确认删除ID为 "${row.cid}" 的项目内容吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteProjectContentById(row.cid)
          .then(() => {
            this.$message.success('删除成功')
            this.fetchContents()
          })
          .catch(error => {
            this.$message.error(`删除失败: ${error.message}`)
            this.loading = false
          })
      }).catch(() => {
        // 取消删除
      })
    },

    // 恢复内容
    restoreContent(row) {
      this.$confirm(`确认恢复ID为 "${row.cid}" 的项目内容吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.loading = true
        restoreProjectContent(row.cid)
          .then(() => {
            this.$message.success('恢复成功')
            this.fetchContents()
          })
          .catch(error => {
            this.$message.error(`恢复失败: ${error.message}`)
            this.loading = false
          })
      }).catch(() => {
        // 取消恢复
      })
    },

    // 打开源码文件
    openSourceFile(url) {
      window.open(getImageUrl(url), '_blank');
    },

  }
}
</script>

<style scoped>
.project-content-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.content-table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-dropdown-link {
  color: #409EFF;
  cursor: pointer;
}

.content-preview {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.source-file-dialog {
  width: 80%;
  max-width: 800px;
}

.source-file-dialog .el-message-box__content {
  max-height: 400px;
  overflow: auto;
  white-space: pre-wrap;
  font-family: monospace;
}

</style> 