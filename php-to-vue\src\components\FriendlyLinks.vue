<template>
  <div class="friendly-links-container" v-loading="loading">
    <h3 class="friendly-links-title">{{ title }}</h3>
    <div class="friendly-links-list">
      <template v-if="friendlyList.length > 0">
        <a 
          v-for="item in friendlyList" 
          :key="item.id" 
          :href="item.href" 
          target="_blank" 
          class="friendly-link-item"
          :title="item.name">
          <div class="friendly-link-content">
            <img v-if="item.thumb" :src="item.thumb" :alt="item.name" class="friendly-link-logo">
            <span class="friendly-link-name">{{ item.name }}</span>
          </div>
        </a>
      </template>
      <div v-else class="friendly-links-empty">
        {{ emptyText }}
      </div>
    </div>
  </div>
</template>

<script>
import { getPublicFriendlyList } from '@/api/friendly'

export default {
  name: 'FriendlyLinks',
  props: {
    title: {
      type: String,
      default: '友情链接'
    },
    limit: {
      type: Number,
      default: 10
    },
    emptyText: {
      type: String,
      default: '暂无友情链接'
    }
  },
  data() {
    return {
      loading: false,
      friendlyList: []
    }
  },
  created() {
    this.fetchFriendlyList()
  },
  methods: {
    /**
     * 获取公开友情链接列表
     */
    fetchFriendlyList() {
      this.loading = true
      getPublicFriendlyList({
        page: 1,
        page_size: this.limit
      })
        .then(response => {
          // 如果返回的数据是数组，直接使用
          if (Array.isArray(response)) {
            this.friendlyList = response
          } 
          // 如果返回的是标准的数据结构，包含items属性
          else if (response && Array.isArray(response.items)) {
            this.friendlyList = response.items
          }
          // 如果返回的是包装在data中的数组
          else if (response && Array.isArray(response.data)) {
            this.friendlyList = response.data
          }
          // 其他情况，设置为空数组
          else {
            this.friendlyList = []
          }
        })
        .catch(error => {
          console.error('获取友情链接失败:', error)
          this.friendlyList = []
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style scoped>
.friendly-links-container {
  margin: 15px 0;
}

.friendly-links-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.friendly-links-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.friendly-link-item {
  text-decoration: none;
  padding: 8px 12px;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
}

.friendly-link-item:hover {
  background-color: #f0f0f0;
  border-color: #dcdcdc;
}

.friendly-link-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.friendly-link-logo {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.friendly-link-name {
  color: #666;
  font-size: 14px;
}

.friendly-links-empty {
  width: 100%;
  text-align: center;
  color: #999;
  padding: 10px;
}
</style> 