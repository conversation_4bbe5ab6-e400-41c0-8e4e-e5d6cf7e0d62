package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupSettingsRoutes 设置设置相关路由
func SetupSettingsRoutes(router fiber.Router) {
	// 需要认证和权限的路由
	settings := router.Group("/settings")
	settings.Use(utils.RequireAuthentication)
	
	// 获取设置
	settings.Get("/", middleware.RequirePermission("settings.view"), handlers.GetSettings)
	
	// 更新设置
	settings.Put("/", middleware.RequirePermission("settings.edit"), handlers.UpdateSettings)
} 