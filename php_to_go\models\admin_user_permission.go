package models

import (
	"time"
)

// AdminUserPermission 用户-权限关联模型
type AdminUserPermission struct {
	ID           uint64    `json:"id" gorm:"primaryKey;type:bigint unsigned;autoIncrement;comment:ID"`
	RoleID       uint64    `json:"role_id" gorm:"not null;index:idx_role_permission;type:bigint unsigned;comment:角色ID"`
	PermissionID uint64    `json:"permission_id" gorm:"not null;index:idx_role_permission;type:bigint unsigned;comment:权限ID"`
	CreatedAt    time.Time `json:"created_at" gorm:"comment:创建时间戳"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"comment:更新时间戳"`
}

// TableName 指定数据库表名
func (AdminUserPermission) TableName() string {
	return "admin_role_permissions"
} 