package config

import (
	"os"
	"strconv"
	"time"
	"github.com/joho/godotenv"
)

// Config 应用配置结构体
type Config struct {
	AppName       string
	AppEnv        string
	Port          string
	DBConfig      DBConfig
	RedisConfig   RedisConfig   // Redis 配置
	JWTSecret     string        // JWT 密钥
	JWTExpiration time.Duration // JWT 过期时间
	AppInvitation string        // 邀请码模式 ("none" 或其他)
	SMTPHost      string        // SMTP 服务器地址
	SMTPPort      int           // SMTP 服务器端口
	SMTPUser      string        // SMTP 用户名
	SMTPPassword  string        // SMTP 密码
	SMTPFrom      string        // 发件人邮箱
	WebBaseURL    string        // Web前端基础URL，用于构建重置密码链接
}

// DBConfig 数据库配置结构体
type DBConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
}

// RedisConfig Redis 配置结构体
type RedisConfig struct {
	Addr     string // Redis 地址 (e.g., "localhost:6379")
	Password string // Redis 密码 (留空如果没有密码)
	DB       int    // Redis 数据库编号 (e.g., 0)
}

// LoadConfig 从环境变量加载配置
func LoadConfig() *Config {
	// 加载 .env 文件（只需加载一次即可，重复加载无害）
	_ = godotenv.Load()

	jwtExpHoursStr := os.Getenv("JWT_EXPIRATION_HOURS")
	jwtExpHours, err := strconv.Atoi(jwtExpHoursStr)
	if err != nil {
		jwtExpHours = 72 // Default to 72 hours on error
	}
	jwtExpiration := time.Duration(jwtExpHours) * time.Hour

	redisDBStr := os.Getenv("REDIS_DB")
	redisDB, err := strconv.Atoi(redisDBStr)
	if err != nil {
		redisDB = 0 // Default to DB 0 on error
	}

	smtpPortStr := os.Getenv("SMTP_PORT")
	smtpPort, err := strconv.Atoi(smtpPortStr)
	if err != nil {
		smtpPort = 587 // Default SMTP port on error
	}

	return &Config{
		AppName: os.Getenv("APP_NAME"),
		AppEnv:  os.Getenv("APP_ENV"),
		Port:    os.Getenv("PORT"),
		DBConfig: DBConfig{
			Host:     os.Getenv("DB_HOST"),
			Port:     os.Getenv("DB_PORT"),
			User:     os.Getenv("DB_USER"),
			Password: os.Getenv("DB_PASSWORD"),
			DBName:   os.Getenv("DB_NAME"),
		},
		RedisConfig: RedisConfig{
			Addr:     os.Getenv("REDIS_ADDR"),
			Password: os.Getenv("REDIS_PASSWORD"),
			DB:       redisDB,
		},
		JWTSecret:     os.Getenv("JWT_SECRET"),
		JWTExpiration: jwtExpiration,
		AppInvitation: os.Getenv("APP_INVITATION"),
		SMTPHost:      os.Getenv("SMTP_HOST"),
		SMTPPort:      smtpPort,
		SMTPUser:      os.Getenv("SMTP_USER"),
		SMTPPassword:  os.Getenv("SMTP_PASSWORD"),
		SMTPFrom:      os.Getenv("SMTP_FROM"),
		WebBaseURL:    os.Getenv("WEB_BASE_URL"),
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}
