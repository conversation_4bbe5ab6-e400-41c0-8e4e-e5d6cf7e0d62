package utils

import (
	"go-fiber-api/api/dto"
	"math"

	"github.com/gofiber/fiber/v2"
)

// Success 通用成功响应
func Success(c *fiber.Ctx, message string, data interface{}) error {
	if message == "" {
		message = "操作成功"
	}
	return c.Status(fiber.StatusOK).JSON(dto.StandardResponse{
		Code:    0,
		Message: message,
		Data:    data,
	})
}

// Fail 通用失败响应
func Fail(c *fiber.Ctx, statusCode int, errorCode int, message string, err error) error {
	if message == "" {
		message = "操作失败"
	}
	resp := dto.StandardResponse{
		Code:    errorCode,
		Message: message,
	}
	if err != nil {
		resp.Error = err.Error() // 仅在开发模式下暴露详细错误可能更安全
	}
	return c.Status(statusCode).JSON(resp)
}

// SuccessPaginated 分页成功响应
func SuccessPaginated(c *fiber.Ctx, message string, items interface{}, total int64, page, pageSize int) error {
	if message == "" {
		message = "获取成功"
	}
	
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))
	
	paginatedData := dto.PaginatedResponse{
		Items:      items,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}
	
	return Success(c, message, paginatedData)
}

// --- 常用错误响应封装 ---

// BadRequest 400 错误请求
func BadRequest(c *fiber.Ctx, message string, err error) error {
	return Fail(c, fiber.StatusBadRequest, 400, message, err)
}

// Unauthorized 401 未授权
func Unauthorized(c *fiber.Ctx, message string, err error) error {
	return Fail(c, fiber.StatusUnauthorized, 401, message, err)
}

// Forbidden 403 禁止访问
func Forbidden(c *fiber.Ctx, message string, err error) error {
	return Fail(c, fiber.StatusForbidden, 403, message, err)
}

// NotFound 404 资源未找到
func NotFound(c *fiber.Ctx, message string) error {
	return Fail(c, fiber.StatusNotFound, 404, message, nil)
}

// ServerError 500 服务器内部错误
func ServerError(c *fiber.Ctx, message string, err error) error {
	return Fail(c, fiber.StatusInternalServerError, 500, message, err)
}
