package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
)

// SetupAdminRoutes 设置管理员相关路由
func SetupAdminRoutes(router fiber.Router) {
	admin := router.Group("/admin")

	// 使用认证中间件保护管理员路由
	admin.Use(utils.RequireAuthentication)

	// 使用操作日志中间件记录管理员操作
	admin.Use(middleware.OperationLogMiddleware())

	// 管理员用户路由
	users := admin.Group("/users")
	users.Get("/", middleware.RequirePermission("users.view"), handlers.GetAdminUsers)
	users.Get("/:id", middleware.RequirePermission("users.view"), handlers.GetAdminUser)
	users.Post("/", middleware.RequirePermission("users.create"), handlers.CreateAdminUser)
	users.Put("/:id", middleware.RequirePermission("users.edit"), handlers.UpdateAdminUser)
	users.Delete("/:id", middleware.RequirePermission("users.delete"), handlers.DeleteAdminUser)


}
