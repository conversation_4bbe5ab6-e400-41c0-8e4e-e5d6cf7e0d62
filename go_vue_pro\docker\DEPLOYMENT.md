# 部署指南

本项目使用Docker Compose进行部署，使用宝塔面板管理的服务器环境。

## 前提条件

- 远程服务器已安装Docker和Docker Compose
- 远程服务器已有宝塔面板，运行MySQL 5.7（端口3306）和Redis服务
- Nginx已通过宝塔面板配置好

## 部署步骤

### 1. 准备环境变量文件

在docker目录下创建`.env`文件：

```
# MySQL配置 - 注意端口为3308，避免与宝塔默认MySQL冲突
DB_HOST=mysql
DB_PORT=3308
DB_USER=root
DB_PASSWORD=your_password  # 设置安全的MySQL密码
DB_NAME=go_fiber

# Redis配置 - 使用宝塔已有的Redis服务
HOST_REDIS_ADDR=127.0.0.1:6379

# API服务端口
PORT=3000
```

### 2. 启动后端服务

```bash
cd /path/to/project/docker
docker-compose up -d
```

服务将在以下端口可用：
- MySQL 8数据库: 3308端口
- API服务: 3000端口
- 监控面板: 8080端口

### 3. 构建前端代码

```bash
# 克隆前端代码
git clone https://density11:<EMAIL>/liguangsheng1/php-to-vue.git frontend

# 进入前端目录
cd frontend

# 创建环境变量配置
cat > .env.production << EOF
VUE_APP_BASE_API=/api
VUE_APP_WS_API=ws://go.xss4.com/ws
NODE_ENV=production
EOF

# 安装依赖和构建
npm install
npm run build

# 复制构建文件到网站目录
cp -r dist/* /www/wwwroot/go.xss4.com/
```

### 4. 配置Nginx

已经配置好的Nginx配置文件(`go-fiber-app.conf`)应该已经包含了所有必要的代理配置。
确保该配置文件在宝塔面板的站点设置中生效。

### 注意事项

1. **数据库访问**：
   - 应用内部（容器内）访问MySQL时使用`mysql`作为主机名和默认端口3306
   - 从宿主机访问Docker中的MySQL 8时，使用`localhost:3308`
   - 原有的宝塔MySQL 5.7仍然在`localhost:3306`可用

2. **Redis配置**：
   - 确保Redis允许Docker容器访问：
     - 编辑Redis配置文件(通常是`/etc/redis/redis.conf`)
     - 将`bind 127.0.0.1`改为`bind 0.0.0.0`或添加你的Docker内部网络
     - 如果有防火墙，确保允许Docker网络访问Redis端口

3. 服务启动失败时，可以查看日志：
   ```bash
   docker-compose logs api
   docker-compose logs worker
   ```

4. 如果需要导入数据，可以使用：
   ```bash
   # 从宿主机访问Docker中的MySQL
   mysql -h127.0.0.1 -P3308 -uroot -p'your_password' go_fiber < backup.sql
   ``` 