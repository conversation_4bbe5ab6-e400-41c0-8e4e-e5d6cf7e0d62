package handlers

import (
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// GetInvitations 获取邀请码列表
// @Summary 获取邀请码列表
// @Description 获取邀请码列表，支持分页和筛选
// @Tags 邀请码管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码，默认为1"
// @Param page_size query int false "每页条数，默认为10"
// @Param code query string false "邀请码(精确匹配)"
// @Param state query int false "状态"
// @Success 200 {object} dto.StandardResponse{data=dto.PaginatedResponse{items=[]dto.InvitationListItem}}
// @Router /admin/invitation/index [get]
func GetInvitations(c *fiber.Ctx) error {
	// 获取分页参数
	page, _ := strconv.Atoi(c.Query("page", "1"))
	pageSize, _ := strconv.Atoi(c.Query("page_size", "10"))
	
	// 获取筛选参数
	code := c.Query("code", "")
	state := c.Query("state", "")
	
	// 构建查询
	query := database.DB.Model(&models.Invitation{})
	
	// 应用筛选条件
	if code != "" {
		query = query.Where("code = ?", code)
	}
	
	if state != "" {
		stateInt, err := strconv.Atoi(state)
		if err == nil {
			query = query.Where("state = ?", stateInt)
		}
	}
	
	// 计算总数
	var total int64
	query.Count(&total)
	
	// 获取分页数据
	var invitations []models.Invitation
	query.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&invitations)
	
	// 转换为DTO
	var invitationDTOs []dto.InvitationListItem
	for _, invitation := range invitations {
		usedAt := ""
		if invitation.UsedAt != nil {
			usedAt = invitation.UsedAt.Format("2006-01-02 15:04:05")
		}
		
		expiredAt := ""
		if invitation.ExpiredAt != nil {
			expiredAt = invitation.ExpiredAt.Format("2006-01-02 15:04:05")
		}
		
		invitationDTOs = append(invitationDTOs, dto.InvitationListItem{
			ID:          invitation.ID,
			Code:        invitation.Code,
			Description: invitation.Description,
			UserID:      invitation.UserID,
			UsedAt:      usedAt,
			ExpiredAt:   expiredAt,
			State:       invitation.State,
			UsedCount:   invitation.UsedCount,
			Limit:       invitation.Limit,
			CreatedAt:   invitation.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	
	// 返回分页响应
	return utils.SuccessPaginated(c, "获取邀请码列表成功", invitationDTOs, total, page, pageSize)
}

// GetInvitation 获取单个邀请码详情
// @Summary 获取邀请码详情
// @Description 根据ID获取邀请码详情
// @Tags 邀请码管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "邀请码ID"
// @Success 200 {object} dto.StandardResponse{data=dto.InvitationResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "邀请码不存在"
// @Router /admin/invitation/index/{id} [get]
func GetInvitation(c *fiber.Ctx) error {
	// 获取邀请码ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的邀请码ID", err)
	}
	
	// 查询邀请码
	var invitation models.Invitation
	if err := database.DB.First(&invitation, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "邀请码不存在")
		}
		return utils.ServerError(c, "获取邀请码失败", err)
	}
	
	// 转换为DTO
	usedAt := ""
	if invitation.UsedAt != nil {
		usedAt = invitation.UsedAt.Format("2006-01-02 15:04:05")
	}
	
	expiredAt := ""
	if invitation.ExpiredAt != nil {
		expiredAt = invitation.ExpiredAt.Format("2006-01-02 15:04:05")
	}
	
	invitationDTO := dto.InvitationResponse{
		ID:          invitation.ID,
		Code:        invitation.Code,
		Description: invitation.Description,
		UserID:      invitation.UserID,
		UsedAt:      usedAt,
		ExpiredAt:   expiredAt,
		State:       invitation.State,
		UsedCount:   invitation.UsedCount,
		Limit:       invitation.Limit,
		CreatedAt:   invitation.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   invitation.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "获取邀请码成功", invitationDTO)
}

// CreateInvitation 创建邀请码
// @Summary 创建邀请码
// @Description 创建新的邀请码，支持批量生成
// @Tags 邀请码管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param invitation body dto.InvitationRequest true "邀请码信息"
// @Success 200 {object} dto.StandardResponse{data=[]dto.InvitationResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/invitation/index [post]
func CreateInvitation(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.InvitationRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	
	// 批量生成数量(默认为1)
	count := req.Count
	if count <= 0 {
		count = 1
	}
	
	// 创建邀请码数组
	var createdInvitations []dto.InvitationResponse
	var invitations []models.Invitation
	
	// 批量生成邀请码
	for i := 0; i < count; i++ {
		// 如果是批量生成，则自动生成随机邀请码
		invitationCode := req.Code
		if count > 1 || invitationCode == "" {
			invitationCode = utils.GenerateRandomCode(8) // 生成8位随机邀请码
		}
		
		// 检查邀请码是否已存在
		var existCount int64
		if err := database.DB.Model(&models.Invitation{}).Where("code = ?", invitationCode).Count(&existCount).Error; err != nil {
			return utils.ServerError(c, "检查邀请码失败", err)
		}
		
		if existCount > 0 {
			// 如果是单个创建且邀请码已存在，则返回错误
			if count == 1 {
				return utils.BadRequest(c, "邀请码已存在", nil)
			}
			// 如果是批量创建，则跳过重复的邀请码并重新生成
			i--
			continue
		}
		
		// 创建新邀请码
		invitation := models.Invitation{
			Code:        invitationCode,
			Description: req.Description,
			UserID:      userID,
			State:       req.State,
			Limit:       req.Limit, // 设置使用次数限制
		}
		
		// 设置过期时间
		if req.ExpiredAt != "" {
			expiredAt, err := time.Parse("2006-01-02 15:04:05", req.ExpiredAt)
			if err == nil {
				invitation.ExpiredAt = &expiredAt
			}
		}
		
		invitations = append(invitations, invitation)
	}
	
	// 批量插入数据库
	if err := database.DB.Create(&invitations).Error; err != nil {
		return utils.ServerError(c, "创建邀请码失败", err)
	}
	
	// 转换为DTO
	for _, invitation := range invitations {
		usedAt := ""
		if invitation.UsedAt != nil {
			usedAt = invitation.UsedAt.Format("2006-01-02 15:04:05")
		}
		
		expiredAt := ""
		if invitation.ExpiredAt != nil {
			expiredAt = invitation.ExpiredAt.Format("2006-01-02 15:04:05")
		}
		
		createdInvitations = append(createdInvitations, dto.InvitationResponse{
			ID:          invitation.ID,
			Code:        invitation.Code,
			Description: invitation.Description,
			UserID:      invitation.UserID,
			UsedAt:      usedAt,
			ExpiredAt:   expiredAt,
			State:       invitation.State,
			UsedCount:   invitation.UsedCount,
			Limit:       invitation.Limit,
			CreatedAt:   invitation.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   invitation.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	
	return utils.Success(c, "创建邀请码成功", createdInvitations)
}

// UpdateInvitation 更新邀请码
// @Summary 更新邀请码
// @Description 更新邀请码信息
// @Tags 邀请码管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "邀请码ID"
// @Param invitation body dto.InvitationUpdateRequest true "邀请码更新信息"
// @Success 200 {object} dto.StandardResponse{data=dto.InvitationResponse}
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "邀请码不存在"
// @Router /admin/invitation/index/{id} [put]
func UpdateInvitation(c *fiber.Ctx) error {
	// 获取邀请码ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的邀请码ID", err)
	}
	
	// 查询邀请码
	var invitation models.Invitation
	if err := database.DB.First(&invitation, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "邀请码不存在")
		}
		return utils.ServerError(c, "获取邀请码失败", err)
	}
	
	// 解析请求体
	var req dto.InvitationUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 准备更新数据
	updates := make(map[string]interface{})
	
	if req.Code != nil {
		// 检查邀请码是否已存在（排除自己）
		var count int64
		if err := database.DB.Model(&models.Invitation{}).Where("code = ? AND id != ?", *req.Code, id).Count(&count).Error; err != nil {
			return utils.ServerError(c, "检查邀请码失败", err)
		}
		
		if count > 0 {
			return utils.BadRequest(c, "邀请码已存在", nil)
		}
		
		updates["code"] = *req.Code
	}
	
	// 更新描述字段
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	
	// 更新状态字段
	if req.State != nil {
		updates["state"] = *req.State
	}
	
	// 更新使用次数限制
	if req.Limit != nil {
		updates["limit"] = *req.Limit
	}
	
	// 更新过期时间
	if req.ExpiredAt != nil {
		if *req.ExpiredAt == "" {
			// 如果传入空字符串，表示取消过期时间设置
			updates["expired_at"] = nil
		} else {
			// 否则解析日期字符串为时间对象
			expiredAt, err := time.Parse("2006-01-02 15:04:05", *req.ExpiredAt)
			if err == nil {
				updates["expired_at"] = expiredAt
			}
		}
	}
	
	// 执行更新
	if err := database.DB.Model(&invitation).Updates(updates).Error; err != nil {
		return utils.ServerError(c, "更新邀请码失败", err)
	}
	
	// 重新获取更新后的邀请码
	if err := database.DB.First(&invitation, id).Error; err != nil {
		return utils.ServerError(c, "获取更新后的邀请码失败", err)
	}
	
	// 转换为DTO
	usedAt := ""
	if invitation.UsedAt != nil {
		usedAt = invitation.UsedAt.Format("2006-01-02 15:04:05")
	}
	
	expiredAt := ""
	if invitation.ExpiredAt != nil {
		expiredAt = invitation.ExpiredAt.Format("2006-01-02 15:04:05")
	}
	
	invitationDTO := dto.InvitationResponse{
		ID:          invitation.ID,
		Code:        invitation.Code,
		Description: invitation.Description,
		UserID:      invitation.UserID,
		UsedAt:      usedAt,
		ExpiredAt:   expiredAt,
		State:       invitation.State,
		UsedCount:   invitation.UsedCount,
		Limit:       invitation.Limit,
		CreatedAt:   invitation.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   invitation.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	
	return utils.Success(c, "更新邀请码成功", invitationDTO)
}

// DeleteInvitation 删除邀请码
// @Summary 删除邀请码
// @Description 删除现有邀请码
// @Tags 邀请码管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "邀请码ID"
// @Success 200 {object} dto.StandardResponse "删除成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "邀请码不存在"
// @Router /admin/invitation/index/{id} [delete]
func DeleteInvitation(c *fiber.Ctx) error {
	// 获取邀请码ID
	id, err := strconv.ParseUint(c.Params("id"), 10, 64)
	if err != nil {
		return utils.BadRequest(c, "无效的邀请码ID", err)
	}
	
	// 查询邀请码
	var invitation models.Invitation
	if err := database.DB.First(&invitation, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return utils.NotFound(c, "邀请码不存在")
		}
		return utils.ServerError(c, "获取邀请码失败", err)
	}
	
	// 软删除邀请码
	if err := database.DB.Delete(&invitation).Error; err != nil {
		return utils.ServerError(c, "删除邀请码失败", err)
	}
	
	return utils.Success(c, "删除邀请码成功", nil)
}

// BatchOperateInvitations 批量操作邀请码
// @Summary 批量操作邀请码
// @Description 批量启用/禁用/删除邀请码
// @Tags 邀请码管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param batch body dto.InvitationBatchRequest true "批量操作请求"
// @Success 200 {object} dto.StandardResponse "操作成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Router /admin/invitation/batch [post]
func BatchOperateInvitations(c *fiber.Ctx) error {
	// 解析请求体
	var req dto.InvitationBatchRequest
	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}
	
	// 验证请求数据
	if err := utils.ValidateStruct(req); err != nil {
		return utils.BadRequest(c, "数据验证失败", err)
	}
	
	// 检查ID列表不为空
	if len(req.IDs) == 0 {
		return utils.BadRequest(c, "ID列表不能为空", nil)
	}
	
	// 根据操作类型执行不同的操作
	switch req.Action {
	case "enable":
		// 批量启用
		if err := database.DB.Model(&models.Invitation{}).Where("id IN ?", req.IDs).Update("state", 1).Error; err != nil {
			return utils.ServerError(c, "批量启用邀请码失败", err)
		}
		return utils.Success(c, "批量启用邀请码成功", nil)
		
	case "disable":
		// 批量禁用
		if err := database.DB.Model(&models.Invitation{}).Where("id IN ?", req.IDs).Update("state", 0).Error; err != nil {
			return utils.ServerError(c, "批量禁用邀请码失败", err)
		}
		return utils.Success(c, "批量禁用邀请码成功", nil)
		
	case "delete":
		// 批量删除
		if err := database.DB.Where("id IN ?", req.IDs).Delete(&models.Invitation{}).Error; err != nil {
			return utils.ServerError(c, "批量删除邀请码失败", err)
		}
		return utils.Success(c, "批量删除邀请码成功", nil)
		
	default:
		return utils.BadRequest(c, "不支持的操作类型", nil)
	}
} 