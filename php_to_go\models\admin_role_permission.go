package models

import (
	"time"
)

// AdminRolePermission 角色-权限关联表
type AdminRolePermission struct {
	RoleID       uint64    `gorm:"primaryKey;type:bigint;not null"`
	PermissionID uint64    `gorm:"primaryKey;type:bigint;not null"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

func (AdminRolePermission) TableName() string {
	return "admin_role_permissions"
} 