import request from './index'

/**
 * 获取文章列表
 * @param {Object} params - 查询参数
 * @param {string} [params.title] - 文章标题(模糊查询)
 * @param {number} [params.state] - 状态(1:已发布, 0:草稿, 不传:所有)
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getArticleList(params) {
  return request({
    url: '/admin/article/index',
    method: 'get',
    params
  })
}

/**
 * 创建文章
 * @param {Object} data - 文章数据
 * @param {string} data.title - 文章标题
 * @param {string} data.content - 文章内容
 * @param {number} [data.state=0] - 状态(1:已发布, 0:草稿)
 * @param {string} [data.summary] - 文章摘要
 * @param {string} [data.cover] - 封面图片URL
 * @returns {Promise}
 */
export function createArticle(data) {
  return request({
    url: '/admin/article/index',
    method: 'post',
    data
  })
}

/**
 * 获取单个文章详情
 * @param {number} id - 文章ID
 * @returns {Promise}
 */
export function getArticle(id) {
  return request({
    url: `/admin/article/index/${id}`,
    method: 'get'
  })
}

/**
 * 获取公告详情（后台接口）
 * @param {number} id - 公告ID
 * @returns {Promise}
 */
export function getArticleDetail(id) {
  return request({
    url: `/admin/article/index/${id}`,
    method: 'get'
  })
}

/**
 * 更新文章
 * @param {number} id - 文章ID
 * @param {Object} data - 需要更新的文章数据
 * @param {string} [data.title] - 文章标题
 * @param {string} [data.content] - 文章内容
 * @param {number} [data.state] - 状态(1:已发布, 0:草稿)
 * @param {string} [data.summary] - 文章摘要
 * @param {string} [data.cover] - 封面图片URL
 * @returns {Promise}
 */
export function updateArticle(id, data) {
  return request({
    url: `/admin/article/index/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除文章
 * @param {number} id - 文章ID
 * @returns {Promise}
 */
export function deleteArticle(id) {
  return request({
    url: `/admin/article/index/${id}`,
    method: 'delete'
  })
}

/**
 * 发布文章
 * @param {number} id - 文章ID
 * @param {number} [delayMinutes] - 延迟发布时间(分钟)
 * @returns {Promise}
 */
export function publishArticle(id, delayMinutes) {
  const params = {}
  if (delayMinutes !== undefined) {
    params.delay_minutes = delayMinutes
  }
  
  return request({
    url: `/admin/article/publish/${id}`,
    method: 'post',
    params
  })
}

/**
 * 获取公告列表
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getAnnouncementList(params) {
  return request({
    url: '/announcements',
    method: 'get',
    params
  })
} 