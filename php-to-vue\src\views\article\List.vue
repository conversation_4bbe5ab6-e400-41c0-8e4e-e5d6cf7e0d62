<template>
  <div class="article-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>公告管理</span>
        <el-button type="primary" size="small" class="create-btn" @click="handleCreate" icon="el-icon-plus">
          新建公告
        </el-button>
      </div>

      <!-- 搜索过滤区域 -->
      <div class="filter-container">
        <el-select v-model="listQuery.status" placeholder="状态" clearable style="width: 120px" class="filter-item">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
          搜索
        </el-button>
      </div>

      <!-- 数据表格 -->
      <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">
        <el-table-column align="center" label="ID" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>

        <el-table-column  align="center" label="标题">
          <template slot-scope="scope">
            <span class="link-type" @click="handleDetail(scope.row)">{{ scope.row.title }}</span>
          </template>
        </el-table-column>

        <el-table-column  align="center" label="作者">
          <template slot-scope="scope">
            <span>{{ scope.row.author || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column  align="center" label="状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '已发布' : '草稿' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column align="center" label="创建时间">
          <template slot-scope="scope">
            <span>{{ scope.row.created_at }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" label="操作" width="530">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" icon="el-icon-view" @click="handleDetail(scope.row)">
              查看
            </el-button>
            <el-button size="mini" type="success" icon="el-icon-edit" @click="handleUpdate(scope.row)">
              编辑
            </el-button>
            <el-button 
              v-if="scope.row.status === 0"
              size="mini" 
              type="warning" 
              icon="el-icon-s-promotion" 
              @click="handlePublish(scope.row)">
              发布
            </el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="listQuery.page" :page-sizes="[10, 20, 30, 50]" :page-size="listQuery.page_size"
          layout="total, sizes, prev, pager, next, jumper" :total="total" />
      </div>
    </el-card>

    <!-- 查看公告详情对话框 -->
    <el-dialog title="公告详情" :visible.sync="detailDialogVisible">
      <div v-loading="detailLoading" class="detail-container">
        <h2 class="detail-title">{{ currentArticle.title }}</h2>
        <div class="detail-meta">
          <span>作者：{{ currentArticle.author || '未设置' }}</span>
          <span>状态：{{ currentArticle.status === 1 ? '已发布' : '草稿' }}</span>
          <span>创建时间：{{ currentArticle.created_at }}</span>
          <span>更新时间：{{ currentArticle.updated_at }}</span>
        </div>
        <div class="detail-content">
          <div v-html="currentArticle.description"></div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 发布公告对话框 -->
    <el-dialog title="发布公告" :visible.sync="publishDialogVisible" width="30%">
      <div class="publish-container">
        <p>您确定要发布 <strong>{{ currentArticle.title }}</strong> 吗？</p>
        
        <el-form ref="publishForm" :model="publishForm" label-width="120px">
          <el-form-item label="延迟发布">
            <el-switch v-model="publishForm.isDelayed"></el-switch>
          </el-form-item>
          
          <el-form-item label="延迟时间(分钟)" v-if="publishForm.isDelayed">
            <el-input-number 
              v-model="publishForm.delayMinutes" 
              :min="1" 
              :max="1440"
              controls-position="right">
            </el-input-number>
          </el-form-item>
          
          <el-form-item label="WebSocket通知" v-if="wsSupported">
            <el-switch v-model="publishForm.sendNotification" disabled></el-switch>
            <div class="tip-text">发布后将自动通过WebSocket发送通知</div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="publishDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmPublish" :loading="publishLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getArticleList, getArticleDetail, deleteArticle, publishArticle } from '@/api/article'

export default {
  name: 'ArticleList',
  data() {
    return {
      list: [], // 公告列表数据
      total: 0, // 总记录数
      listLoading: true, // 列表加载状态
      listQuery: {
        page: 1,
        page_size: 10,
        status: undefined
      },
      statusOptions: [
        { label: '全部', value: undefined },
        { label: '已发布', value: 1 },
        { label: '草稿', value: 0 }
      ],
      detailDialogVisible: false,
      detailLoading: false,
      publishDialogVisible: false,
      publishLoading: false,
      publishForm: {
        isDelayed: false,
        delayMinutes: 5,
        sendNotification: true
      },
      wsSupported: true, // WebSocket支持状态，默认支持
      currentArticle: {} // 当前查看的公告详情
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取公告列表
    getList() {
      this.listLoading = true
      getArticleList(this.listQuery).then(response => {
        this.list = response.items
        this.total = response.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },

    // 处理过滤搜索
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    // 处理每页条数变化
    handleSizeChange(val) {
      this.listQuery.page_size = val
      this.getList()
    },

    // 处理页码变化
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },

    // 处理查看详情
    handleDetail(row) {
      this.detailDialogVisible = true
      this.detailLoading = true
      this.currentArticle = Object.assign({}, row) // 先显示基本信息

      // 获取完整详情
      getArticleDetail(row.id).then(response => {
        this.currentArticle = response
        this.detailLoading = false
      }).catch(() => {
        this.detailLoading = false
      })
    },

    // 处理创建公告
    handleCreate() {
      this.$router.push('/article/create')
    },

    // 处理更新公告
    handleUpdate(row) {
      this.$router.push(`/article/edit/${row.id}`)
    },

    // 处理删除公告
    handleDelete(row) {
      this.$confirm('此操作将永久删除该公告, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteArticle(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    
    // 处理发布公告
    handlePublish(row) {
      this.currentArticle = Object.assign({}, row)
      this.publishDialogVisible = true
      // 重置发布表单
      this.publishForm = {
        isDelayed: false,
        delayMinutes: 5,
        sendNotification: true
      }
    },
    
    // 确认发布
    confirmPublish() {
      this.publishLoading = true
      
      // 获取延迟分钟数
      const delayMinutes = this.publishForm.isDelayed ? this.publishForm.delayMinutes : undefined
      
      publishArticle(this.currentArticle.id, delayMinutes)
        .then(() => {
          this.$message({
            type: 'success',
            message: this.publishForm.isDelayed 
              ? `发布成功！将在${this.publishForm.delayMinutes}分钟后推送通知`
              : '发布成功！通知已发送'
          })
          this.publishDialogVisible = false
          this.publishLoading = false
          this.getList() // 刷新列表
        })
        .catch(error => {
          console.error('发布失败:', error)
          this.$message.error('发布失败，请重试')
          this.publishLoading = false
        })
    }
  }
}
</script>

<style scoped>
.article-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.create-btn {
  float: right;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 详情样式 */
.detail-container {
  padding: 0 20px;
}

.detail-title {
  text-align: center;
  margin-bottom: 20px;
}

.detail-meta {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.detail-content {
  line-height: 1.8;
}

/* 发布公告样式 */
.publish-container {
  padding: 0 20px;
}

.tip-text {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}
.article-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.create-btn {
  float: right;
}

.pagination-container {
  margin-top: 30px;
}

.detail-container {
  padding: 20px;
}

.detail-title {
  text-align: center;
  margin-bottom: 20px;
}

.detail-meta {
  display: flex;
  justify-content: space-around;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.detail-meta span {
  margin: 5px 10px;
}

.detail-content {
  border-top: 1px solid #eee;
  padding-top: 20px;
  min-height: 200px;
}

.link-type {
  color: #409EFF;
  cursor: pointer;
}

.link-type:hover {
  text-decoration: underline;
}
</style>