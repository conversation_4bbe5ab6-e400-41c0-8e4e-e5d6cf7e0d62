package processors

import (
	"context"
	"encoding/json"
	"fmt"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils/queue/tasks"
	"log"
	"time"

	"github.com/hibiken/asynq"
)

// ArticlePublishProcessor 处理公告发布任务
func ArticlePublishProcessor(ctx context.Context, t *asynq.Task) error {
	var payload tasks.ArticlePublishPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("错误的任务负载: %v", err)
	}

	log.Printf("处理公告发布任务, 公告ID: %d, 标题: %s\n", payload.ArticleID, payload.Title)

	// 从数据库加载公告
	var article models.Article
	if err := database.DB.First(&article, payload.ArticleID).Error; err != nil {
		return fmt.Errorf("公告不存在: %v", err)
	}

	// 更新公告状态为已发布
	if err := database.DB.Model(&article).Update("status", 1).Error; err != nil {
		return fmt.Errorf("更新公告状态失败: %v", err)
	}

	// 将公告信息广播到所有在线用户
	// 这里可以通过Redis发布消息，让WebSocket服务器推送给在线用户
	notificationData, err := json.Marshal(map[string]interface{}{
		"type":    "article",
		"id":      article.ID,
		"title":   article.Title,
		"content": article.Description,
		"author":  article.Author,
		"time":    time.Now().Format("2006-01-02 15:04:05"),
	})
	if err != nil {
		return fmt.Errorf("序列化通知数据失败: %v", err)
	}

	// 通过Redis的Pub/Sub机制发布消息
	if err := database.Rdb.Publish(ctx, "notifications", string(notificationData)).Err(); err != nil {
		log.Printf("发布通知消息失败: %v", err)
		// 这里我们记录错误但不中断处理流程
	}

	log.Printf("公告 %d 发布成功\n", article.ID)
	return nil
}
