import request from './index'

/**
 * 获取项目列表
 * @param {Object} params - 查询参数
 * @param {string} [params.name] - 项目名称(模糊查询)
 * @param {number} [params.state] - 状态(1:启用, 0:禁用, 不传:所有)
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getProjectList(params) {
  return request({
    url: '/admin/project/my',
    method: 'get',
    params
  })
}

/**
 * 创建项目
 * @param {Object} data - 项目数据
 * @param {string} data.name - 项目名称
 * @param {string} data.description - 项目描述
 * @param {number} [data.state=1] - 状态(1:启用, 0:禁用)
 * @param {string} [data.remark] - 备注
 * @returns {Promise}
 */
export function createProject(data) {
  return request({
    url: '/admin/project/my',
    method: 'post',
    data
  })
}

/**
 * 获取单个项目详情
 * @param {number} id - 项目ID
 * @returns {Promise}
 */
export function getProject(id) {
  return request({
    url: `/admin/project/my/${id}`,
    method: 'get'
  })
}

/**
 * 更新项目
 * @param {number} id - 项目ID
 * @param {Object} data - 需要更新的项目数据
 * @param {string} [data.name] - 项目名称
 * @param {string} [data.description] - 项目描述
 * @param {number} [data.state] - 状态(1:启用, 0:禁用)
 * @param {string} [data.remark] - 备注
 * @returns {Promise}
 */
export function updateProject(id, data) {
  return request({
    url: `/admin/project/my/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除项目
 * @param {number} id - 项目ID
 * @returns {Promise}
 */
export function deleteProject(id) {
  return request({
    url: `/admin/project/my/${id}`,
    method: 'delete'
  })
}

/**
 * 获取项目代码
 * @param {number} id - 项目ID
 * @returns {Promise}
 */
export function getProjectCode(id) {
  return request({
    url: `/admin/project/viewcode/${id}`,
    method: 'get'
  })
}

/**
 * 查看项目代码（别名，保持与组件兼容）
 * @param {number} id - 项目ID
 * @returns {Promise}
 */
export function viewProjectCode(id) {
  return getProjectCode(id)
}

/**
 * 保存项目代码
 * @param {number} id - 项目ID
 * @param {Object} data - 代码数据
 * @param {string} data.code - 项目代码内容
 * @returns {Promise}
 */
export function saveProjectCode(id, data) {
  return request({
    url: `/admin/project/my/${id}/code`,
    method: 'put',
    data
  })
}

/**
 * 预览项目
 * @param {number} projectId - 项目ID
 * @returns {Promise}
 */
export function previewProject(projectId) {
  return request({
    url: '/admin/project/my/preview',
    method: 'get',
    params: {
      project_id: projectId
    }
  })
}

/**
 * 切换项目状态
 * @param {number} id - 项目ID
 * @returns {Promise}
 */
export function toggleProjectState(id) {
  return request({
    url: `/admin/project/my/${id}/toggle`,
    method: 'put'
  })
}

/**
 * 获取项目统计数据
 * @returns {Promise}
 */
export function getProjectStats() {
  return request({
    url: '/admin/project/my/stats',
    method: 'get'
  })
}

/**
 * 恢复已删除的项目
 * @param {number} id - 项目ID
 * @returns {Promise}
 */
export function restoreProject(id) {
  return request({
    url: `/admin/project/restore/${id}`,
    method: 'put'
  })
}