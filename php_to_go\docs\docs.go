// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/article/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有公告列表，支持分页",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "公告管理"
                ],
                "summary": "获取公告列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态过滤，1-启用，0-禁用",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.ArticleListItem"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的公告",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "公告管理"
                ],
                "summary": "创建公告",
                "parameters": [
                    {
                        "description": "公告信息",
                        "name": "article",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ArticleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ArticleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/article/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取公告详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "公告管理"
                ],
                "summary": "获取公告详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "公告ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ArticleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有公告信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "公告管理"
                ],
                "summary": "更新公告",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "公告ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "公告更新信息",
                        "name": "article",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ArticleUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ArticleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID删除公告",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "公告管理"
                ],
                "summary": "删除公告",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "公告ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/article/publish/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "将公告状态设置为已发布并通过队列发送通知",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "公告管理"
                ],
                "summary": "发布公告",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "公告ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "延迟发布时间(分钟)",
                        "name": "delay_minutes",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ArticleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/auth/captcha": {
            "get": {
                "description": "生成一个新的图片验证码，返回验证码密钥和Base64编码的图片",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "获取验证码",
                "responses": {
                    "200": {
                        "description": "成功响应",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.CaptchaResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "生成验证码失败",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/auth/forgot": {
            "post": {
                "description": "处理用户忘记密码请求，向用户邮箱发送重置密码链接",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "忘记密码",
                "parameters": [
                    {
                        "description": "忘记密码请求信息",
                        "name": "forgotPassword",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AuthForgotPasswordDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功发送密码重置邮件",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误或验证码错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "用户名或邮箱不匹配",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误或邮件发送失败",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/auth/login": {
            "post": {
                "description": "使用用户名、密码和验证码登录并获取JWT令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "管理员登录",
                "parameters": [
                    {
                        "description": "登录凭证 (包含验证码)",
                        "name": "login",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AuthLoginDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功，返回JWT令牌",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.AuthResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误或验证码错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "401": {
                        "description": "用户名或密码错误或账户被禁用",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误或令牌生成失败",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/auth/reset": {
            "post": {
                "description": "使用重置令牌重置用户密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "重置密码",
                "parameters": [
                    {
                        "description": "重置密码请求信息",
                        "name": "resetPassword",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AuthResetPasswordDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码重置成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误或令牌无效",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/auth/signup": {
            "post": {
                "description": "创建一个新的管理员账户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "管理员注册",
                "parameters": [
                    {
                        "description": "注册信息 (referee根据配置决定是否必需)",
                        "name": "signup",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AuthSignupDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "注册成功，返回用户ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "properties": {
                                                "id": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误、验证码错误、邀请码错误或用户名/邮箱已存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/auth/verify": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "验证JWT令牌是否有效",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "验证Token",
                "responses": {
                    "200": {
                        "description": "验证结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "properties": {
                                                "valid": {
                                                    "type": "boolean"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "令牌无效",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/dashboard": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统概览数据，包括用户数、角色数、权限数等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "控制面板"
                ],
                "summary": "获取控制面板数据",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.DashboardDataResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/dashboard/logs": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统中的最近操作日志",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "控制面板"
                ],
                "summary": "获取最近操作日志",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "限制数量，默认为10",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.OperationLogResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/dashboard/system": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统运行信息，包括服务器状态、数据库状态等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "控制面板"
                ],
                "summary": "获取系统信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.SystemInfoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/domain/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取域名列表，支持分页和筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "域名管理"
                ],
                "summary": "获取域名列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "域名(模糊搜索)",
                        "name": "domain",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "类型：10 过滤域名/20 切换使用的域名",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态",
                        "name": "state",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.DomainListItem"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的域名",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "域名管理"
                ],
                "summary": "创建域名",
                "parameters": [
                    {
                        "description": "域名信息",
                        "name": "domain",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.DomainRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.DomainResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/domain/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取域名详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "域名管理"
                ],
                "summary": "获取域名详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "域名ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.DomainResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "域名不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有域名",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "域名管理"
                ],
                "summary": "更新域名",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "域名ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "域名更新信息",
                        "name": "domain",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.DomainUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.DomainResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "域名不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除现有域名",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "域名管理"
                ],
                "summary": "删除域名",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "域名ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "域名不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/failed-job/clear-all": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "清空所有失败任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "清空所有失败任务",
                "responses": {
                    "200": {
                        "description": "清空成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/failed-job/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取失败任务列表，支持分页和筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "获取失败任务列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "队列名称(模糊搜索)",
                        "name": "queue",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "连接名称(模糊搜索)",
                        "name": "connection",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "开始时间(格式: 2006-01-02 15:04:05)",
                        "name": "start_time",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "结束时间(格式: 2006-01-02 15:04:05)",
                        "name": "end_time",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码(默认: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量(默认: 10)",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.FailedJobListItem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的失败任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "创建失败任务",
                "parameters": [
                    {
                        "description": "失败任务信息",
                        "name": "failedJob",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.FailedJobCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.FailedJobResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/failed-job/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取失败任务详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "获取失败任务详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "失败任务ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.FailedJobResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "失败任务不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有失败任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "更新失败任务",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "失败任务ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "失败任务更新信息",
                        "name": "failedJob",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.FailedJobUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.FailedJobResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "失败任务不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除现有失败任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "删除失败任务",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "失败任务ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "失败任务不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/failed-job/retry/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "重试失败的任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "重试失败任务",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "失败任务ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "重试成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "失败任务不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/friendly-list": {
            "get": {
                "description": "获取公开可用的友情链接列表，无需认证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "公开接口"
                ],
                "summary": "获取公开友情链接列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为20",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.FriendlyListItem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/friendly/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取友情链接列表，支持分页和筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "友情链接管理"
                ],
                "summary": "获取友情链接列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "名称(模糊搜索)",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "类型",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态",
                        "name": "state",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.FriendlyListItem"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的友情链接",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "友情链接管理"
                ],
                "summary": "创建友情链接",
                "parameters": [
                    {
                        "description": "友情链接信息",
                        "name": "friendly",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.FriendlyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.FriendlyResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/friendly/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取友情链接详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "友情链接管理"
                ],
                "summary": "获取友情链接详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "友情链接ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.FriendlyResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "友情链接不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有友情链接",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "友情链接管理"
                ],
                "summary": "更新友情链接",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "友情链接ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "友情链接更新信息",
                        "name": "friendly",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.FriendlyUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.FriendlyResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "友情链接不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除现有友情链接",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "友情链接管理"
                ],
                "summary": "删除友情链接",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "友情链接ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "友情链接不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/invitation/batch": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "批量启用/禁用/删除邀请码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "邀请码管理"
                ],
                "summary": "批量操作邀请码",
                "parameters": [
                    {
                        "description": "批量操作请求",
                        "name": "batch",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.InvitationBatchRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/invitation/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取邀请码列表，支持分页和筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "邀请码管理"
                ],
                "summary": "获取邀请码列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "邀请码(精确匹配)",
                        "name": "code",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态",
                        "name": "state",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.InvitationListItem"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的邀请码，支持批量生成",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "邀请码管理"
                ],
                "summary": "创建邀请码",
                "parameters": [
                    {
                        "description": "邀请码信息",
                        "name": "invitation",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.InvitationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.InvitationResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/invitation/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取邀请码详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "邀请码管理"
                ],
                "summary": "获取邀请码详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "邀请码ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.InvitationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "邀请码不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有邀请码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "邀请码管理"
                ],
                "summary": "更新邀请码",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "邀请码ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "邀请码更新信息",
                        "name": "invitation",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.InvitationUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.InvitationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "邀请码不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除现有邀请码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "邀请码管理"
                ],
                "summary": "删除邀请码",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "邀请码ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "邀请码不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/job/clear-all": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "清空所有任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "清空所有任务",
                "responses": {
                    "200": {
                        "description": "清空成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/job/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取任务列表，支持分页和筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "获取任务列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "队列名称(模糊搜索)",
                        "name": "queue",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "尝试次数",
                        "name": "attempts",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "是否已保留",
                        "name": "is_reserved",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "页码(默认: 1)",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量(默认: 10)",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.JobListItem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "创建任务",
                "parameters": [
                    {
                        "description": "任务信息",
                        "name": "job",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.JobCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.JobResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/job/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取任务详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "获取任务详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "任务ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.JobResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "任务不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "更新任务",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "任务ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "任务更新信息",
                        "name": "job",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.JobUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.JobResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "任务不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除现有任务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "任务管理"
                ],
                "summary": "删除任务",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "任务ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "任务不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/menu/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统中的所有菜单",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "菜单管理"
                ],
                "summary": "获取菜单列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "菜单标题(模糊搜索)",
                        "name": "title",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.MenuResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建一个新的菜单",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "菜单管理"
                ],
                "summary": "创建菜单",
                "parameters": [
                    {
                        "description": "菜单信息",
                        "name": "menu",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.MenuCreateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.MenuResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/menu/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定ID的菜单详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "菜单管理"
                ],
                "summary": "获取菜单详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "菜单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.MenuDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "菜单不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新已存在的菜单信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "菜单管理"
                ],
                "summary": "更新菜单",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "菜单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "菜单更新信息",
                        "name": "menu",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.MenuUpdateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.MenuResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "菜单不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除指定的菜单",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "菜单管理"
                ],
                "summary": "删除菜单",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "菜单ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "菜单不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/menu/tree": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户有权限访问的菜单树形结构",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "菜单管理"
                ],
                "summary": "获取用户菜单树",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.MenuResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/module/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取模块列表，支持分页和筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模块管理"
                ],
                "summary": "获取模块列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模块名称(模糊搜索)",
                        "name": "title",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "安全等级",
                        "name": "level",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态",
                        "name": "state",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.ModuleListItem"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的模块",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模块管理"
                ],
                "summary": "创建模块",
                "parameters": [
                    {
                        "description": "模块信息",
                        "name": "module",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ModuleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ModuleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/module/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取模块详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模块管理"
                ],
                "summary": "获取模块详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "模块ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ModuleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "模块不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有模块",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模块管理"
                ],
                "summary": "更新模块",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "模块ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "模块更新信息",
                        "name": "module",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ModuleUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ModuleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "模块不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除现有模块",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模块管理"
                ],
                "summary": "删除模块",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "模块ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "模块不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/permissions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统中的所有权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "获取权限列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "权限名称(模糊搜索)",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "父权限ID",
                        "name": "parent_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.PermissionResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建一个新的权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "创建权限",
                "parameters": [
                    {
                        "description": "权限信息",
                        "name": "permission",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.PermissionCreateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.PermissionResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/permissions/batch-delete": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "批量删除指定ID的权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "批量删除权限",
                "parameters": [
                    {
                        "description": "权限ID列表",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/permissions/check/{slug}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "检查当前用户是否有特定的权限标识",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "检查用户权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "权限标识",
                        "name": "slug",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "boolean"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/permissions/tree": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统中的所有权限，以树形结构返回",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "获取权限树",
                "parameters": [
                    {
                        "type": "string",
                        "description": "排除的权限ID，多个ID用逗号分隔",
                        "name": "exclude_ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.PermissionResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/permissions/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定ID的权限详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "获取权限详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "权限ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.PermissionDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "权限不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新已存在的权限信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "更新权限",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "权限ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "权限更新信息",
                        "name": "permission",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.PermissionUpdateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.PermissionResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "权限不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除指定的权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "删除权限",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "权限ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "权限不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/project/my": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的项目列表，支持分页",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目管理"
                ],
                "summary": "获取项目列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态过滤，1-启用，0-禁用",
                        "name": "state",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.ProjectListItem"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的项目",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目管理"
                ],
                "summary": "创建项目",
                "parameters": [
                    {
                        "description": "项目信息",
                        "name": "project",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ProjectRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProjectResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/project/my/preview": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "预览项目内容",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目管理"
                ],
                "summary": "预览项目",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "project_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/project/my/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取项目详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目管理"
                ],
                "summary": "获取项目详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProjectResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有项目信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目管理"
                ],
                "summary": "更新项目",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "项目更新信息",
                        "name": "project",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ProjectUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProjectResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID删除项目",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目管理"
                ],
                "summary": "删除项目",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/project/my/{id}/toggle": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "切换项目的启用/禁用状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目管理"
                ],
                "summary": "切换项目状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProjectResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/project/my/{userid}/{projectid}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据用户ID和项目ID获取项目内容详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目内容管理"
                ],
                "summary": "获取项目内容详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "userid",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "projectid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProjectContentResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据用户ID和项目ID删除项目内容",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目内容管理"
                ],
                "summary": "删除项目内容",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "userid",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "projectid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/project/viewcode/{projectid}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据项目ID查看代码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目管理"
                ],
                "summary": "查看项目代码",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目ID",
                        "name": "projectid",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/projectscontent/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取项目内容列表，支持分页",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目内容管理"
                ],
                "summary": "获取项目内容列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "项目ID过滤",
                        "name": "project_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.ProjectContentListItem"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的项目内容",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目内容管理"
                ],
                "summary": "创建项目内容",
                "parameters": [
                    {
                        "description": "项目内容信息",
                        "name": "content",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ProjectContentRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProjectContentResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/projectscontent/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取项目内容详情",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目内容管理"
                ],
                "summary": "获取项目内容详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "项目内容ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProjectContentResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有项目内容",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目内容管理"
                ],
                "summary": "更新项目内容",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "内容ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "项目内容更新信息",
                        "name": "content",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ProjectContentUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProjectContentResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID删除项目内容",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "项目内容管理"
                ],
                "summary": "删除项目内容",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "内容ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/all": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统中的所有角色，不分页",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取所有角色",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/dto.RoleResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/admin/role/batch-delete": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "批量删除指定ID的角色(管理员角色除外)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "批量删除角色",
                "parameters": [
                    {
                        "description": "角色ID列表",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/index": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取角色列表，支持分页和筛选",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取角色列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "角色名称(模糊搜索)",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Guard名称",
                        "name": "guard_name",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.RoleResponse"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "创建角色",
                "parameters": [
                    {
                        "description": "角色信息",
                        "name": "role",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.RoleCreateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.RoleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/index/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取角色详情，包含权限树形结构",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取角色详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.RoleDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新现有角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "更新角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "角色更新信息",
                        "name": "role",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.RoleUpdateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.RoleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除现有角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "删除角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/{id}/menus": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "批量分配菜单给指定角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "分配菜单给角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "菜单ID列表",
                        "name": "menu_ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分配成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/{id}/permissions": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "批量分配权限给指定角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "分配权限给角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "权限ID列表",
                        "name": "permission_ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分配成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/{id}/users": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取拥有指定角色的用户列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "获取角色用户列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "页码，默认为1",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页条数，默认为10",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.UserResponse"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "将指定角色分配给多个用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "分配角色给用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户ID列表",
                        "name": "user_ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分配成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/role/{id}/users/remove": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "从多个用户移除指定角色",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色管理"
                ],
                "summary": "从用户移除角色",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "角色ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "用户ID列表",
                        "name": "user_ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.BatchIDsDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "移除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "角色不存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/settings": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有网站设置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "网站设置"
                ],
                "summary": "获取网站设置",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.SettingsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新网站设置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "网站设置"
                ],
                "summary": "更新网站设置",
                "parameters": [
                    {
                        "description": "网站设置信息",
                        "name": "settings",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SettingsUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.SettingsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/users": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取管理员用户列表，支持分页和按用户名、姓名、邮箱、状态过滤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "获取管理员用户列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名 (模糊查询)",
                        "name": "username",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "姓名 (模糊查询)",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "邮箱 (模糊查询)",
                        "name": "email",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "状态 (1:启用, 其他或不传:所有)",
                        "name": "state",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功响应",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "allOf": [
                                                {
                                                    "$ref": "#/definitions/dto.PaginatedResponse"
                                                },
                                                {
                                                    "type": "object",
                                                    "properties": {
                                                        "items": {
                                                            "type": "array",
                                                            "items": {
                                                                "$ref": "#/definitions/dto.AdminUserResponse"
                                                            }
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效的查询参数",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建一个新的管理员用户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "创建管理员用户",
                "parameters": [
                    {
                        "description": "创建用户请求体",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AdminUserCreateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功，返回用户ID",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "properties": {
                                                "id": {
                                                    "type": "integer"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效的请求数据或用户名已存在",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/admin/users/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取管理员用户的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "获取单个管理员用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功响应",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.AdminUserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "用户未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID更新管理员用户的信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "更新管理员用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新用户请求体 (只需提供要更新的字段)",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AdminUserUpdateDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "无效的用户ID、请求数据或用户名已被使用",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "用户未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID删除管理员用户 (软删除)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AdminUsers"
                ],
                "summary": "删除管理员用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "用户未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/auth/profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户的详细信息包括权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证管理"
                ],
                "summary": "获取用户信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProfileResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新当前登录用户的个人信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证管理"
                ],
                "summary": "更新用户信息",
                "parameters": [
                    {
                        "description": "用户信息",
                        "name": "profile",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UpdateProfileDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/dto.ProfileResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/download/{hash}": {
            "get": {
                "description": "根据哈希值下载文件",
                "produces": [
                    "application/octet-stream",
                    "text/plain"
                ],
                "tags": [
                    "Gateway"
                ],
                "summary": "下载文件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "文件哈希值",
                        "name": "hash",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "文件下载成功",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "404": {
                        "description": "文件未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/emails/scheduled": {
            "post": {
                "description": "将邮件请求加入队列，支持延迟发送",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Email"
                ],
                "summary": "发送定时邮件",
                "parameters": [
                    {
                        "description": "定时邮件请求体",
                        "name": "email",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.ScheduledEmailRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "邮件已加入发送队列",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/emails/send": {
            "post": {
                "description": "立即将邮件请求加入发送队列",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Email"
                ],
                "summary": "发送邮件",
                "parameters": [
                    {
                        "description": "邮件请求体",
                        "name": "email",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.EmailRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "邮件已加入发送队列",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/keepsession": {
            "get": {
                "description": "保持客户端会话活跃状态",
                "produces": [
                    "text/plain"
                ],
                "tags": [
                    "Gateway"
                ],
                "summary": "保持会话",
                "responses": {
                    "200": {
                        "description": "会话保持成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/logs": {
            "get": {
                "description": "返回日志查看欢迎页面",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Gateway"
                ],
                "summary": "查看日志",
                "responses": {
                    "200": {
                        "description": "成功响应",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/queue/stats": {
            "get": {
                "description": "获取当前所有队列的统计数据（如任务数、延迟等）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Queue"
                ],
                "summary": "获取队列统计信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/submit": {
            "get": {
                "description": "接收并存储项目相关数据",
                "consumes": [
                    "multipart/form-data",
                    "application/json",
                    "application/x-www-form-urlencoded"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Gateway"
                ],
                "summary": "提交数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "项目唯一键",
                        "name": "unique_key",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "项目ID (当unique_key未提供时使用)",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Cookie信息",
                        "name": "cookie",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "位置信息",
                        "name": "location",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "顶级位置",
                        "name": "toplocation",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "读取URL",
                        "name": "duquurl",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "JSONP回调函数名",
                        "name": "callback",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "数据提交成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "properties": {
                                                "hashid": {
                                                    "type": "string"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "项目未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "接收并存储项目相关数据",
                "consumes": [
                    "multipart/form-data",
                    "application/json",
                    "application/x-www-form-urlencoded"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Gateway"
                ],
                "summary": "提交数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "项目唯一键",
                        "name": "unique_key",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "项目ID (当unique_key未提供时使用)",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Cookie信息",
                        "name": "cookie",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "位置信息",
                        "name": "location",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "顶级位置",
                        "name": "toplocation",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "读取URL",
                        "name": "duquurl",
                        "in": "formData"
                    },
                    {
                        "type": "string",
                        "description": "JSONP回调函数名",
                        "name": "callback",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "数据提交成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/dto.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "properties": {
                                                "hashid": {
                                                    "type": "string"
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "项目未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/{unique_key}": {
            "get": {
                "description": "根据唯一键获取项目内容",
                "produces": [
                    "text/html",
                    "text/plain"
                ],
                "tags": [
                    "Gateway"
                ],
                "summary": "获取唯一内容",
                "parameters": [
                    {
                        "type": "string",
                        "description": "项目唯一键",
                        "name": "unique_key",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "项目内容",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "403": {
                        "description": "禁止访问",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "404": {
                        "description": "内容未找到",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        },
        "/{unique_key}/{rand_key}.jpg": {
            "get": {
                "description": "根据唯一键和随机键获取图片",
                "produces": [
                    "image/jpeg"
                ],
                "tags": [
                    "Gateway"
                ],
                "summary": "获取唯一图片",
                "parameters": [
                    {
                        "type": "string",
                        "description": "项目唯一键",
                        "name": "unique_key",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "随机键",
                        "name": "rand_key",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "图片获取成功",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "图片未找到",
                        "schema": {
                            "$ref": "#/definitions/dto.StandardResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dto.AdminUserCreateDTO": {
            "type": "object",
            "required": [
                "name",
                "password",
                "username"
            ],
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "notify": {
                    "type": "integer"
                },
                "password": {
                    "type": "string",
                    "maxLength": 80,
                    "minLength": 6
                },
                "referee": {
                    "type": "string"
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                },
                "username": {
                    "type": "string",
                    "maxLength": 120,
                    "minLength": 3
                }
            }
        },
        "dto.AdminUserResponse": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "lasted_at": {
                    "type": "string"
                },
                "lasted_ipaddress": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "notify": {
                    "type": "integer"
                },
                "referee": {
                    "type": "string"
                },
                "state": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "dto.AdminUserUpdateDTO": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "notify": {
                    "type": "integer"
                },
                "password": {
                    "type": "string",
                    "maxLength": 80,
                    "minLength": 6
                },
                "referee": {
                    "type": "string"
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                },
                "username": {
                    "type": "string",
                    "maxLength": 120,
                    "minLength": 3
                }
            }
        },
        "dto.ArticleListItem": {
            "type": "object",
            "properties": {
                "author": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "dto.ArticleRequest": {
            "type": "object",
            "required": [
                "description",
                "title"
            ],
            "properties": {
                "author": {
                    "type": "string",
                    "maxLength": 100
                },
                "description": {
                    "type": "string"
                },
                "status": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                },
                "title": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 2
                }
            }
        },
        "dto.ArticleResponse": {
            "type": "object",
            "properties": {
                "author": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "created_by": {
                    "type": "integer"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "updated_by": {
                    "type": "integer"
                }
            }
        },
        "dto.ArticleUpdateRequest": {
            "type": "object",
            "properties": {
                "author": {
                    "type": "string",
                    "maxLength": 100
                },
                "description": {
                    "type": "string"
                },
                "status": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                },
                "title": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 2
                }
            }
        },
        "dto.AuthForgotPasswordDTO": {
            "type": "object",
            "required": [
                "captcha",
                "captcha_key",
                "email",
                "username"
            ],
            "properties": {
                "captcha": {
                    "type": "string"
                },
                "captcha_key": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "dto.AuthLoginDTO": {
            "type": "object",
            "required": [
                "captcha",
                "captcha_key",
                "password",
                "username"
            ],
            "properties": {
                "captcha": {
                    "type": "string"
                },
                "captcha_key": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "dto.AuthResetPasswordDTO": {
            "type": "object",
            "required": [
                "new_password",
                "token"
            ],
            "properties": {
                "new_password": {
                    "type": "string",
                    "maxLength": 80,
                    "minLength": 6
                },
                "token": {
                    "type": "string"
                }
            }
        },
        "dto.AuthResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string"
                }
            }
        },
        "dto.AuthSignupDTO": {
            "type": "object",
            "required": [
                "captcha",
                "captcha_key",
                "name",
                "password",
                "username"
            ],
            "properties": {
                "captcha": {
                    "type": "string"
                },
                "captcha_key": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "maxLength": 80,
                    "minLength": 6
                },
                "referee": {
                    "description": "Made optional for validation, logic handled in handler",
                    "type": "string"
                },
                "username": {
                    "type": "string",
                    "maxLength": 120,
                    "minLength": 3
                }
            }
        },
        "dto.BatchIDsDTO": {
            "type": "object",
            "required": [
                "ids"
            ],
            "properties": {
                "ids": {
                    "description": "ID列表",
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "dto.CaptchaResponse": {
            "type": "object",
            "properties": {
                "captcha_key": {
                    "description": "验证码唯一标识",
                    "type": "string"
                },
                "image_data": {
                    "description": "Base64编码的图片数据 (e.g., data:image/png;base64,...)",
                    "type": "string"
                }
            }
        },
        "dto.DashboardDataResponse": {
            "type": "object",
            "properties": {
                "menu_count": {
                    "description": "菜单总数",
                    "type": "integer"
                },
                "permission_count": {
                    "description": "权限总数",
                    "type": "integer"
                },
                "project_count": {
                    "description": "项目总数",
                    "type": "integer"
                },
                "role_count": {
                    "description": "角色总数",
                    "type": "integer"
                },
                "today_project_count": {
                    "description": "今日新增项目数",
                    "type": "integer"
                },
                "today_user_count": {
                    "description": "今日新增用户数",
                    "type": "integer"
                },
                "user_count": {
                    "description": "用户总数",
                    "type": "integer"
                }
            }
        },
        "dto.DomainListItem": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "domain": {
                    "description": "域名",
                    "type": "string"
                },
                "id": {
                    "description": "域名ID",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "type": {
                    "description": "类型 10 过滤域名/20 切换使用的域名",
                    "type": "integer"
                }
            }
        },
        "dto.DomainRequest": {
            "type": "object",
            "required": [
                "domain",
                "state",
                "type"
            ],
            "properties": {
                "domain": {
                    "description": "域名",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "type": {
                    "description": "类型 10 过滤域名/20 切换使用的域名",
                    "type": "integer",
                    "enum": [
                        10,
                        20
                    ]
                }
            }
        },
        "dto.DomainResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "domain": {
                    "description": "域名",
                    "type": "string"
                },
                "id": {
                    "description": "域名ID",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "type": {
                    "description": "类型 10 过滤域名/20 切换使用的域名",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "dto.DomainUpdateRequest": {
            "type": "object",
            "properties": {
                "domain": {
                    "description": "域名",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "type": {
                    "description": "类型 10 过滤域名/20 切换使用的域名",
                    "type": "integer",
                    "enum": [
                        10,
                        20
                    ]
                }
            }
        },
        "dto.EmailRequest": {
            "type": "object",
            "required": [
                "body",
                "subject",
                "to"
            ],
            "properties": {
                "body": {
                    "type": "string"
                },
                "is_html": {
                    "type": "boolean"
                },
                "subject": {
                    "type": "string"
                },
                "to": {
                    "type": "string"
                }
            }
        },
        "dto.FailedJobCreateRequest": {
            "type": "object",
            "required": [
                "connection",
                "exception",
                "payload",
                "queue"
            ],
            "properties": {
                "connection": {
                    "type": "string"
                },
                "exception": {
                    "type": "string"
                },
                "payload": {
                    "type": "string"
                },
                "queue": {
                    "type": "string"
                }
            }
        },
        "dto.FailedJobListItem": {
            "type": "object",
            "properties": {
                "connection": {
                    "type": "string"
                },
                "exception": {
                    "type": "string"
                },
                "failed_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "queue": {
                    "type": "string"
                }
            }
        },
        "dto.FailedJobResponse": {
            "type": "object",
            "properties": {
                "connection": {
                    "type": "string"
                },
                "exception": {
                    "type": "string"
                },
                "failed_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "payload": {
                    "type": "string"
                },
                "queue": {
                    "type": "string"
                }
            }
        },
        "dto.FailedJobUpdateRequest": {
            "type": "object",
            "properties": {
                "connection": {
                    "type": "string"
                },
                "exception": {
                    "type": "string"
                },
                "payload": {
                    "type": "string"
                },
                "queue": {
                    "type": "string"
                }
            }
        },
        "dto.FriendlyListItem": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "href": {
                    "description": "链接地址",
                    "type": "string"
                },
                "id": {
                    "description": "友情链接ID",
                    "type": "integer"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "thumb": {
                    "description": "缩略图",
                    "type": "string"
                },
                "type": {
                    "description": "类型",
                    "type": "string"
                }
            }
        },
        "dto.FriendlyRequest": {
            "type": "object",
            "required": [
                "href",
                "name",
                "type"
            ],
            "properties": {
                "href": {
                    "description": "链接地址",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "thumb": {
                    "description": "缩略图",
                    "type": "string"
                },
                "type": {
                    "description": "类型",
                    "type": "string"
                }
            }
        },
        "dto.FriendlyResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "href": {
                    "description": "链接地址",
                    "type": "string"
                },
                "id": {
                    "description": "友情链接ID",
                    "type": "integer"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "thumb": {
                    "description": "缩略图",
                    "type": "string"
                },
                "type": {
                    "description": "类型",
                    "type": "string"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                }
            }
        },
        "dto.FriendlyUpdateRequest": {
            "type": "object",
            "properties": {
                "href": {
                    "description": "链接地址",
                    "type": "string"
                },
                "name": {
                    "description": "名称",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "thumb": {
                    "description": "缩略图",
                    "type": "string"
                },
                "type": {
                    "description": "类型",
                    "type": "string"
                }
            }
        },
        "dto.InvitationBatchRequest": {
            "type": "object",
            "required": [
                "action",
                "ids"
            ],
            "properties": {
                "action": {
                    "description": "操作类型",
                    "type": "string",
                    "enum": [
                        "enable",
                        "disable",
                        "delete"
                    ]
                },
                "ids": {
                    "description": "邀请码ID列表",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "dto.InvitationListItem": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "邀请码",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "id": {
                    "description": "邀请码ID",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "used_at": {
                    "description": "使用时间",
                    "type": "string"
                },
                "user_id": {
                    "description": "创建用户ID",
                    "type": "integer"
                }
            }
        },
        "dto.InvitationRequest": {
            "type": "object",
            "required": [
                "code"
            ],
            "properties": {
                "code": {
                    "description": "邀请码",
                    "type": "string"
                },
                "count": {
                    "description": "批量生成数量",
                    "type": "integer",
                    "maximum": 100,
                    "minimum": 1
                },
                "state": {
                    "description": "状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                }
            }
        },
        "dto.InvitationResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "邀请码",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "id": {
                    "description": "邀请码ID",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "used_at": {
                    "description": "使用时间",
                    "type": "string"
                },
                "user_id": {
                    "description": "创建用户ID",
                    "type": "integer"
                }
            }
        },
        "dto.InvitationUpdateRequest": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "邀请码",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                }
            }
        },
        "dto.JobCreateRequest": {
            "type": "object",
            "required": [
                "attempts",
                "available_at",
                "payload",
                "queue"
            ],
            "properties": {
                "attempts": {
                    "type": "integer"
                },
                "available_at": {
                    "type": "integer"
                },
                "payload": {
                    "type": "string"
                },
                "queue": {
                    "type": "string"
                }
            }
        },
        "dto.JobListItem": {
            "type": "object",
            "properties": {
                "attempts": {
                    "type": "integer"
                },
                "available_at": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "queue": {
                    "type": "string"
                },
                "reserved_at": {
                    "type": "integer"
                }
            }
        },
        "dto.JobResponse": {
            "type": "object",
            "properties": {
                "attempts": {
                    "type": "integer"
                },
                "available_at": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "payload": {
                    "type": "string"
                },
                "queue": {
                    "type": "string"
                },
                "reserved_at": {
                    "type": "integer"
                }
            }
        },
        "dto.JobUpdateRequest": {
            "type": "object",
            "properties": {
                "attempts": {
                    "type": "integer"
                },
                "available_at": {
                    "type": "integer"
                },
                "payload": {
                    "type": "string"
                },
                "queue": {
                    "type": "string"
                },
                "reserved_at": {
                    "type": "integer"
                }
            }
        },
        "dto.MenuCreateDTO": {
            "type": "object",
            "required": [
                "title"
            ],
            "properties": {
                "icon": {
                    "type": "string"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "permission_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "title": {
                    "type": "string"
                },
                "uri": {
                    "type": "string"
                }
            }
        },
        "dto.MenuDetailResponse": {
            "type": "object",
            "properties": {
                "icon": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "title": {
                    "type": "string"
                },
                "uri": {
                    "type": "string"
                }
            }
        },
        "dto.MenuResponse": {
            "type": "object",
            "properties": {
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dto.MenuResponse"
                    }
                },
                "icon": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "uri": {
                    "type": "string"
                }
            }
        },
        "dto.MenuUpdateDTO": {
            "type": "object",
            "required": [
                "title"
            ],
            "properties": {
                "icon": {
                    "type": "string"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "title": {
                    "type": "string"
                },
                "uri": {
                    "type": "string"
                }
            }
        },
        "dto.ModuleListItem": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "description": {
                    "description": "模块描述",
                    "type": "string"
                },
                "id": {
                    "description": "模块ID",
                    "type": "integer"
                },
                "is_share": {
                    "description": "共享状态",
                    "type": "integer"
                },
                "level": {
                    "description": "安全等级",
                    "type": "integer"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "title": {
                    "description": "模块名称",
                    "type": "string"
                }
            }
        },
        "dto.ModuleRequest": {
            "type": "object",
            "required": [
                "code",
                "title"
            ],
            "properties": {
                "code": {
                    "description": "HTML代码",
                    "type": "string"
                },
                "description": {
                    "description": "模块描述",
                    "type": "string"
                },
                "is_share": {
                    "description": "共享状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "keys": {
                    "description": "关键字",
                    "type": "string"
                },
                "level": {
                    "description": "安全等级",
                    "type": "integer",
                    "maximum": 9,
                    "minimum": 0
                },
                "setkeys": {
                    "description": "配置参数",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "title": {
                    "description": "模块名称",
                    "type": "string"
                }
            }
        },
        "dto.ModuleResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "HTML代码",
                    "type": "string"
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "description": {
                    "description": "模块描述",
                    "type": "string"
                },
                "id": {
                    "description": "模块ID",
                    "type": "integer"
                },
                "is_share": {
                    "description": "共享状态",
                    "type": "integer"
                },
                "keys": {
                    "description": "关键字",
                    "type": "string"
                },
                "level": {
                    "description": "安全等级",
                    "type": "integer"
                },
                "setkeys": {
                    "description": "配置参数",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer"
                },
                "title": {
                    "description": "模块名称",
                    "type": "string"
                },
                "updated_at": {
                    "description": "更新时间",
                    "type": "string"
                },
                "user_id": {
                    "description": "创建人ID",
                    "type": "integer"
                }
            }
        },
        "dto.ModuleUpdateRequest": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "HTML代码",
                    "type": "string"
                },
                "description": {
                    "description": "模块描述",
                    "type": "string"
                },
                "is_share": {
                    "description": "共享状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "keys": {
                    "description": "关键字",
                    "type": "string"
                },
                "level": {
                    "description": "安全等级",
                    "type": "integer",
                    "maximum": 9,
                    "minimum": 0
                },
                "setkeys": {
                    "description": "配置参数",
                    "type": "string"
                },
                "state": {
                    "description": "状态",
                    "type": "integer",
                    "maximum": 1,
                    "minimum": 0
                },
                "title": {
                    "description": "模块名称",
                    "type": "string"
                }
            }
        },
        "dto.OperationLogResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "description": "创建时间",
                    "type": "string"
                },
                "id": {
                    "description": "日志ID",
                    "type": "integer"
                },
                "input": {
                    "description": "输入数据",
                    "type": "string"
                },
                "ip": {
                    "description": "IP地址",
                    "type": "string"
                },
                "method": {
                    "description": "HTTP方法",
                    "type": "string"
                },
                "path": {
                    "description": "操作路径",
                    "type": "string"
                },
                "user_id": {
                    "description": "用户ID",
                    "type": "integer"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "dto.PaginatedResponse": {
            "type": "object",
            "properties": {
                "items": {
                    "description": "当前页数据项"
                },
                "page": {
                    "description": "当前页码",
                    "type": "integer"
                },
                "page_size": {
                    "description": "每页大小",
                    "type": "integer"
                },
                "total": {
                    "description": "总记录数",
                    "type": "integer"
                },
                "total_pages": {
                    "description": "总页数",
                    "type": "integer"
                }
            }
        },
        "dto.PermissionCreateDTO": {
            "type": "object",
            "required": [
                "name",
                "slug"
            ],
            "properties": {
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string",
                    "maxLength": 50
                },
                "order": {
                    "type": "integer",
                    "default": 0
                },
                "parent_id": {
                    "type": "integer",
                    "default": 0
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "slug": {
                    "type": "string",
                    "maxLength": 50
                }
            }
        },
        "dto.PermissionDetailResponse": {
            "type": "object",
            "properties": {
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "slug": {
                    "type": "string"
                }
            }
        },
        "dto.PermissionResponse": {
            "type": "object",
            "properties": {
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dto.PermissionResponse"
                    }
                },
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "slug": {
                    "type": "string"
                }
            }
        },
        "dto.PermissionUpdateDTO": {
            "type": "object",
            "required": [
                "name",
                "slug"
            ],
            "properties": {
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string",
                    "maxLength": 50
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "role_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "slug": {
                    "type": "string",
                    "maxLength": 50
                }
            }
        },
        "dto.ProfileResponse": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "permissions": {
                    "description": "用户权限列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "role_slugs": {
                    "description": "用户角色标识列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dto.RoleItem"
                    }
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "dto.ProjectContentListItem": {
            "type": "object",
            "properties": {
                "cid": {
                    "type": "integer"
                },
                "content": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "domain": {
                    "type": "string"
                },
                "hash": {
                    "type": "string"
                },
                "project_id": {
                    "type": "integer"
                },
                "screenshot": {
                    "description": "屏幕截图URL路径",
                    "type": "string"
                },
                "server": {
                    "type": "string"
                },
                "source_file": {
                    "description": "源码文件URL路径",
                    "type": "string"
                },
                "state": {
                    "type": "integer"
                }
            }
        },
        "dto.ProjectContentRequest": {
            "type": "object",
            "required": [
                "content",
                "domain",
                "hash",
                "project_id",
                "server",
                "state"
            ],
            "properties": {
                "content": {
                    "type": "string"
                },
                "domain": {
                    "type": "string",
                    "maxLength": 255
                },
                "hash": {
                    "type": "string",
                    "maxLength": 32
                },
                "project_id": {
                    "type": "integer"
                },
                "screenshot": {
                    "type": "string",
                    "maxLength": 500
                },
                "server": {
                    "type": "string"
                },
                "source_file": {
                    "type": "string"
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "dto.ProjectContentResponse": {
            "type": "object",
            "properties": {
                "cid": {
                    "type": "integer"
                },
                "content": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "domain": {
                    "type": "string"
                },
                "hash": {
                    "type": "string"
                },
                "project_id": {
                    "type": "integer"
                },
                "screenshot": {
                    "description": "屏幕截图URL路径",
                    "type": "string"
                },
                "server": {
                    "type": "string"
                },
                "source_file": {
                    "description": "源码文件URL路径",
                    "type": "string"
                },
                "state": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "dto.ProjectContentUpdateRequest": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string"
                },
                "domain": {
                    "type": "string",
                    "maxLength": 255
                },
                "hash": {
                    "type": "string",
                    "maxLength": 32
                },
                "server": {
                    "type": "string"
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                }
            }
        },
        "dto.ProjectListItem": {
            "type": "object",
            "properties": {
                "content_count": {
                    "description": "项目内容记录数",
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "state": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "unique_key": {
                    "type": "string"
                }
            }
        },
        "dto.ProjectRequest": {
            "type": "object",
            "required": [
                "module_id",
                "state",
                "title",
                "unique_key"
            ],
            "properties": {
                "code": {
                    "type": "string"
                },
                "description": {
                    "type": "string",
                    "maxLength": 255
                },
                "module_ext_param": {
                    "type": "string"
                },
                "module_id": {
                    "type": "string"
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                },
                "title": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 2
                },
                "unique_key": {
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 2
                }
            }
        },
        "dto.ProjectResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_new_record": {
                    "type": "boolean"
                },
                "module_ext_param": {
                    "type": "string"
                },
                "module_id": {
                    "type": "string"
                },
                "state": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "unique_key": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                }
            }
        },
        "dto.ProjectUpdateRequest": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "description": {
                    "type": "string",
                    "maxLength": 255
                },
                "module_ext_param": {
                    "type": "string"
                },
                "module_id": {
                    "type": "string"
                },
                "state": {
                    "type": "integer",
                    "enum": [
                        0,
                        1
                    ]
                },
                "title": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 2
                }
            }
        },
        "dto.RoleCreateDTO": {
            "type": "object",
            "required": [
                "guard_name",
                "name"
            ],
            "properties": {
                "guard_name": {
                    "type": "string"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string"
                },
                "permission_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "remark": {
                    "type": "string"
                }
            }
        },
        "dto.RoleDetailResponse": {
            "type": "object",
            "properties": {
                "guard_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_admin": {
                    "description": "是否为超级管理员",
                    "type": "boolean"
                },
                "menus": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dto.RoleMenuResponse"
                    }
                },
                "name": {
                    "type": "string"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dto.RolePermissionResponse"
                    }
                },
                "remark": {
                    "type": "string"
                }
            }
        },
        "dto.RoleItem": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "slug": {
                    "type": "string"
                }
            }
        },
        "dto.RoleMenuResponse": {
            "type": "object",
            "properties": {
                "icon": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "uri": {
                    "type": "string"
                }
            }
        },
        "dto.RolePermissionResponse": {
            "type": "object",
            "properties": {
                "http_method": {
                    "type": "string"
                },
                "http_path": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "order": {
                    "type": "integer"
                },
                "parent_id": {
                    "type": "integer"
                },
                "slug": {
                    "type": "string"
                }
            }
        },
        "dto.RoleResponse": {
            "type": "object",
            "properties": {
                "guard_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "permission_count": {
                    "type": "integer"
                },
                "remark": {
                    "type": "string"
                }
            }
        },
        "dto.RoleUpdateDTO": {
            "type": "object",
            "properties": {
                "guard_name": {
                    "type": "string"
                },
                "menu_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "name": {
                    "type": "string"
                },
                "permission_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "remark": {
                    "type": "string"
                }
            }
        },
        "dto.ScheduledEmailRequest": {
            "type": "object",
            "required": [
                "body",
                "subject",
                "to"
            ],
            "properties": {
                "body": {
                    "type": "string"
                },
                "delay_seconds": {
                    "type": "integer",
                    "minimum": 0
                },
                "is_html": {
                    "type": "boolean"
                },
                "subject": {
                    "type": "string"
                },
                "to": {
                    "type": "string"
                }
            }
        },
        "dto.SettingsResponse": {
            "type": "object",
            "properties": {
                "closed_message": {
                    "description": "关闭提示信息",
                    "type": "string"
                },
                "mail_from_email": {
                    "description": "发件人邮箱",
                    "type": "string"
                },
                "mail_from_name": {
                    "description": "发件人名称",
                    "type": "string"
                },
                "mail_host": {
                    "description": "邮件服务器地址",
                    "type": "string"
                },
                "mail_password": {
                    "description": "邮件密码",
                    "type": "string"
                },
                "mail_port": {
                    "description": "邮件服务器端口",
                    "type": "integer"
                },
                "mail_username": {
                    "description": "邮件用户名",
                    "type": "string"
                },
                "register_closed": {
                    "description": "是否关闭注册",
                    "type": "boolean"
                },
                "site_closed": {
                    "description": "网站是否关闭",
                    "type": "boolean"
                },
                "site_description": {
                    "description": "网站描述",
                    "type": "string"
                },
                "site_favicon": {
                    "description": "网站Favicon",
                    "type": "string"
                },
                "site_footer": {
                    "description": "网站页脚",
                    "type": "string"
                },
                "site_keywords": {
                    "description": "网站关键词",
                    "type": "string"
                },
                "site_logo": {
                    "description": "网站Logo",
                    "type": "string"
                },
                "site_name": {
                    "description": "网站名称",
                    "type": "string"
                },
                "site_notice": {
                    "description": "网站公告",
                    "type": "string"
                }
            }
        },
        "dto.SettingsUpdateRequest": {
            "type": "object",
            "properties": {
                "closed_message": {
                    "description": "关闭提示信息",
                    "type": "string"
                },
                "mail_from_email": {
                    "description": "发件人邮箱",
                    "type": "string"
                },
                "mail_from_name": {
                    "description": "发件人名称",
                    "type": "string"
                },
                "mail_host": {
                    "description": "邮件服务器地址",
                    "type": "string"
                },
                "mail_password": {
                    "description": "邮件密码",
                    "type": "string"
                },
                "mail_port": {
                    "description": "邮件服务器端口",
                    "type": "integer"
                },
                "mail_username": {
                    "description": "邮件用户名",
                    "type": "string"
                },
                "register_closed": {
                    "description": "是否关闭注册",
                    "type": "boolean"
                },
                "site_closed": {
                    "description": "网站是否关闭",
                    "type": "boolean"
                },
                "site_description": {
                    "description": "网站描述",
                    "type": "string"
                },
                "site_favicon": {
                    "description": "网站Favicon",
                    "type": "string"
                },
                "site_footer": {
                    "description": "网站页脚",
                    "type": "string"
                },
                "site_keywords": {
                    "description": "网站关键词",
                    "type": "string"
                },
                "site_logo": {
                    "description": "网站Logo",
                    "type": "string"
                },
                "site_name": {
                    "description": "网站名称",
                    "type": "string"
                },
                "site_notice": {
                    "description": "网站公告",
                    "type": "string"
                }
            }
        },
        "dto.StandardResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "业务状态码，0表示成功",
                    "type": "integer"
                },
                "data": {
                    "description": "响应数据"
                },
                "error": {
                    "description": "错误信息，仅在开发环境显示",
                    "type": "string"
                },
                "message": {
                    "description": "响应消息",
                    "type": "string"
                }
            }
        },
        "dto.SystemInfoResponse": {
            "type": "object",
            "properties": {
                "database_version": {
                    "description": "数据库版本",
                    "type": "string"
                },
                "go_version": {
                    "description": "Go版本",
                    "type": "string"
                },
                "server_time": {
                    "description": "服务器时间",
                    "type": "string"
                },
                "uptime": {
                    "description": "运行时间",
                    "type": "string"
                },
                "version": {
                    "description": "系统版本",
                    "type": "string"
                }
            }
        },
        "dto.UpdateProfileDTO": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "email": {
                    "type": "string",
                    "maxLength": 100
                },
                "name": {
                    "type": "string",
                    "maxLength": 50
                }
            }
        },
        "dto.UserResponse": {
            "type": "object",
            "properties": {
                "avatar": {
                    "description": "头像",
                    "type": "string"
                },
                "email": {
                    "description": "邮箱",
                    "type": "string"
                },
                "id": {
                    "description": "用户ID",
                    "type": "integer"
                },
                "name": {
                    "description": "姓名",
                    "type": "string"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "请输入 'Bearer {token}' - 注意Bearer和token之间有一个空格",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:3000",
	BasePath:         "/api",
	Schemes:          []string{"http", "https"},
	Title:            "Go Fiber API",
	Description:      "这是使用Go Fiber构建的API服务.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
