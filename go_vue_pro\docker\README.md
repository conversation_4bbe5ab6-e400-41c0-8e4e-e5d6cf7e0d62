# Docker部署说明

本项目支持使用Docker进行部署，包含以下服务：

- 后端：API服务器 (Go Fiber)：go run cmd/server/main.go 队列Worker服务：go run cmd/worker/main.go
- MySQL数据库
- Redis缓存
- Nginx反向代理

## 前置条件

- 安装Docker和Docker Compose
- 确保80端口未被占用（如果需要修改端口，可以在docker-compose.yml中配置）
- 我们只暴露80端口，通过nginx来转发路由即可

## 配置说明

### 环境变量

在项目根目录创建`.env`文件，包含以下配置：

```env
# 应用配置
APP_NAME=Go Fiber API
APP_ENV=production
PORT=3000
APP_INVITATION=none

# 数据库配置
DB_HOST=mysql
DB_PORT=3306
DB_USER=user
DB_PASSWORD=password
DB_NAME=go_fiber

# Redis配置
REDIS_ADDR=redis:6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION_HOURS=72

# SMTP配置（如果需要邮件功能）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>
WEB_BASE_URL=https://yourdomain.com
```

## 部署步骤

1. 克隆项目到服务器

```bash
git clone <项目地址>
cd <项目目录>
```

2. 创建必要的目录和文件

```bash
mkdir -p docker/nginx
touch .env
```

3. 编辑`.env`文件，填入适合您环境的配置信息

4. 构建并启动容器

```bash
cd docker
docker-compose build --no-cache
docker-compose up -d
```

5. 查看容器运行状态

```bash
docker-compose ps
```

## 服务访问

- API服务: http://your-server-ip/api/
- Swagger文档: http://your-server-ip/swagger/
- 队列管理面板: http://your-server-ip/asynq/

## 日志查看

```bash
# 查看API服务日志
docker logs go-fiber-api

# 查看Worker服务日志
docker logs go-fiber-worker

# 查看Nginx访问日志
docker exec -it go-fiber-nginx cat /var/log/nginx/access.log

# 查看Nginx错误日志
docker exec -it go-fiber-nginx cat /var/log/nginx/error.log
```

## 常见问题

1. **数据库连接失败**
   - 检查`.env`文件中的数据库配置
   - 确保MySQL容器正常运行：`docker logs go-fiber-mysql`

2. **Redis连接失败**
   - 检查`.env`文件中的Redis配置
   - 确保Redis容器正常运行：`docker logs go-fiber-redis`

3. **API服务无法访问**
   - 检查Nginx配置和日志
   - 确保API容器正常运行：`docker logs go-fiber-api`

## 更新部署

当代码有更新时，可以按照以下步骤更新部署：

```bash
cd <项目目录>
git pull
cd docker
docker-compose down
docker-compose up -d --build
``` 