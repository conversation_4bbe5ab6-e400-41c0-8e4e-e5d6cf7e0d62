<template>
  <div class="friendly-list">
    <div class="page-header">
      <h1 class="page-title">友情链接管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <i class="el-icon-plus"></i> 添加友情链接
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="链接名称">
          <el-input v-model="filterForm.name" placeholder="请输入链接名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filterForm.state" placeholder="请选择状态" clearable>
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchFriendly">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 友情链接列表 -->
    <el-card shadow="hover" class="friendly-table-container">
      <div slot="header">
        <span>友情链接列表</span>
        <el-dropdown style="float: right; cursor: pointer;" @command="handleBatchCommand">
          <span class="el-dropdown-link">
            批量操作 <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="delete">批量删除</el-dropdown-item>
            <el-dropdown-item command="enable">批量启用</el-dropdown-item>
            <el-dropdown-item command="disable">批量禁用</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-table
        :data="friendlyList"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        v-loading="loading">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="链接名称" min-width="150"></el-table-column>
        <el-table-column prop="href" label="链接地址" min-width="200">
          <template slot-scope="scope">
            <a :href="scope.row.href" target="_blank">{{ scope.row.href }}</a>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template slot-scope="scope">
            <el-tag>{{ formatType(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="缩略图" width="100">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row.thumb"
              :src="scope.row.thumb"
              style="width: 40px; height: 40px"
              fit="contain"
              :preview-src-list="[scope.row.thumb]">
            </el-image>
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80"></el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
        <el-table-column prop="state" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.state === 1 ? 'success' : 'info'">
              {{ scope.row.state === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="showEditDialog(scope.row)">编辑</el-button>
            <el-button 
              size="mini" 
              :type="scope.row.state === 1 ? 'warning' : 'success'"
              @click="toggleState(scope.row)">
              {{ scope.row.state === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button size="mini" type="danger" @click="deleteFriendlyItem(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 添加/编辑友情链接对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="链接名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入链接名称"></el-input>
        </el-form-item>
        <el-form-item label="链接地址" prop="href">
          <el-input v-model="form.href" placeholder="请输入链接地址"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择链接类型">
            <el-option label="友情链接" value="1"></el-option>
            <el-option label="合作伙伴" value="2"></el-option>
            <el-option label="推荐网站" value="3"></el-option>
            <el-option label="其他" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="缩略图" prop="thumb">
          <el-input v-model="form.thumb" placeholder="请输入缩略图 URL"></el-input>
          <div class="logo-preview" v-if="form.thumb">
            <el-image 
              :src="form.thumb" 
              style="width: 40px; height: 40px; margin-top: 10px;" 
              fit="contain">
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="9999"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-radio-group v-model="form.state">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFriendlyList, createFriendly, updateFriendly, deleteFriendly, getFriendlyDetail } from '@/api/friendly'

export default {
  name: 'FriendlyList',
  data() {
    return {
      loading: false,
      submitLoading: false,
      // 筛选表单
      filterForm: {
        name: '',
        state: null
      },
      // 友情链接列表数据
      friendlyList: [],
      // 分页
      pagination: {
        page: 1,
        page_size: 10,
        total: 0
      },
      // 选中的行
      multipleSelection: [],
      // 对话框控制
      dialogVisible: false,
      dialogTitle: '添加友情链接',
      isEdit: false,
      editId: null,
      // 表单数据
      form: {
        name: '',
        href: '',
        type: '1',
        thumb: '',
        sort: 0,
        state: 1
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入链接名称', trigger: 'blur' },
          { max: 50, message: '链接名称不能超过50个字符', trigger: 'blur' }
        ],
        href: [
          { required: true, message: '请输入链接地址', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请输入类型', trigger: 'blur' }
        ],
        thumb: [
          { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ],
        state: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.fetchFriendlyList()
  },
  methods: {
    // 获取友情链接列表
    fetchFriendlyList() {
      this.loading = true
      const params = {
        page: this.pagination.page,
        page_size: this.pagination.page_size,
        ...this.filterForm
      }
      
      // 移除值为null或空字符串的参数
      Object.keys(params).forEach(key => {
        if (params[key] === null || params[key] === '') {
          delete params[key]
        }
      })

      getFriendlyList(params)
        .then(response => {
          this.friendlyList = response.items || []
          this.pagination.total = response.total || 0
        })
        .catch(error => {
          this.$message.error(`获取友情链接列表失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 搜索友情链接
    searchFriendly() {
      this.pagination.page = 1
      this.fetchFriendlyList()
    },
    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        name: '',
        state: null
      }
      this.searchFriendly()
    },
    // 批量操作
    handleBatchCommand(command) {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择友情链接')
        return
      }
      
      if (command === 'delete') {
        this.$confirm('确认删除选中的友情链接吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.loading = true
          const deletePromises = this.multipleSelection.map(item => deleteFriendly(item.id))
          Promise.all(deletePromises)
            .then(() => {
              this.$message.success('批量删除成功')
              this.fetchFriendlyList()
            })
            .catch(error => {
              this.$message.error(`批量删除失败: ${error.message}`)
            })
            .finally(() => {
              this.loading = false
            })
        }).catch(() => {
          // 取消删除
        })
      } else if (command === 'enable' || command === 'disable') {
        const state = command === 'enable' ? 1 : 0
        const actionText = command === 'enable' ? '启用' : '禁用'
        
        this.$confirm(`确认${actionText}选中的友情链接吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.loading = true
          const updatePromises = this.multipleSelection.map(item => updateFriendly(item.id, { state }))
          Promise.all(updatePromises)
            .then(() => {
              this.$message.success(`批量${actionText}成功`)
              this.fetchFriendlyList()
            })
            .catch(error => {
              this.$message.error(`批量${actionText}失败: ${error.message}`)
            })
            .finally(() => {
              this.loading = false
            })
        }).catch(() => {
          // 取消操作
        })
      }
    },
    // 选择行变化
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.page_size = val
      this.fetchFriendlyList()
    },
    // 页码变化
    handleCurrentChange(val) {
      this.pagination.page = val
      this.fetchFriendlyList()
    },
    // 切换友情链接状态
    toggleState(row) {
      const newState = row.state === 1 ? 0 : 1
      const actionText = newState === 1 ? '启用' : '禁用'
      
      this.loading = true
      updateFriendly(row.id, { state: newState })
        .then(() => {
          this.$message.success(`${actionText}成功`)
          row.state = newState // 直接更新本地数据，避免重新请求
        })
        .catch(error => {
          this.$message.error(`${actionText}失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 删除友情链接
    deleteFriendlyItem(row) {
      this.$confirm(`确认删除友情链接 "${row.name}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        deleteFriendly(row.id)
          .then(() => {
            this.$message.success('删除成功')
            this.fetchFriendlyList()
          })
          .catch(error => {
            this.$message.error(`删除失败: ${error.message}`)
          })
          .finally(() => {
            this.loading = false
          })
      }).catch(() => {
        // 取消删除
      })
    },
    // 显示添加对话框
    showCreateDialog() {
      this.dialogTitle = '添加友情链接'
      this.isEdit = false
      this.editId = null
      this.form = {
        name: '',
        href: '',
        type: '1',
        thumb: '',
        sort: 0,
        state: 1
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.form.clearValidate()
      })
    },
    // 显示编辑对话框
    showEditDialog(row) {
      this.dialogTitle = '编辑友情链接'
      this.isEdit = true
      this.editId = row.id
      
      // 加载友情链接详情
      this.loading = true
      getFriendlyDetail(row.id)
        .then(data => {
          this.form = {
            name: data.name,
            href: data.href,
            type: data.type || '',
            thumb: data.thumb || '',
            sort: data.sort || 0,
            state: data.state
          }
          this.dialogVisible = true
          this.$nextTick(() => {
            this.$refs.form.clearValidate()
          })
        })
        .catch(error => {
          this.$message.error(`获取详情失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (!valid) return
        
        this.submitLoading = true
        const request = this.isEdit
          ? updateFriendly(this.editId, this.form)
          : createFriendly(this.form)
        
        request
          .then(() => {
            this.$message.success(this.isEdit ? '更新成功' : '添加成功')
            this.dialogVisible = false
            this.fetchFriendlyList()
          })
          .catch(error => {
            this.$message.error(`${this.isEdit ? '更新' : '添加'}失败: ${error.message}`)
          })
          .finally(() => {
            this.submitLoading = false
          })
      })
    },
    // 格式化类型
    formatType(type) {
      const typeMap = {
        '1': '友情链接',
        '2': '合作伙伴',
        '3': '推荐网站',
        '4': '其他'
      }
      return typeMap[type] || type
    }
  }
}
</script>

<style scoped>
.friendly-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.friendly-table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-dropdown-link {
  color: #409EFF;
  cursor: pointer;
}

.logo-preview {
  display: flex;
  align-items: center;
  margin-top: 5px;
}
</style> 