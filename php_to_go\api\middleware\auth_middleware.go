package middleware

import (
	"log"
	"strings"

	"go-fiber-api/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
)

// AuthMiddleware 实现 JWT 认证和授权
func AuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 1. 从 Authorization 头获取 Token
		authHeader := c.Get("Authorization")
		if authHeader == "" {
			log.Println("AuthMiddleware: 缺少认证令牌")
			return utils.Unauthorized(c, "缺少认证令牌", nil)
		}

		// 检查是否为 Bearer Token
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			return utils.Unauthorized(c, "令牌格式无效，应为 Bearer <token>", nil)
		}
		tokenString := parts[1]

		// 2. 解析和验证 Token，只验证有效性
		claims, err := utils.ParseToken(tokenString)
		if err != nil {
			if err == jwt.ErrSignatureInvalid {
				return utils.Unauthorized(c, "令牌签名无效", err)
			}
			return utils.Unauthorized(c, "令牌无效或已过期", err)
		}

		// 验证通过
		log.Printf("AuthMiddleware: 用户 %s (ID: %d) 认证成功", claims.Username, claims.ID)
		
		// 4. 调用下一个处理器
		return c.Next()
	}
}
