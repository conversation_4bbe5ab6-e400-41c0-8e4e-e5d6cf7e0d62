<template>
  <div class="permission-list">
    <div class="page-header">
      <h1 class="page-title">权限管理</h1>
      <div class="page-actions">
        <el-button type="primary" @click="showCreateDialog">创建权限</el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card shadow="hover" class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="权限名称">
          <el-input
            v-model="filterForm.name"
            placeholder="请输入权限名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="权限标识">
          <el-input
            v-model="filterForm.slug"
            placeholder="请输入权限标识"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="searchPermissions"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" @click="resetFilter"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 权限树形列表 -->
    <el-card shadow="hover" class="permission-table-container">
      <div slot="header">
        <div class="table-header">
          <span>权限列表</span>
          <div class="table-actions">
            <el-button
              v-if="multipleSelection.length > 0"
              type="danger"
              size="small"
              @click="batchDeletePermissions"
            >
              批量删除 ({{ multipleSelection.length }})
            </el-button>
          </div>
        </div>
      </div>
      <el-table
        :data="permissionTreeData"
        stripe
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="defaultExpandAll"
        ref="permissionTable"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="权限名称" min-width="200">
          <template slot-scope="scope">
            <span :style="{ paddingLeft: (scope.row.level || 0) * 20 + 'px' }">
              <i
                v-if="scope.row.children && scope.row.children.length"
                class="el-icon-folder"
                style="color: #409eff; margin-right: 5px"
              ></i>
              <i
                v-else
                class="el-icon-document"
                style="color: #67c23a; margin-right: 5px"
              ></i>
              {{ scope.row.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="slug"
          label="权限标识"
          min-width="150"
        ></el-table-column>
        <el-table-column prop="http_method" label="HTTP方法" min-width="120">
          <template slot-scope="scope">
            <el-tag
              v-if="scope.row.http_method"
              :type="getMethodTagType(scope.row.http_method)"
              size="small"
            >
              {{ scope.row.http_method }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="http_path"
          label="HTTP路径"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="order"
          label="排序"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" width="340" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="viewPermissionDetail(scope.row)"
              >详情</el-button
            >
            <el-button
              size="mini"
              type="success"
              @click="showUpdateDialog(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="mini"
              type="info"
              @click="showCreateChildDialog(scope.row)"
              >添加子权限</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="deletePermissionItem(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 权限详情对话框 -->
    <el-dialog
      title="权限详情"
      :visible.sync="detailDialogVisible"
      width="600px"
    >
      <div v-loading="detailLoading">
        <div v-if="currentPermission">
          <el-descriptions border>
            <el-descriptions-item label="ID">{{
              currentPermission.id
            }}</el-descriptions-item>
            <el-descriptions-item label="权限名称">{{
              currentPermission.name
            }}</el-descriptions-item>
            <el-descriptions-item label="权限标识">{{
              currentPermission.slug
            }}</el-descriptions-item>
            <el-descriptions-item label="HTTP方法">{{
              currentPermission.http_method || "-"
            }}</el-descriptions-item>
            <el-descriptions-item label="HTTP路径" :span="3">{{
              currentPermission.http_path || "-"
            }}</el-descriptions-item>
          </el-descriptions>

          <div
            class="detail-section"
            v-if="currentPermission.menus && currentPermission.menus.length"
          >
            <h4>关联菜单</h4>
            <el-tag
              v-for="menu in currentPermission.menus"
              :key="menu.id"
              type="primary"
              class="menu-tag"
            >
              {{ menu.title }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 创建/编辑权限对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '创建权限' : '编辑权限'"
      :visible.sync="formDialogVisible"
      width="600px"
    >
      <el-form
        :model="permissionForm"
        :rules="permissionFormRules"
        ref="permissionForm"
        label-width="120px"
        v-loading="formLoading"
      >
        <el-form-item label="权限名称" prop="name">
          <el-input
            v-model="permissionForm.name"
            placeholder="请输入权限名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="权限标识" prop="slug">
          <el-input
            v-model="permissionForm.slug"
            placeholder="请输入权限标识"
          ></el-input>
        </el-form-item>
        <el-form-item label="HTTP方法" prop="http_method">
          <el-select
            v-model="permissionForm.http_method"
            placeholder="请选择HTTP方法"
            style="width: 100%"
          >
            <el-option label="GET" value="GET"></el-option>
            <el-option label="POST" value="POST"></el-option>
            <el-option label="PUT" value="PUT"></el-option>
            <el-option label="DELETE" value="DELETE"></el-option>
            <el-option label="PATCH" value="PATCH"></el-option>
            <el-option label="HEAD" value="HEAD"></el-option>
            <el-option label="OPTIONS" value="OPTIONS"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="HTTP路径" prop="http_path">
          <el-input
            v-model="permissionForm.http_path"
            placeholder="请输入HTTP路径"
          ></el-input>
        </el-form-item>
        <el-form-item label="父权限" prop="parent_id">
          <el-select
            v-model="permissionForm.parent_id"
            placeholder="请选择父权限"
            style="width: 100%"
            clearable
          >
            <el-option label="无父权限" :value="0"></el-option>
            <el-option
              v-for="item in parentPermissionOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              :disabled="item.id === permissionForm.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number
            v-model="permissionForm.order"
            :min="0"
            :max="9999"
            placeholder="排序值"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="关联菜单" prop="menu_ids">
          <el-select
            v-model="permissionForm.menu_ids"
            multiple
            placeholder="请选择菜单"
            style="width: 100%"
          >
            <el-option
              v-for="item in menuOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="submitPermissionForm"
          :loading="formLoading"
          >确认</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPermissionList,
  getPermissionTree,
  getPermissionDetail,
  createPermission,
  updatePermission,
  deletePermission,
  batchDeletePermissions,
} from "@/api/permission";
import { getMenuList } from "@/api/menu";

export default {
  name: "PermissionList",
  data() {
    return {
      loading: false,
      detailLoading: false,
      formLoading: false,
      // 筛选表单
      filterForm: {
        name: "",
        parent_id: "",
      },
      // 权限列表数据
      permissionList: [],
      // 权限树形数据
      permissionTreeData: [],
      // 分页
      pagination: {
        page: 1,
        page_size: 10,
        total: 0,
      },
      // 选中的行
      multipleSelection: [],
      // 详情对话框
      detailDialogVisible: false,
      currentPermission: null,
      // 表单对话框
      formDialogVisible: false,
      // 对话框类型：create-创建，update-更新
      dialogType: "create",
      // 权限表单
      permissionForm: {
        name: "",
        slug: "",
        http_method: "",
        http_path: "",
        menu_ids: [],
        parent_id: 0,
        order: 0,
      },
      // 表单验证规则
      permissionFormRules: {
        name: [{ required: true, message: "请输入权限名称", trigger: "blur" }],
        slug: [{ required: true, message: "请输入权限标识", trigger: "blur" }],
      },
      // 菜单选项
      menuOptions: [],
      // 父权限选项
      parentPermissionOptions: [],
      // 是否默认展开所有节点
      defaultExpandAll: false,
    };
  },
  created() {
    this.fetchPermissionTree();
    this.fetchMenuOptions();
    this.fetchParentPermissionOptions();
  },
  methods: {
    // 获取权限树形数据
    fetchPermissionTree() {
      this.loading = true;
      const params = {
        ...this.filterForm,
      };

      // 移除值为null或空字符串的参数
      Object.keys(params).forEach((key) => {
        if (params[key] === null || params[key] === "") {
          delete params[key];
        }
      });

      getPermissionTree(params)
        .then((response) => {
          this.permissionTreeData = response || [];
        })
        .catch((error) => {
          this.$message.error(`获取权限树失败: ${error.message}`);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取权限列表（用于父权限选择）
    fetchParentPermissionOptions() {
      getPermissionList()
        .then((response) => {
          // 处理后端返回的数据
          if (Array.isArray(response)) {
            this.parentPermissionOptions = response;
          } else {
            this.parentPermissionOptions = response.items || [];
          }
        })
        .catch((error) => {
          console.error("获取父权限选项失败:", error);
        });
    },
    // 获取菜单选项
    fetchMenuOptions() {
      getMenuList()
        .then((response) => {
          this.menuOptions = response || [];
        })
        .catch((error) => {
          this.$message.error(`获取菜单列表失败: ${error.message}`);
        });
    },
    // 显示创建权限对话框
    showCreateDialog() {
      this.dialogType = "create";
      this.resetPermissionForm();
      this.formDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.permissionForm.clearValidate();
      });
    },
    // 显示更新权限对话框
    showUpdateDialog(row) {
      this.dialogType = "update";
      this.resetPermissionForm();
      this.formLoading = true;

      getPermissionDetail(row.id)
        .then((data) => {
          this.permissionForm = {
            id: data.id,
            name: data.name || "",
            slug: data.slug || "",
            http_method: data.http_method || "",
            http_path: data.http_path || "",
            menu_ids: data.menu_ids || [],
            parent_id: data.parent_id || 0,
            order: data.order || 0,
          };
        })
        .catch((error) => {
          this.$message.error(`获取权限详情失败: ${error.message}`);
          this.formDialogVisible = false;
        })
        .finally(() => {
          this.formLoading = false;
          this.formDialogVisible = true;
        });
    },
    // 重置权限表单
    resetPermissionForm() {
      this.permissionForm = {
        name: "",
        slug: "",
        http_method: "",
        http_path: "",
        menu_ids: [],
        parent_id: 0,
        order: 0,
      };
      // 如果表单已经被创建，则重置验证
      if (this.$refs.permissionForm) {
        this.$refs.permissionForm.resetFields();
      }
    },
    // 提交权限表单
    submitPermissionForm() {
      this.$refs.permissionForm.validate((valid) => {
        if (!valid) {
          return false;
        }

        this.formLoading = true;

        // 构建API需要的数据结构
        let formData = { ...this.permissionForm };

        // 根据对话框类型决定是创建还是更新
        let apiCall =
          this.dialogType === "create"
            ? createPermission(formData)
            : updatePermission(this.permissionForm.id, formData);

        apiCall
          .then(() => {
            this.$message.success(
              this.dialogType === "create" ? "创建成功" : "更新成功"
            );
            this.formDialogVisible = false;
            this.fetchPermissionTree();
          })
          .catch((error) => {
            this.$message.error(
              `${this.dialogType === "create" ? "创建" : "更新"}失败: ${
                error.message
              }`
            );
          })
          .finally(() => {
            this.formLoading = false;
          });
      });
    },
    // 搜索权限
    searchPermissions() {
      this.fetchPermissionTree();
    },
    // 重置筛选条件
    resetFilter() {
      this.filterForm = {
        name: "",
        parent_id: "",
      };
      this.searchPermissions();
    },
    // 选择行变化
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    // 递归设置树节点的展开状态
    setTreeNodesExpandedState(data, isExpanded) {
      if (!data || !data.length) return;

      // 获取表格引用
      const table = this.$refs.permissionTable;
      if (!table || !table.store || !table.store.nodesMap) {
        return;
      }

      data.forEach((item) => {
        // 检查节点ID是否存在并且存在于nodesMap中
        if (item && item.id !== undefined && table.store.nodesMap[item.id]) {
          // 设置节点的展开状态
          table.store.nodesMap[item.id].expanded = isExpanded;
        }

        // 递归处理子节点
        if (item.children && item.children.length > 0) {
          this.setTreeNodesExpandedState(item.children, isExpanded);
        }
      });
    },
    // 获取HTTP方法标签类型
    getMethodTagType(method) {
      const typeMap = {
        GET: "success",
        POST: "primary",
        PUT: "warning",
        DELETE: "danger",
        PATCH: "info",
        HEAD: "",
        OPTIONS: "",
      };
      return typeMap[method] || "info";
    },
    // 显示创建子权限对话框
    showCreateChildDialog(row) {
      this.dialogType = "create";
      this.resetPermissionForm();
      this.permissionForm.parent_id = row.id;
      this.formDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.permissionForm.clearValidate();
      });
    },
    // 查看权限详情
    viewPermissionDetail(row) {
      this.detailDialogVisible = true;
      this.detailLoading = true;
      this.currentPermission = null;

      getPermissionDetail(row.id)
        .then((data) => {
          this.currentPermission = data;
        })
        .catch((error) => {
          this.$message.error(`获取权限详情失败: ${error.message}`);
        })
        .finally(() => {
          this.detailLoading = false;
        });
    },
    // 删除权限
    deletePermissionItem(row) {
      this.$confirm(`确认删除权限 "${row.name}" 吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          deletePermission(row.id)
            .then(() => {
              this.$message.success("删除成功");
              this.fetchPermissionTree();
            })
            .catch((error) => {
              this.$message.error(`删除失败: ${error.message}`);
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          // 取消删除
        });
    },
    // 批量删除权限
    batchDeletePermissions() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning("请选择要删除的权限");
        return;
      }

      const ids = this.multipleSelection.map((item) => item.id);

      this.$confirm(`确认批量删除选中的 ${ids.length} 个权限吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.loading = true;
          batchDeletePermissions(ids)
            .then(() => {
              this.$message.success("批量删除成功");
              this.fetchPermissionTree();
            })
            .catch((error) => {
              this.$message.error(`批量删除失败: ${error.message}`);
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch(() => {
          // 取消删除
        });
    },
  },
};
</script>

<style scoped>
.permission-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.permission-table-container {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.detail-section {
  margin-top: 20px;
}

.menu-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
