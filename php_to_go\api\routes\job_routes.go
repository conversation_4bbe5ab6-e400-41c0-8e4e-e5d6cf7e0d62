package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"
	"go-fiber-api/utils"
	"github.com/gofiber/fiber/v2"
)

// SetupJobRoutes 设置任务相关路由
func SetupJobRoutes(router fiber.Router) {
	// 任务路由组
	job := router.Group("/job")
	
	// 添加认证中间件
	job.Use(utils.RequireAuthentication)
	
	// 应用认证中间件
	job.Use(middleware.AuthMiddleware())
	
	// 获取任务列表
	job.Get("/index", middleware.RequirePermission("job.view"), handlers.GetJobs)
	
	// 获取单个任务详情
	job.Get("/index/:id", middleware.RequirePermission("job.view"), handlers.GetJob)
	
	// 创建任务
	job.Post("/index", middleware.RequirePermission("job.create"), handlers.CreateJob)
	
	// 更新任务
	job.Put("/index/:id", middleware.RequirePermission("job.edit"), handlers.UpdateJob)
	
	// 删除任务
	job.Delete("/index/:id", middleware.RequirePermission("job.delete"), handlers.DeleteJob)
	
	// 清空所有任务
	job.Delete("/clear-all", middleware.RequirePermission("job.delete"), handlers.ClearAllJobs)
} 