<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <span>{{ title }}</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="上级菜单" prop="parent_id">
          <el-select v-model="form.parent_id" placeholder="请选择上级菜单" style="width: 100%">
            <el-option label="顶级菜单" :value="0" />
            <el-option 
              v-for="item in menuOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="菜单标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入菜单标题" />
        </el-form-item>

        <el-form-item label="菜单图标" prop="icon">
          <el-input v-model="form.icon" placeholder="请选择菜单图标">
            <template slot="prepend">
              <i :class="form.icon || 'el-icon-menu'" style="width: 20px;" />
            </template>
            <el-button slot="append" @click="showIconSelector = true">选择图标</el-button>
          </el-input>
        </el-form-item>

        <el-form-item label="菜单排序" prop="order">
          <el-input-number v-model="form.order" :min="0" :max="9999" controls-position="right" />
        </el-form-item>

        <el-form-item label="菜单路径" prop="uri">
          <el-input v-model="form.uri" placeholder="请输入菜单路径" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图标选择器对话框 -->
    <el-dialog
      title="选择图标"
      :visible.sync="showIconSelector"
      width="800px"
      append-to-body
    >
      <div class="icon-container">
        <div
          v-for="(icon, index) in iconList"
          :key="index"
          class="icon-item"
          @click="selectIcon(icon)"
        >
          <i :class="icon" />
          <span>{{ icon }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createMenu, getMenuList } from '@/api/menu'

export default {
  name: 'MenuCreate',
  data() {
    return {
      title: '添加菜单',
      // 表单参数
      form: {
        parent_id: 0,
        title: '',
        icon: '',
        order: 0,
        uri: '',
        role_ids: [],
        permission_ids: []
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: '菜单标题不能为空', trigger: 'blur' }
        ]
      },
      // 菜单选项
      menuOptions: [],
      // 是否显示图标选择器
      showIconSelector: false,
      // 图标列表
      iconList: [
        'el-icon-menu',
        'el-icon-s-home',
        'el-icon-s-custom',
        'el-icon-s-grid',
        'el-icon-s-tools',
        'el-icon-s-marketing',
        'el-icon-s-platform',
        'el-icon-s-operation',
        'el-icon-s-data',
        'el-icon-s-cooperation',
        'el-icon-s-order',
        'el-icon-s-release',
        'el-icon-s-ticket',
        'el-icon-s-management',
        'el-icon-s-open',
        'el-icon-s-shop',
        'el-icon-s-help',
        'el-icon-picture',
        'el-icon-camera',
        'el-icon-video-camera',
        'el-icon-document',
        'el-icon-setting',
        'el-icon-user',
        'el-icon-phone',
        'el-icon-more'
      ]
    }
  },
  created() {
    this.getMenuOptions()
    
    // 如果有父菜单ID，则设置父菜单
    const parentId = this.$route.query.parentId
    if (parentId) {
      this.form.parent_id = parseInt(parentId)
    }
  },
  methods: {
    /** 获取菜单选项 */
    getMenuOptions() {
      getMenuList().then(response => {
        this.menuOptions = response || []
      })
    },
    /** 表单提交 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          createMenu(this.form).then(() => {
            this.$message.success('新增成功')
            this.goBack()
          }).catch(error => {
            console.error('创建菜单失败:', error)
          })
        }
      })
    },
    /** 取消按钮 */
    cancel() {
      this.goBack()
    },
    /** 返回按钮操作 */
    goBack() {
      this.$router.push({ path: '/menu' })
    },
    /** 选择图标 */
    selectIcon(icon) {
      this.form.icon = icon
      this.showIconSelector = false
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.icon-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.icon-item {
  width: 16%;
  margin-bottom: 15px;
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #eee;
  border-radius: 4px;
  transition: all 0.3s;
}
.icon-item:hover {
  background-color: #f5f7fa;
  color: #409EFF;
}
.icon-item i {
  font-size: 24px;
  margin-bottom: 10px;
}
.icon-item span {
  font-size: 12px;
  word-break: break-all;
  text-align: center;
  padding: 0 5px;
}
</style> 