---
description: 数据库结构，数据库
globs: 
alwaysApply: false
---
# Go Fiber API 数据库结构

本文档描述了 [go_fiber.sql](mdc:database/go_fiber.sql) 数据库的表结构和关系。此数据库主要用于管理后台系统，包含权限管理、角色管理、用户管理等功能。

## 管理员相关表

### 用户表 (admin_users)
- 存储管理员用户信息
- 主要字段：id, username, password, name, email, avatar, alias, referee, notify, remember_token, lasted_ipaddress, lasted_at, state
- 包含软删除功能 (deleted_at)
- 记录用户最后登录IP和时间

### 角色表 (admin_roles)
- 定义系统中的各种角色
- 主要字段：id, name, slug
- slug 为角色的唯一标识符

### 权限表 (admin_permissions)
- 定义系统中的权限项
- 主要字段：id, name, slug, http_method, http_path, order, parent_id
- 支持按HTTP方法和路径定义权限
- 支持权限层级结构

### 菜单表 (admin_menu)
- 定义系统的导航菜单
- 主要字段：id, parent_id, order, title, icon, uri
- 支持通过 parent_id 构建菜单树

## 关联表

### 角色-用户关联 (admin_role_users)
- 定义用户与角色的多对多关系
- 字段：role_id, user_id

### 角色-权限关联 (admin_role_permissions)
- 定义角色与权限的多对多关系
- 字段：role_id, permission_id

### 角色-菜单关联 (admin_role_menu)
- 定义角色可访问的菜单项
- 字段：role_id, menu_id

### 权限-菜单关联 (admin_permission_menu)
- 定义权限与菜单的关联
- 字段：permission_id, menu_id

## 内容表

### 公告表 (articles)
- 存储系统公告信息
- 主要字段：id, title, description, status, author, created_by, updated_by
- 包含软删除功能 (deleted_at)

### 域名表 (domain)
- 管理系统域名配置
- 主要字段：id, domain, type, state
- type 区分过滤域名(10)和切换域名(20)

### 友情链接表 (friendly)
- 管理系统友情链接
- 主要字段：id, name, href, thumb, type, state
- 包含软删除功能 (deleted_at)

### 邀请码表 (invitation)
- 管理系统邀请码
- 主要字段：id, code, user_id, used_at, state
- 包含软删除功能 (deleted_at)

## 项目相关表

### 项目表 (projects)
- 存储项目基本信息
- 主要字段：id, title, unique_key, description, code, user_id, module_id, module_ext_param, is_new_record, state
- 包含软删除功能 (deleted_at)

### 项目内容表 (projects_content)
- 存储项目具体内容
- 主要字段：cid, project_id, user_id, content, server, domain, hash, state
- 包含软删除功能 (deleted_at)

### 模块表 (modules)
- 存储系统模块信息
- 主要字段：id, title, description, keys, setkeys, code, level, user_id, is_share, state
- 包含软删除功能 (deleted_at)

## 系统表

### 操作日志 (admin_operation_log)
- 记录管理员操作日志
- 主要字段：id, user_id, path, method, ip, input

### 失败任务 (failed_jobs)
- 记录系统中失败的异步任务
- 主要字段：id, connection, queue, payload, exception, failed_at

### 队列任务 (jobs)
- 存储队列任务信息
- 主要字段：id, queue, payload, attempts, reserved_at, available_at, created_at

## 表关系图

```
admin_users ──┬── admin_role_users ─── admin_roles
              │
              └── admin_operation_log

admin_roles ──┬── admin_role_permissions ─── admin_permissions
              │
              └── admin_role_menu ─── admin_menu

admin_permissions ─── admin_permission_menu ─── admin_menu

projects ─── projects_content

research ─── research_job ─── research_task
```

## 数据库设计特点

1. 采用RBAC(基于角色的访问控制)模型
2. 使用软删除机制(deleted_at)(admin_menu不需要软删除)
3. 记录数据创建和更新时间(created_at, updated_at)
4. 使用索引优化查询性能
5. 使用utf8mb4字符集和utf8mb4_unicode_ci排序规则

