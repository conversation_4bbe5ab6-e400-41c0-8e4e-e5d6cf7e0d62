import request from './index'

/**
 * 获取XSS模板列表
 * @param {Object} params - 查询参数
 * @param {number} [params.project_id] - 项目ID
 * @param {string} [params.title] - 模板标题关键词
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=10] - 每页数量
 * @returns {Promise}
 */
export function getXssTemplateList(params) {
  return request({
    url: '/admin/xss-templates',
    method: 'get',
    params
  })
}

/**
 * 获取XSS模板详情
 * @param {number} id - 模板ID
 * @returns {Promise}
 */
export function getXssTemplate(id) {
  return request({
    url: `/admin/xss-templates/${id}`,
    method: 'get'
  })
}

/**
 * 创建XSS模板
 * @param {Object} data - 模板数据
 * @param {number} data.project_id - 项目ID
 * @param {string} data.title - 模板标题
 * @param {string} data.content - 模板内容
 * @returns {Promise}
 */
export function createXssTemplate(data) {
  return request({
    url: '/admin/xss-templates',
    method: 'post',
    data
  })
}

/**
 * 更新XSS模板
 * @param {number} id - 模板ID
 * @param {Object} data - 更新数据
 * @param {string} [data.title] - 模板标题
 * @param {string} [data.content] - 模板内容
 * @returns {Promise}
 */
export function updateXssTemplate(id, data) {
  return request({
    url: `/admin/xss-templates/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除XSS模板
 * @param {number} id - 模板ID
 * @returns {Promise}
 */
export function deleteXssTemplate(id) {
  return request({
    url: `/admin/xss-templates/${id}`,
    method: 'delete'
  })
}
