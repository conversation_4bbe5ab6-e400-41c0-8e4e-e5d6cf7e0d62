<template>
  <div class="project-detail">
    <div class="page-header">
      <h1 class="page-title">项目详情</h1>
      <div class="page-actions">
        <el-button type="warning" @click="$router.push(`/project/create?id=${projectId}`)">
          <i class="el-icon-edit"></i> 编辑项目
        </el-button>
        <el-button @click="$router.push('/project')">
          <i class="el-icon-back"></i> 返回列表
        </el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="基本信息" name="info">
        <el-card v-loading="loading" class="project-card">
          <template v-if="project">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="项目名称" :span="2">
                <strong>{{ project.title }}</strong>
              </el-descriptions-item>
              <el-descriptions-item label="项目ID">{{ project.id }}</el-descriptions-item>
              <el-descriptions-item label="唯一标识">{{ project.unique_key }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ project.created_at }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ project.updated_at }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="project.state === 1 ? 'success' : 'info'">
                  {{ project.state === 1 ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="模块ID">{{ project.module_id }}</el-descriptions-item>
              <el-descriptions-item label="项目描述" :span="2">
                {{ project.description || '无描述' }}
              </el-descriptions-item>
              <el-descriptions-item label="模块扩展参数" :span="2">
                {{ project.module_ext_param || '无' }}
              </el-descriptions-item>
            </el-descriptions>
          </template>
          <div v-else-if="!loading" class="empty-state">
            <i class="el-icon-warning-outline"></i>
            <p>项目不存在或已被删除</p>
            <el-button type="primary" @click="$router.push('/project')">返回项目列表</el-button>
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="项目内容" name="content">
        <div class="content-header">
          <el-button type="primary" @click="createContent">
            <i class="el-icon-plus"></i> 创建项目内容
          </el-button>
        </div>

        <!-- 项目内容列表 -->
        <el-table :data="contents" stripe style="width: 100%" @selection-change="handleSelectionChange"
          v-loading="contentLoading">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="cid" label="ID" width="80"></el-table-column>
          <el-table-column prop="domain" label="域名" min-width="180"></el-table-column>
          <el-table-column prop="hash" label="哈希值" width="150"></el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
          <el-table-column prop="state" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.state === 1 ? 'success' : 'info'">
                {{ scope.row.state === 1 ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="viewContent(scope.row)">查看</el-button>
              <el-button size="mini" type="warning" @click="editContent(scope.row)">编辑</el-button>
              <el-button size="mini" type="danger" @click="deleteContent(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination @size-change="handleContentSizeChange" @current-change="handleContentCurrentChange"
            :current-page="contentPagination.page" :page-sizes="[10, 20, 50, 100]"
            :page-size="contentPagination.page_size" layout="total, sizes, prev, pager, next, jumper"
            :total="contentPagination.total">
          </el-pagination>
        </div>

        <!-- 批量操作 -->
        <div class="batch-actions" v-if="multipleSelection.length > 0">
          <el-button type="danger" @click="batchDeleteContents">
            批量删除选中内容
          </el-button>
        </div>
      </el-tab-pane>

      <!-- 项目代码标签页 -->
      <el-tab-pane label="项目代码" name="code">
        <el-card v-loading="templateLoading" class="project-card">
          <div class="code-selection-header">
            <div class="selection-row">
              <div class="selection-item">
                <span class="selection-label">选择备用域名</span>
                <el-select v-model="selectedDomain" placeholder="请选择备用域名">
                  <el-option v-for="item in domainOptions" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </div>
              <div class="selection-item">
                <span class="selection-label">协议</span>
                <el-select v-model="selectedProtocol" placeholder="请选择协议">
                  <el-option label="HTTP://" value="HTTP://"></el-option>
                  <el-option label="HTTPS://" value="HTTPS://"></el-option>
                  <el-option label="//" value="//"></el-option>
                </el-select>
              </div>
            </div>
          </div>

          <!-- 使用XssTemplate组件 -->
          <xss-template v-if="project" :domain="selectedDomain" :protocol="selectedProtocol"
            :unique-key="project.unique_key"
            :template-content="templateContent && templateContent.processed"></xss-template>

          <div v-else-if="!templateLoading" class="empty-state">
            <i class="el-icon-warning-outline"></i>
            <p>未找到项目模板代码</p>
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 自定义XSS模板标签页 -->
      <el-tab-pane label="自定义XSS模板" name="custom-xss">
        <el-card class="project-card">
          <self-xss-template v-if="project" :project-id="project.id"></self-xss-template>
          <div v-else class="empty-state">
            <i class="el-icon-warning-outline"></i>
            <p>项目信息加载中...</p>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 删除项目确认对话框 -->
    <el-dialog title="确认删除" :visible.sync="deleteDialogVisible" width="30%">
      <span>确定要删除项目 "{{ project?.title }}" 吗？此操作不可恢复！</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete" :loading="deleteLoading">确定删除</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getProject, deleteProject } from '@/api/project'
import { getProjectContentList, deleteProjectContentById } from '@/api/projectContent'
import { getDomainList } from '@/api/domain'
import XssTemplate from '@/components/XssTemplate.vue'
import SelfXssTemplate from '@/components/SelfXssTemplate.vue'

export default {
  name: 'ProjectDetail',
  components: {
    XssTemplate,
    SelfXssTemplate
  },
  data() {
    return {
      activeTab: 'info',
      projectId: this.$route.params.id,
      project: null,
      loading: false,
      deleteDialogVisible: false,
      deleteLoading: false,

      // 项目内容相关
      contentLoading: false,
      contents: [],
      contentPagination: {
        page: 1,
        page_size: 10,
        total: 0
      },
      multipleSelection: [],

      // 项目代码相关
      templateLoading: false,
      selectedDomain: '',
      selectedProtocol: 'HTTP://',
      domainOptions: [],
      templateContent: null,
      templateId: 1 // 默认模板ID，可以根据项目信息动态设置
    }
  },
  watch: {
    activeTab(newTab) {
      if (newTab === 'content' && this.contents.length === 0) {
        this.fetchContents()
      }
    },
    selectedDomain() {
      this.updateTemplateContent()
    },
    selectedProtocol() {
      this.updateTemplateContent()
    }
  },
  methods: {
    fetchDomainList() {
      getDomainList().then(response => {
        this.domainOptions = response.items.map(item => ({ label: item.domain, value: item.domain }))
        this.selectedDomain = this.domainOptions[0].value
      })
    },
    fetchProjectDetail() {
      this.loading = true
      getProject(this.projectId)
        .then(data => {
          this.project = data
          // 可以根据项目信息设置模板ID
          if (data.module_id) {
            this.templateId = data.module_id
          }
        })
        .catch(error => {
          this.$message.error(`获取项目详情失败: ${error.message}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    confirmDelete() {
      this.deleteLoading = true
      deleteProject(this.projectId)
        .then(() => {
          this.$message.success('项目删除成功')
          this.deleteDialogVisible = false
          this.$router.push('/project')
        })
        .catch(error => {
          this.$message.error(`删除失败: ${error.message}`)
        })
        .finally(() => {
          this.deleteLoading = false
        })
    },

    // 项目内容相关方法
    fetchContents() {
      this.contentLoading = true
      const params = {
        page: this.contentPagination.page,
        page_size: this.contentPagination.page_size,
        project_id: this.projectId
      }

      getProjectContentList(params)
        .then(response => {
          this.contents = response.items || []
          this.contentPagination.total = response.total || 0
        })
        .catch(error => {
          this.$message.error(`获取项目内容失败: ${error.message}`)
        })
        .finally(() => {
          this.contentLoading = false
        })
    },
    createContent() {
      this.$router.push({
        path: '/project-content/create',
        query: { project_id: this.projectId }
      })
    },
    viewContent(row) {
      this.$router.push(`/project-content/${row.cid}`)
    },
    editContent(row) {
      this.$router.push(`/project-content/edit/${row.cid}`)
    },
    deleteContent(row) {
      this.$confirm(`确认删除ID为 "${row.cid}" 的项目内容吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.contentLoading = true
        deleteProjectContentById(row.cid)
          .then(() => {
            this.$message.success('删除成功')
            this.fetchContents()
          })
          .catch(error => {
            this.$message.error(`删除失败: ${error.message}`)
          })
          .finally(() => {
            this.contentLoading = false
          })
      }).catch(() => {
        // 取消删除
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleContentSizeChange(val) {
      this.contentPagination.page_size = val
      this.fetchContents()
    },
    handleContentCurrentChange(val) {
      this.contentPagination.page = val
      this.fetchContents()
    },
    batchDeleteContents() {
      if (this.multipleSelection.length === 0) return

      this.$confirm('确认删除选中的项目内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.contentLoading = true
        const deletePromises = this.multipleSelection.map(item => deleteProjectContentById(item.cid))
        Promise.all(deletePromises)
          .then(() => {
            this.$message.success('批量删除成功')
            this.fetchContents()
          })
          .catch(error => {
            this.$message.error(`批量删除失败: ${error.message}`)
          })
          .finally(() => {
            this.contentLoading = false
          })
      }).catch(() => {
        // 取消删除
      })
    },
    updateTemplateContent() {
      if (this.project && this.templateId) {
        this.fetchTemplateContent();
      }
    },
    copyCode(code) {
      navigator.clipboard.writeText(code).then(() => {
        this.$message.success('代码已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    }
  },
  created() {
    this.fetchProjectDetail()
    // 如果初始就是内容标签，加载内容数据
    if (this.activeTab === 'content') {
      this.fetchContents()
    } else if (this.activeTab === 'code') {
      this.fetchTemplateContent()
    }
    this.fetchDomainList()
  }
}
</script>

<style scoped>
@import './Detail.scss';
</style>
